﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Reflection;

namespace Library.CommonHelpers;

public static class DataTableToObjectModelConverter<T> where T : new()
{
    public static List<T> Convert(DataTable dataTable)
    {
        var objects = new List<T>();

        foreach (DataRow row in dataTable.Rows)
        {
            var obj = new T();

            foreach (DataColumn col in dataTable.Columns)
            {
                var prop = obj.GetType().GetProperty(col.ColumnName, BindingFlags.Public | BindingFlags.Instance);
                if (prop != null && row[col] != DBNull.Value)
                {
                    var propType = Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType;

                    if (propType.IsEnum)
                    {
                        prop.SetValue(obj, Enum.ToObject(propType, row[col]));
                    }
                    else
                    {
                        // use this if conversion fails for nullable types - https://stackoverflow.com/questions/3531318/convert-changetype-fails-on-nullable-types
                        prop.SetValue(obj, System.Convert.ChangeType(row[col], propType), null);
                    }
                }
            }

            objects.Add(obj);
        }

        return objects;
    }
}
