﻿using System;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Flurl.Http;
using Polly;
using Polly.Retry;

namespace Library.ResilientHttpClient;

public class FAResilientHttpClient
{
    private static readonly int[] httpStatusCodesWorthRetrying =
    {
        (int)HttpStatusCode.RequestTimeout, // 408
        (int)HttpStatusCode.BadGateway, // 502
        (int)HttpStatusCode.ServiceUnavailable, // 503
        (int)HttpStatusCode.GatewayTimeout // 504
    };

    private AsyncRetryPolicy BuildRetryPolicy()
    {
        var retryPolicy = Policy
            .Handle<FlurlHttpException>(IsTransientError)
            .WaitAndRetryAsync(3, retryAttempt =>
            {
                var nextAttemptIn = TimeSpan.FromSeconds(Math.Pow(2, retryAttempt));
                Console.WriteLine(
                    $"Retry attempt {retryAttempt} to make request. Next try on {nextAttemptIn.TotalSeconds} seconds.");
                return nextAttemptIn;
            });

        return retryPolicy;
    }

    private bool IsTransientError(FlurlHttpException exception)
    {
        return exception.StatusCode.HasValue && httpStatusCodesWorthRetrying.Contains(exception.StatusCode.Value);
    }

    public async Task<T> GetJsonAsync<T>(string url, string auth, object headers = null)
    {
        var policy = BuildRetryPolicy();
        headers ??= new { Accept = "application/json", User_Agent = "Flurl" };
        return await policy.ExecuteAsync(() => url
            .WithHeaders(headers)
            .WithOAuthBearerToken(auth)
            .GetJsonAsync<T>()).ConfigureAwait(false);
    }

    public async Task<IFlurlResponse> GetAsync(string url, string auth, object headers = null)
    {
        var policy = BuildRetryPolicy();
        headers ??= new { Accept = "application/json", User_Agent = "Flurl" };
        return await policy.ExecuteAsync(() => url
            .WithHeaders(headers)
            .AllowAnyHttpStatus()
            .WithOAuthBearerToken(auth).GetAsync()).ConfigureAwait(false);
    }

    public async Task<string> PostJsonAsync(string url, string auth, object data, object headers = null)
    {
        var policy = BuildRetryPolicy();
        headers ??= new { Accept = "application/json", User_Agent = "Flurl" };
        return await policy.ExecuteAsync(() => url
            .WithHeaders(headers)
            .WithOAuthBearerToken(auth)
            .PostJsonAsync(data)
            .ReceiveString()).ConfigureAwait(false);
    }

    public async Task<Tvalue> PostJsonAsync<Tvalue>(string url, string auth, object data, object headers = null)
    {
        var policy = BuildRetryPolicy();
        headers ??= new { Accept = "application/json", User_Agent = "Flurl" };
        return await policy.ExecuteAsync(() => url
            .WithHeaders(headers)
            .WithOAuthBearerToken(auth)
            .PostJsonAsync(data)
            .ReceiveJson<Tvalue>()).ConfigureAwait(false);
    }
}
