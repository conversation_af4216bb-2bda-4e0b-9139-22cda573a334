﻿using FAEngage.Core;
using FAEngage.Core.MasterRepositories;
using FAEngage.Core.Models;
using FAEngage.Core.Models.MasterDbModels;
using FAEngage.DbStorage.DbContexts;
using Microsoft.EntityFrameworkCore;

namespace FAEngage.DbStorage.MasterRepositories;

public class PositionCodeEntityMappingRepository(MasterDbContext masterDbContext) : IPositionCodeEntityMappingRepository
{
    private IQueryable<PositionCodeEntityMapping> GetActivePositionMapping(long companyId)
    {
        return masterDbContext.PositionCodeEntityMappings
            .Where(p => p.CompanyId == companyId && !p.IsDetached);
    }

    private IQueryable<PositionCode> GetPositions(long companyId, bool includeDeactive = false)
    {
        if (includeDeactive)
        {
            return masterDbContext.PositionCodes
                .Where(p => p.CompanyId == companyId);
        }
        else
        {
            return masterDbContext.PositionCodes
                .Where(p => p.CompanyId == companyId && !p.Deleted);
        }
    }

    private IQueryable<PositionCode> GetPositionsUnderPositionIds(List<long> positionCodeIds, 
        long companyId, bool includeDeactive = false, bool isAdmin = false, bool includeUser = false)
    {
        if (includeUser)
            return GetPositions(companyId, includeDeactive).Where(u =>
                positionCodeIds.Contains(u.Id)
                || positionCodeIds.Contains(u.Parent.Id)
                || positionCodeIds.Contains(u.Parent.Parent.Id)
                || positionCodeIds.Contains(u.Parent.Parent.Parent.Id)
                || positionCodeIds.Contains(u.Parent.Parent.Parent.Parent.Id)
                || positionCodeIds.Contains(u.Parent.Parent.Parent.Parent.Parent.Id)
                || positionCodeIds.Contains(u.Parent.Parent.Parent.Parent.Parent.Parent.Id)
                || positionCodeIds.Contains(u.Parent.Parent.Parent.Parent.Parent.Parent.Parent.Id)
                || isAdmin);
        else
            return GetPositions(companyId, includeDeactive).Where(u =>
                positionCodeIds.Contains(u.Parent.Id)
                || positionCodeIds.Contains(u.Parent.Parent.Id)
                || positionCodeIds.Contains(u.Parent.Parent.Parent.Id)
                || positionCodeIds.Contains(u.Parent.Parent.Parent.Parent.Id)
                || positionCodeIds.Contains(u.Parent.Parent.Parent.Parent.Parent.Id)
                || positionCodeIds.Contains(u.Parent.Parent.Parent.Parent.Parent.Parent.Id)
                || positionCodeIds.Contains(u.Parent.Parent.Parent.Parent.Parent.Parent.Parent.Id)
                || isAdmin);
    }

    public async Task<List<PositionCodeEntityMappingMin>> GetAllPositionCodesUnderUser(long companyId, List<long> positionCodeIds, bool isAdmin, bool includeUser)
    {
        return await GetPositionsUnderPositionIds(positionCodeIds, companyId, false, isAdmin, includeUser)
            .Select(x => new PositionCodeEntityMappingMin
            {
                PositionId = x.Id,
                EntityId = x.PositionCodeEntityMappings.Where(m => !m.IsDetached)
                    .Select(e => e.EntityId).FirstOrDefault(),
                PositionCode = x.Name,
                PositionCodeLevel = x.Level,
                ParentPositionId = x.ParentId
            }).ToListAsync();
    }

    public async Task<List<PositionCodeEntityMappingMin>> GetPositionUsers(long companyId, List<long> pcIds)
    {
        return await masterDbContext.PositionCodeEntityMappings.Where(p => p.CompanyId == companyId && !p.IsDetached && pcIds.Contains(p.PositionCodeId)).Select(p => new PositionCodeEntityMappingMin
        {
            PositionId = p.PositionCodeId,
            EntityId = p.EntityId,
        }).ToListAsync();
    }

    public async Task<List<PositionCodeHierarchy>> GetPositionCodeHierarchies(long companyId, List<long> positionCodeIds)
    {
        return await masterDbContext.PositionCodeHierarchies.Where(s => s.CompanyId == companyId && positionCodeIds.Contains(s.PositionLevel1.Value))
            .ToListAsync();
    }
}