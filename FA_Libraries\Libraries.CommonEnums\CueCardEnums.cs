﻿namespace Libraries.CommonEnums;

public enum CueCardPrimaryType
{
    Custom = 1,
    Standard = 2
}

public enum CueCardSecondaryType
{
    LabelAndValue = 1,
    ProgressBar = 2,
    TrendLine = 3,
    ListOfSchemes = 4,
    ListOfAssets = 5,
    OutletAttributes = 6,
    OutstandingPayments = 7,
    MTDAvg = 8,
    List = 9
}

public enum CueCardActionFlow
{
    OutletTargetAchievement = 1,
    YTDVsLYTD = 2,
    AssetManagementScreen = 3
}

public enum CueCardPerspective
{
    Outlet = 1,
    User = 2
}

public enum MetricType
{
    NotRequired = 0,
    CalculatedMetric = 1,
    ExternalMetric = 2,
    ApiBased = 3
}
