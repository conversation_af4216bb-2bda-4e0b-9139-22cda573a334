﻿using System.Text.Json;
using FAEngage.Core.MasterRepositories;
using FAEngage.Core.Models;
using FAEngage.DbStorage.DbContexts;
using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore;

namespace FAEngage.DbStorage.MasterRepositories;

public class CompanySettingsRepository(MasterDbContext masterDbContext) : ICompanySettingsRepository
{
    private async Task<T> GetData<T>(string key, long companyId, T defaultValue = default(T))
    {
        var settings = await GetSettings(companyId);
        try
        {
            if (settings.TryGetValue(key, out var data))
            {
                return (T)Convert.ChangeType(Convert.ChangeType(data, data.GetType()), typeof(T));
            }
            else
            {
                return defaultValue;
            }
        }
        catch (Exception)
        {
            return defaultValue;
        }
    }

    public virtual async Task<Dictionary<string, object>> GetSettings(long companyId)
    {
        var settingsdb = await (from s in masterDbContext.CompanySettings
            join sv in masterDbContext.CompanySettingValues.Where(sv => sv.CompanyId == companyId)
                on s.Id equals sv.SettingId into svJoined
            from sv in svJoined.DefaultIfEmpty()
            where sv == null || !s.IsDeprecated
            select new CompanySettingsValue
            {
                CompanyId = sv != null ? sv.CompanyId : companyId,
                SettingKey = s.SettingKey,
                SettingType = s.SettingType,
                DefaultValue = s.DefaultValue,
                SettingValue = sv != null ? sv.SettingValue : null
            }).ToListAsync();

        var settings = settingsdb.ToDictionary(k => k.SettingKey,
            v => ConvertFrom(v.SettingType, v?.SettingValue, v.DefaultValue));
        var country = settings.TryGetValue("Country", out var setting) ? setting ?? "India" : "India";
        return await ReplaceCountryDetails(settings, country.ToString());
    }

    public async Task<bool> UsesUserDistributorMapping(long companyId)
    {
        var settings = await GetSettings(companyId);
        var value = settings.TryGetValue("TypeofDistributorMapping", out var setting)
            ? setting.ToString()
            : null;
        return value is "UserDistributor";
    }

    public async Task<bool> UsesBeatDistributorMapping(long companyId)
    {
        var settings = await GetSettings(companyId);
        var value = settings.TryGetValue("TypeofDistributorMapping", out var setting)
            ? setting?.ToString()
            : null;
        return value is "BeatDistributor";
    }

    public async Task<bool> UsesVanSales(long companyId)
    {
        var settings = await GetSettings(companyId);
        var value = settings.GetValueOrDefault("usesVanSales");
        if (value != null) return (bool)value;
        return false;
    }

    public async Task<bool> UsesPositionCodes(long companyId)
    {
        var settings = await GetSettings(companyId);
        var value = settings.GetValueOrDefault("UsesPositionCodes");
        if (value != null) return (bool)value;
        return false;
    }

    public async Task<bool> UsesAutomaticErpIdWithRegionCode(long companyId)
    {
        var settings = await GetSettings(companyId);
        var value = settings.GetValueOrDefault("AutomaticERPIDWithRegionCode");
        if (value != null) return (bool)value;
        return false;
    }

    public async Task<bool> UsesRoutePlan(long companyId)
    {
        var settings = await GetSettings(companyId);
        var value = settings.GetValueOrDefault("JourneyPlanType");
        return value != null && value.Equals("RoutePlan");
    }

    public async Task<bool> UsesNetValueForEmployeeSales(long companyId)
    {
        var settings = await GetSettings(companyId);
        var value = settings.GetValueOrDefault("ShowEmployeeSalesIn");
        return value != null && value.Equals("Net Value");
    }

    public async Task<bool> UsesBeatPlan(long companyId)
    {
        var settings = await GetSettings(companyId);
        var value = settings.GetValueOrDefault("JourneyPlanType");
        return value != null && value.Equals("BeatPlan");
    }

    public async Task<bool> CompanyUsesOpenMarketOperations(long companyId)
    {
        var settings = await GetSettings(companyId);
        var value = settings.GetValueOrDefault("CompanyUsesOpenMarketOperations");
        if (value != null) return (bool)value;
        return false;
    }

    public async Task<bool> UsesSalesInAmount(long companyId)
    {
        var settings = await GetSettings(companyId);
        var value = settings.GetValueOrDefault("UsesSalesinQuantity");
        if (value != null) return (bool)value;
        return false;
    }

    public async Task<bool> UsesScheduledCallProductivity(long companyId)
    {
        var settings = await GetSettings(companyId);
        var value = settings.GetValueOrDefault("ScheduledCallProductivity");
        if (value != null) return (bool)value;
        return false;
    }

    public async Task<TimeSpan> GetCompanyTimeZoneOffset(long companyId)
    {
        try
        {
            return TimeSpan.FromMinutes(await GetData("TimeZoneOffset", companyId, 330));
        }
        catch (Exception)
        {
            return TimeSpan.FromMinutes(330);
        }
    }

    public async Task<bool> UseManualTertiaryOffTake(long companyId)
    {
        var settings = await GetSettings(companyId);
        var value = settings.GetValueOrDefault("tertiaryOfftakeManual");
        if (value != null) return (bool)value;
        return false;
    }

    public async Task<bool> E11Company(long companyId)
    {
        var settings = await GetSettings(companyId);
        var value = settings.GetValueOrDefault("E11Company");
        if (value != null) return (bool)value;
        return false;
    }

    public async Task<bool> UsesInternationalNumberSystem(long companyId)
    {
        var settings = await GetSettings(companyId);
        var value = settings.GetValueOrDefault("NumberSystem");
        if (value != null) return (int)value == 1;
        return false;
    }

    public async Task<string> GetCurrencySymbol(long companyId)
    {
        var settings = await GetSettings(companyId);
        return settings.TryGetValue("CurrencySymbol", out var setting) ? (string)setting : "₹";
    }

    public List<long> GetCompaniesForInvoiceReport(string settingValue)
    {
        var settingId = masterDbContext.CompanySettings
            .Where(x => x.SettingKey == "BillingType")
            .Select(x => x.Id)
            .FirstOrDefault();

        var companyIds = masterDbContext.CompanySettingValues
            .Join(masterDbContext.Companies, r => r.CompanyId, p => p.Id, (r, p) => new { r, p })
            .Where(e => !e.p.Deleted && e.r.SettingId == settingId && e.r.SettingValue == settingValue)
            .Select(e => e.r.CompanyId).Distinct().ToList();

        return companyIds;
    }

    public async Task<TargetValueType> TargetValueType(long companyId)
    {
        var settings = await GetSettings(companyId);
        var x = settings.TryGetValue("TargetValueType", out var setting) ? (string)setting : "Revenue";
        Enum.TryParse(x, out TargetValueType targetValueType);
        return targetValueType;
    }

    public async Task<TargetOn> GetTargetOn(long companyId)
    {
        var settings = await GetSettings(companyId);
        var x = settings.TryGetValue("TargetOn", out var setting) ? (string)setting : "OverAll";
        Enum.TryParse(x, out TargetOn targetOn);
        return targetOn;
    }

    public async Task<string> CalculateAchFrom(long companyId)
    {
        var settings = await GetSettings(companyId);

        try
        {
            if (settings.TryGetValue("CalculateAchFrom", out var setting))
                return (string)setting;
            return "NotApplicable";
        }
        catch (Exception)
        {
            return "NotApplicable";
        }
    }

    public async Task<string> PrimaryTargetsAchievementCalculation(long companyId)
    {
        var settings = await GetSettings(companyId);
        try
        {
            if (settings.TryGetValue("PrimaryTargetsAchievementCalculation", out var setting))
                return (string)setting;
            return "";
        }
        catch (Exception)
        {
            return "";
        }
    }

    public async Task<bool> UseChannelFinancing(long companyId)
    {
        var settings = await GetSettings(companyId);
        var value = settings.GetValueOrDefault("UsesChannelFinancing");
        if (value != null) return (bool)value;
        return false;
    }

    public async Task<bool> UsesEngageWhatsappIntegration(long companyId)
    {
        var settings = await GetSettings(companyId);
        var value = settings.GetValueOrDefault("CompanyUsesEngageWhatsappIntegration");
        if (value != null) return (bool)value;
        return false;
    }

    private static object ConvertFrom(CompanySettingType settingType, string settingValue, string defaultValue)
    {
        switch (settingType)
        {
            case CompanySettingType.Boolean:
                if (!string.IsNullOrWhiteSpace(settingValue) && bool.TryParse(settingValue, out var boolValue))
                    return boolValue;
                return bool.Parse(defaultValue);

            case CompanySettingType.Decimal:
                if (!string.IsNullOrWhiteSpace(settingValue) && double.TryParse(settingValue, out var decimalValue))
                    return decimalValue;
                return double.Parse(defaultValue);

            case CompanySettingType.Integer:
                if (!string.IsNullOrWhiteSpace(settingValue) && long.TryParse(settingValue, out var intValue))
                    return intValue;
                return long.Parse(defaultValue);

            case CompanySettingType.TextList:
                if (string.IsNullOrWhiteSpace(settingValue))
                    return new List<string>();
                return JsonSerializer.Deserialize<List<string>>(settingValue) ?? new List<string>();

            case CompanySettingType.Text:
                return string.IsNullOrWhiteSpace(settingValue) ? defaultValue : settingValue;

            default:
                return settingValue;
        }
    }

    private async Task<Dictionary<string, object>> ReplaceCountryDetails(Dictionary<string, object> configs, string country)
    {
        // Fetch the country details from the database
        var countryDetails = await masterDbContext.CountryDetails
            .FirstOrDefaultAsync(d => d.CountryName == country);

        if (countryDetails == null)
        {
            throw new InvalidOperationException($"Country details for '{country}' not found.");
        }

        // Create a dictionary from the countryDetails properties
        var countryProperties = countryDetails.GetType()
            .GetProperties()
            .Where(p => !p.GetMethod?.IsVirtual ?? false && p.GetIndexParameters().Length == 0)
            .ToDictionary(p => p.Name, p => p.GetValue(countryDetails));

        // Update the configs dictionary with the country properties
        foreach (var property in countryProperties)
        {
            configs[property.Key] = property.Value;
        }

        return configs;
    }

    public async Task<bool> UsesReverseGeoCodeInvoluntryLocation(long companyId)
    {
        var settings = await GetSettings(companyId);
        var value = settings.GetValueOrDefault("reverseGeoCodeInvoluntryLocation");
        if (value != null) return (bool)value;
        return false;
    }
}