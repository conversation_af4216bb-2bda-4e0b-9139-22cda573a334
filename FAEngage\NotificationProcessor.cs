﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Cronos;
using FAEngage.Core.Helpers;
using FAEngage.Core.MasterRepositories;
using FAEngage.Core.Models;
using FAEngage.Core.Models.MasterDbModels;
using FAEngage.Core.Services;
using Flurl.Http;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using Library.Infrastructure.Interface;
using Library.StorageWriter.Reader_Writer;

namespace FAEngage
{
    public interface INotificationProcessor
    {
        Task ProcessUserApp(DateTime date);

        Task ProcessNotification(long id, long companyId);

        Task ProcessNotificationAggregated(long id, long companyId);

        Task ProcessEmployeeNotification(
            long id,
            long companyId,
            long userId,
            Guid transactionId,
            List<long> positionCodeIds
        );

        Task ProcessMessageNotification(
            long id,
            long companyId,
            long userId,
            Guid transactionId,
            List<long> positionCodeIds,
            List<KPICheckModel> kPIs,
            string faTradeNotificationToken,
            string OwnersName,
            Guid? ImageId
        );
    }

    public class NotificationProcessor(
        FAEngageService fAEngageService,
        IQueueManager queueManager,
        IEmployeeRepository employeeRepository,
        ICompanySettingsRepository companySettingsRepository,
        IManagerAlertsRepository managerAlertsRepository,
        IPositionCodeEntityMappingRepository positionCodeEntityMappingRepository,
        IProductDivRepository productDivRepository,
        ILocationRepository locationRepository,
        CrouselBannerBlobReader carouselBannerBlobReader,
        AppNotificationQueueHandler appNotificationQueueHandler
    ) : INotificationProcessor
    {
        private bool IsCronPassed(DateTime date, string cron, long companyId)
        {
            if (cron != null)
            {
                var companySettingsDict = companySettingsRepository.GetSettings(companyId);
                var companySettings = new CompanySettings(companySettingsDict.Result);
                var timeZoneOffset = companySettings.TimeZoneOffset;
                var currentTime = date.Add(timeZoneOffset);
                var startDate = new DateTime(currentTime.AddMinutes(-7).Ticks, DateTimeKind.Utc);
                var endDate = new DateTime(currentTime.AddMinutes(7).Ticks, DateTimeKind.Utc);
                var expression = CronExpression.Parse(cron);
                var occurrences = expression.GetOccurrences(startDate, endDate, true, true);
                return occurrences.ToList().Count > 0;
            }

            return true;
        }

        public async Task ProcessUserApp(DateTime date)
        {
            var notifications = await fAEngageService.GetActiveUserAppNotifications(date);

            var activeNotifications = notifications
                .Where(s => IsCronPassed(date, s.Cron, s.CompanyId))
                .ToList();

            var aggregatedNotifications = notifications
                .Where(s => s.IsAggregated && IsCronPassed(date, s.AggregationCron, s.CompanyId))
                .ToList();

            foreach (var item in activeNotifications)
            {
                await queueManager.AddToNotificationQueue(
                    new EnagageNotificationMain
                    {
                        CompanyId = item.CompanyId,
                        NotificationId = item.Id,
                    }
                );
            }

            foreach (var item in aggregatedNotifications)
            {
                await queueManager.AddToAggregatedNotificationQueue(
                    new EnagageNotificationMain
                    {
                        CompanyId = item.CompanyId,
                        NotificationId = item.Id,
                    }
                );
            }
        }

        public async Task ProcessNotification(long id, long companyId)
        {
            var notification = await fAEngageService.GetNotification(id, companyId);
            var cohorts = notification.CohortIdsLong;
            var transactionId = Guid.NewGuid();
            var userIds = new List<long>();
            var userPositionCodeDict = new Dictionary<long, List<long>>();
            var faTradeTokenUserDict = new Dictionary<long, string>();
            var faTradeUserIdOwnerDict = new Dictionary<long, string>();
            foreach (var cohort in cohorts)
            {
                var dbCohort = await fAEngageService.GetCohort(cohort, companyId);

                #region UserPlatfrom is UserApp Employee Hierarchy case

                if (
                    (
                        dbCohort.UserPlatform == UserPlatform.UserApp
                        || dbCohort.UserPlatform == UserPlatform.AnalyticApp
                    )
                    && dbCohort.ExtraInfoJsonModel
                        is { UserInfos: not null, EmployeeRanks: not null }
                )
                {
                    foreach (var userInfo in dbCohort.ExtraInfoJsonModel.UserInfos)
                    {
                        PortalUserRole userRole;
                        switch (userInfo.Rank)
                        {
                            default:
                                userRole = PortalUserRole.GlobalSalesManager;
                                break;

                            case EmployeeRank.ESM:
                                userRole = PortalUserRole.ClientEmployee;
                                break;

                            case EmployeeRank.ASM:
                                userRole = PortalUserRole.AreaSalesManager;
                                break;

                            case EmployeeRank.RSM:
                                userRole = PortalUserRole.RegionalSalesManager;
                                break;

                            case EmployeeRank.ZSM:
                                userRole = PortalUserRole.ZonalSalesManager;
                                break;

                            case EmployeeRank.NSM:
                                userRole = PortalUserRole.NationalSalesManager;
                                break;
                        }

                        EmployeeType? employeeType = null;
                        var employeeTypes = dbCohort.ExtraInfoJsonModel.EmployeeTypes;
                        if (employeeTypes != null)
                        {
                            if (
                                employeeTypes.Count == 1
                                && employeeTypes.First() == EmployeeType.SR
                            )
                            {
                                employeeType = EmployeeType.SR;
                            }
                            else if (
                                employeeTypes.Count == 1
                                && employeeTypes.First() == EmployeeType.DSR
                            )
                            {
                                employeeType = EmployeeType.DSR;
                            }
                            else if (
                                employeeTypes.Count == 1
                                && employeeTypes.First() == EmployeeType.JSR
                            )
                            {
                                employeeType = EmployeeType.JSR;
                            }
                        }

                        var users = await employeeRepository.GetFieldUserIdsUnderManagerModel(
                            companyId,
                            userRole,
                            userInfo.Id,
                            employeeType
                        );

                        var employeeRanks = dbCohort.ExtraInfoJsonModel.EmployeeRanks;

                        var ids = users
                            .Where(s => employeeRanks.Contains(s.Rank))
                            .Select(s => s.Id)
                            .ToList();

                        userIds.AddRange(ids);
                    }
                }
                #endregion UserPlatfrom is UserApp Employee Hierarchy case

                #region UserPlatfrom is UserApp Position Hierarchy case

                else if (
                    (
                        dbCohort.UserPlatform == UserPlatform.UserApp
                        || dbCohort.UserPlatform == UserPlatform.AnalyticApp
                    )
                    && dbCohort.ExtraInfoJsonModel
                        is { PositionInfos: not null, PositionCodeLevels: not null }
                )
                {
                    foreach (var positionInfo in dbCohort.ExtraInfoJsonModel.PositionInfos)
                    {
                        var positionCodeLevelsList = dbCohort.ExtraInfoJsonModel.PositionCodeLevels;
                        var employeeType = dbCohort.ExtraInfoJsonModel.EmployeeTypes;

                        // Get all the positions under position codes in cohort
                        var hierarchy =
                            await positionCodeEntityMappingRepository.GetAllPositionCodesUnderUser(
                                companyId,
                                positionInfo.PositionId,
                                false,
                                true
                            );

                        var filteredEntityIds = hierarchy
                            .Where(p => positionCodeLevelsList.Contains(p.PositionCodeLevel))
                            .Select(p => p.EntityId)
                            .Distinct()
                            .ToList();

                        userPositionCodeDict = hierarchy
                            .GroupBy(s => s.EntityId)
                            .ToDictionary(
                                gr => gr.Key,
                                gr => gr.Select(r => r.PositionId).ToList()
                            );

                        if (positionCodeLevelsList.Contains(positionInfo.Level))
                        {
                            var parentPositionId =
                                await positionCodeEntityMappingRepository.GetPositionUsers(
                                    companyId,
                                    positionInfo.PositionId
                                );
                            filteredEntityIds.AddRange(
                                parentPositionId.Select(item => item.EntityId)
                            );
                        }

                        List<Employee> employees;
                        if (dbCohort.UserPlatform == UserPlatform.AnalyticApp)
                        {
                            employees = await employeeRepository.GetAllEmployees(companyId);
                            var ids = employees
                                .Where(e => filteredEntityIds.Contains(e.Id))
                                .Select(e => e.Id)
                                .ToList();
                            userIds.AddRange(ids);
                        }
                        else
                        {
                            employees = await employeeRepository.GetAllEmployeesSRAndDSR(companyId);
                            var ids = employees
                                .Where(e =>
                                    filteredEntityIds.Contains(e.Id)
                                    && employeeType.Contains(e.EmployeeType)
                                )
                                .Select(e => e.Id)
                                .ToList();
                            userIds.AddRange(ids);
                        }
                    }
                }
                #endregion UserPlatfrom is UserApp Position Hierarchy case

                #region UserPlatfrom is TradeApp

                else if (dbCohort.UserPlatform == UserPlatform.FATradeApp)
                {
                    var geographicalDetails = dbCohort.ExtraInfoJsonModel.GeographyInfos;
                    var outletAttributes = dbCohort.ExtraInfoJsonModel.OutletAttributes;
                    var outletUniverse = await locationRepository.GetLocationsForCompany(
                        dbCohort.CompanyId
                    );
                    var filteredOutlets = await GetFilteredOutlets(
                        geographicalDetails,
                        outletAttributes,
                        outletUniverse,
                        dbCohort.CompanyId
                    );
                    var faTradeUsers = await locationRepository.GetFATradeUserForOwnersNumber(
                        filteredOutlets.ConvertAll(s => s.OwnersNo)
                    );
                    faTradeUserIdOwnerDict = faTradeUsers.ToDictionary(
                        s => s.Id,
                        s => s.OwnersName
                    );
                    var faTradeTokens = await locationRepository.GetFATradeTokens(
                        faTradeUsers.ConvertAll(s => s.Id),
                        companyId
                    );
                    faTradeTokenUserDict = faTradeTokens
                        .GroupBy(s => s.UserId)
                        .ToDictionary(
                            gr => gr.Key,
                            gr =>
                                gr.Where(s => !string.IsNullOrWhiteSpace(s.NotificationToken))
                                    .OrderByDescending(r => r.CreatedAt)
                                    .Select(q => q.NotificationToken)
                                    .FirstOrDefault()
                        );
                    userIds.AddRange(faTradeTokenUserDict.Select(s => s.Key).ToList());
                }

                #endregion UserPlatfrom is TradeApp
            }

            if (notification.NotificationType == NotificationCondition.General)
            {
                foreach (var item in userIds.Distinct().ToList())
                {
                    await queueManager.AddToNotificationMessage(
                        new EnagageNotificationEmployee
                        {
                            CompanyId = companyId,
                            NotificationId = id,
                            UserId = item,
                            TransactionId = transactionId,
                            PositionCodeIds =
                                userPositionCodeDict?.Count > 0
                                    ? userPositionCodeDict.GetValueOrDefault(item)
                                    : null,
                            FATradeNotificationToken =
                                faTradeTokenUserDict.Count > 0
                                    ? faTradeTokenUserDict.GetValueOrDefault(item)
                                    : null,
                            OwnersName =
                                faTradeUserIdOwnerDict?.Count > 0
                                    ? faTradeUserIdOwnerDict.GetValueOrDefault(item)
                                    : null,
                            ImageId = null,
                        }
                    );
                }
            }
            else if (notification.NotificationType == NotificationCondition.KRABased)
            {
                foreach (var item in userIds.Distinct().ToList())
                {
                    await queueManager.AddToFieldUserNotification(
                        new EnagageNotificationEmployee
                        {
                            CompanyId = companyId,
                            NotificationId = id,
                            UserId = item,
                            TransactionId = transactionId,
                            PositionCodeIds =
                                userPositionCodeDict?.Count > 0
                                    ? userPositionCodeDict.GetValueOrDefault(item)
                                    : null,
                        }
                    );
                }
            }
        }

        public async Task ProcessEmployeeNotification(
            long id,
            long companyId,
            long userId,
            Guid transactionId,
            List<long> positionCodeIds
        )
        {
            var userRole = (await employeeRepository.GetActiveEmployeeById(userId)).UserRole;
            var kpiTriggers = await fAEngageService.GetKPITriggers(id, companyId);
            var passed = false;

            var kpiList = new List<KPICheckModel>();

            //we need to check if all kpi triggers passed then only we send notification message
            foreach (var trigger in kpiTriggers)
            {
                var kpiCheck = await fAEngageService.CheckKPIPassed(
                    id,
                    trigger.KpiId,
                    companyId,
                    userId,
                    userRole,
                    trigger.Value,
                    trigger.ComparisonOperator
                );

                kpiList.Add(kpiCheck);

                if (!kpiCheck.Passed)
                {
                    passed = false;
                    break;
                }

                passed = true;
            }

            if (passed)
            {
                await queueManager.AddToNotificationMessage(
                    new EnagageNotificationEmployee
                    {
                        CompanyId = companyId,
                        NotificationId = id,
                        UserId = userId,
                        TransactionId = transactionId,
                        KPIs = kpiList,
                        PositionCodeIds = positionCodeIds,
                    }
                );
            }
        }

        public async Task ProcessMessageNotification(
            long id,
            long companyId,
            long userId,
            Guid transactionId,
            List<long> positionCodeIds,
            List<KPICheckModel> kPIs,
            string faTradeNotificationToken,
            string OwnersName,
            Guid? ImageId
        )
        {
            var notification = await fAEngageService.GetNotification(id, companyId);
            var aggregationHierarchy = new List<EntityUserMinForEngage>();
            if (notification.UserPlatform == UserPlatform.FATradeApp)
            {
                var message1 = await fAEngageService.GetNotificationMessage(id, companyId);
                var randomMessageText1 = RandomMessage(message1.MessageTestList);
                var replacedMessageText1 = await ReplaceVariableOfMessage(
                    randomMessageText1,
                    id,
                    companyId,
                    "",
                    0,
                    kPIs,
                    "",
                    OwnersName
                );
                var pushNotificationData = new PushNotificationMessage
                {
                    AlertSource = AlertSource.TradeApp,
                    GcmIds = new List<string> { faTradeNotificationToken },
                    Message = new ApiMessage
                    {
                        title = message1.Title,
                        message = replacedMessageText1,
                        NotificationType = NotificationType.Default,
                        orderId = null,
                        imageUrl =
                            message1.Image != null
                                ? carouselBannerBlobReader.GetPublicPath(message1.Image)
                                : null,
                        ctaReturnModel = JsonSerializer.Serialize(
                            new CTAReturnModel
                            {
                                ctaName = "Click Here",
                                ctaType = message1.Uri != null ? CtaType.URL : CtaType.AppScreen,
                                ctaAction =
                                    message1.Uri != null
                                        ? message1.Uri
                                        : message1.OnClickScreen.ToString(),
                            }
                        ),
                    },
                };
                await appNotificationQueueHandler.AddToGridQueue(
                    Guid.NewGuid().ToString(),
                    pushNotificationData
                );
                // maintain log to notifications send
                //saving log to database
                await fAEngageService.AddLogToDB(
                    id,
                    companyId,
                    userId,
                    transactionId,
                    "",
                    true,
                    $"Notification send successfully for notification id {id} and company {companyId}",
                    Guid.NewGuid()
                );
            }
            else if (positionCodeIds == null || positionCodeIds?.Count == 0)
            {
                if (notification.ManagerLevel != null)
                {
                    var aggregationLevel = notification.ManagerLevelRoles;
                    if (aggregationLevel?.Count > 0)
                    {
                        var userHierarchy =
                            await employeeRepository.GetEmployeeWithHierarchyWithNewId(
                                companyId,
                                userId
                            );
                        if (userHierarchy != null)
                        {
                            foreach (var level in aggregationLevel)
                            {
                                switch (level)
                                {
                                    case PortalUserRole.AreaSalesManager:
                                        aggregationHierarchy.Add(
                                            new EntityUserMinForEngage
                                            {
                                                UserId = userHierarchy.ParentId ?? 0,
                                                UserRole = PortalUserRole.AreaSalesManager,
                                            }
                                        );
                                        break;

                                    case PortalUserRole.RegionalSalesManager:
                                        aggregationHierarchy.Add(
                                            new EntityUserMinForEngage
                                            {
                                                UserId = userHierarchy.Parent2Id ?? 0,
                                                UserRole = PortalUserRole.RegionalSalesManager,
                                            }
                                        );
                                        break;

                                    case PortalUserRole.ZonalSalesManager:
                                        aggregationHierarchy.Add(
                                            new EntityUserMinForEngage
                                            {
                                                UserId = userHierarchy.Parent3Id ?? 0,
                                                UserRole = PortalUserRole.ZonalSalesManager,
                                            }
                                        );
                                        break;

                                    case PortalUserRole.NationalSalesManager:
                                        aggregationHierarchy.Add(
                                            new EntityUserMinForEngage
                                            {
                                                UserId = userHierarchy.Parent4Id ?? 0,
                                                UserRole = PortalUserRole.NationalSalesManager,
                                            }
                                        );
                                        break;

                                    case PortalUserRole.GlobalSalesManager:
                                        aggregationHierarchy.Add(
                                            new EntityUserMinForEngage
                                            {
                                                UserId = userHierarchy.Parent5Id ?? 0,
                                                UserRole = PortalUserRole.GlobalSalesManager,
                                            }
                                        );
                                        break;
                                }
                            }
                        }
                    }
                }
            }
            else
            {
                if (notification.ManagerLevel != null)
                {
                    var aggregationLevel = notification.ManagerLevelRoles;
                    if (aggregationLevel != null && aggregationLevel.Count > 0)
                    {
                        var positionHierarchiesDict = (
                            await positionCodeEntityMappingRepository.GetPositionCodeHierarchies(
                                companyId,
                                positionCodeIds
                            )
                        )
                            .GroupBy(s => s.PositionLevel1)
                            .ToDictionary(gr => gr.Key, gr => gr.Select(r => r).LastOrDefault());

                        foreach (var positionCodeId in positionCodeIds)
                        {
                            var positionCodeHierarchy = positionHierarchiesDict.GetValueOrDefault(
                                positionCodeId
                            );

                            if (positionCodeHierarchy != null)
                            {
                                foreach (var level in aggregationLevel)
                                {
                                    switch (level)
                                    {
                                        case PortalUserRole.AreaSalesManager:
                                            aggregationHierarchy.Add(
                                                new EntityUserMinForEngage
                                                {
                                                    UserId =
                                                        positionCodeHierarchy.PositionLevel2UserId
                                                        ?? 0,
                                                    UserRole = PortalUserRole.AreaSalesManager,
                                                    PositionCodeId =
                                                        positionCodeHierarchy.PositionLevel2 ?? 0,
                                                }
                                            );
                                            break;

                                        case PortalUserRole.RegionalSalesManager:
                                            aggregationHierarchy.Add(
                                                new EntityUserMinForEngage
                                                {
                                                    UserId =
                                                        positionCodeHierarchy.PositionLevel3UserId
                                                        ?? 0,
                                                    UserRole = PortalUserRole.RegionalSalesManager,
                                                    PositionCodeId =
                                                        positionCodeHierarchy.PositionLevel3 ?? 0,
                                                }
                                            );
                                            break;

                                        case PortalUserRole.ZonalSalesManager:
                                            aggregationHierarchy.Add(
                                                new EntityUserMinForEngage
                                                {
                                                    UserId =
                                                        positionCodeHierarchy.PositionLevel4UserId
                                                        ?? 0,
                                                    UserRole = PortalUserRole.ZonalSalesManager,
                                                    PositionCodeId =
                                                        positionCodeHierarchy.PositionLevel4 ?? 0,
                                                }
                                            );
                                            break;

                                        case PortalUserRole.NationalSalesManager:
                                            aggregationHierarchy.Add(
                                                new EntityUserMinForEngage
                                                {
                                                    UserId =
                                                        positionCodeHierarchy.PositionLevel5UserId
                                                        ?? 0,
                                                    UserRole = PortalUserRole.NationalSalesManager,
                                                    PositionCodeId =
                                                        positionCodeHierarchy.PositionLevel5 ?? 0,
                                                }
                                            );
                                            break;

                                        case PortalUserRole.GlobalSalesManager:
                                            aggregationHierarchy.Add(
                                                new EntityUserMinForEngage
                                                {
                                                    UserId =
                                                        positionCodeHierarchy.PositionLevel6UserId
                                                        ?? 0,
                                                    UserRole = PortalUserRole.GlobalSalesManager,
                                                    PositionCodeId =
                                                        positionCodeHierarchy.PositionLevel6 ?? 0,
                                                }
                                            );
                                            break;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (notification.UserPlatform != UserPlatform.FATradeApp)
            {
                string aggregationHierarchyJson =
                    aggregationHierarchy.Count > 0
                        ? JsonSerializer.Serialize(aggregationHierarchy)
                        : "[]";

                var message = await fAEngageService.GetNotificationMessage(id, companyId);
                var randomMessageText = RandomMessage(message.MessageTestList);

                var employee = await employeeRepository.GetActiveEmployeeById(userId);
                var empName = employee?.Name;
                var empMobileNo = employee?.ContactNo;

                var replacedMessageText = await ReplaceVariableOfMessage(
                    randomMessageText,
                    id,
                    companyId,
                    empName,
                    0,
                    kPIs,
                    "",
                    ""
                );

                var forAnalytics = notification.UserPlatform == UserPlatform.AnalyticApp;
                var empId = forAnalytics ? employee?.OldTableId ?? userId : userId;
                var res = await NotifiyUser(
                    empId,
                    message,
                    replacedMessageText,
                    transactionId,
                    companyId,
                    forAnalytics
                );

                //for sending notifications on WhatsApp
                var companySettingsDict = await companySettingsRepository.GetSettings(companyId);
                var companySettings = new CompanySettings(companySettingsDict);
                var usesWhatsApp = companySettings.UsesEngageWhatsappIntegration;
                var data = new NotificationResponseReturn();
                if (usesWhatsApp && message.ForWhatsapp)
                {
                    var TemplateVariables = await ReplaceVariablesOfMessage(
                        message.WhatsAppVariablesList,
                        id,
                        companyId,
                        empName,
                        0,
                        kPIs,
                        ""
                    );
                    var TemplateId = message.TemplateID;
                    if (companyId == 172305)
                    {
                        data = await SendToWhatsAppForSARGroup(
                            res.SendId,
                            empMobileNo,
                            TemplateId,
                            TemplateVariables,
                            employee.Id,
                            companyId,
                            replacedMessageText
                        );
                    }

                    if (companyId == 11417)
                    {
                        data = await SendToWhatsAppForSignify(
                            res.SendId,
                            empName,
                            empMobileNo,
                            TemplateId,
                            TemplateVariables
                        );
                    }

                    //saving whatsApp log to database
                    await fAEngageService.AddWhatsAppLogToDB(
                        id,
                        companyId,
                        userId,
                        transactionId,
                        aggregationHierarchyJson,
                        data.Success,
                        data.Message,
                        data.SendId
                    );
                }

                //saving log to database
                if (!forAnalytics)
                {
                    await fAEngageService.AddLogToDB(
                        id,
                        companyId,
                        userId,
                        transactionId,
                        aggregationHierarchyJson,
                        res.Success,
                        res.Message,
                        res.SendId
                    );
                }
                else
                {
                    // for analytics case log to be saved in FAManagerEngageLogs
                    var alertId = await managerAlertsRepository.SaveManagerAlertFromFaEngage(
                        userId,
                        employee.UserRole,
                        companyId,
                        message.Title,
                        replacedMessageText,
                        id,
                        positionCodeIds.Select(id => (long?)id).ToList(),
                        true
                    );

                    await fAEngageService.AddAnalyticsLogToDB(
                        id,
                        companyId,
                        userId,
                        res.Success,
                        res.Message,
                        res.SendId,
                        transactionId,
                        alertId,
                        positionCodeIds.Select(id => (long?)id).ToList(),
                        true
                    );
                }
            }
        }

        private async Task<string> ReplaceVariableOfMessage(
            string message,
            long notificationId,
            long companyId,
            string empName,
            int userCount,
            List<KPICheckModel> kPIs,
            string underEmpName,
            string ownersName
        )
        {
            if (message.Contains("[[UserName]]"))
            {
                message = message.Replace("[[UserName]]", empName);
            }

            if (message.Contains("[[UsersCount]]"))
            {
                message = message.Replace("[[UsersCount]]", userCount.ToString());
            }

            if (message.Contains("[[UsersList]]"))
            {
                message = message.Replace("[[UsersList]]", underEmpName);
            }

            if (message.Contains("[[OwnerName]]"))
            {
                message = message.Replace("[[OwnerName]]", ownersName);
            }

            if (kPIs != null)
            {
                foreach (var kpi in kPIs)
                {
                    if (message.Contains($"<<{kpi.KPIId}>>"))
                    {
                        message = message.Replace($"<<{kpi.KPIId}>>", kpi.AchValue);
                    }
                }
            }

            return message;
        }

        private async Task<List<string>> ReplaceVariablesOfMessage(
            List<string> messages,
            long notificationId,
            long companyId,
            string empName,
            int userCount,
            List<KPICheckModel> kPIs,
            string underEmpName
        )
        {
            var result = new List<string>();

            foreach (var message in messages)
            {
                var data = message;
                if (message.Contains("[[UserName]]"))
                {
                    data = message.Replace("[[UserName]]", empName);
                }

                if (message.Contains("[[UsersCount]]"))
                {
                    data = message.Replace("[[UsersCount]]", userCount.ToString());
                }

                if (message.Contains("[[UsersList]]"))
                {
                    data = message.Replace("[[UsersList]]", underEmpName);
                }

                if (kPIs != null)
                {
                    foreach (var kpi in kPIs)
                    {
                        if (message.Contains($"<<{kpi.KPIId}>>"))
                        {
                            data = message.Replace($"<<{kpi.KPIId}>>", kpi.AchValue);
                        }
                    }
                }

                result.Add(data);
            }

            return result;
        }

        private string RandomMessage(List<string> messages)
        {
            var r = new Random();
            var randomMessage = messages.ElementAt(r.Next(0, messages.Count));
            return randomMessage;
        }

        public async Task ProcessNotificationAggregated(long id, long companyId)
        {
            var logs = await fAEngageService.GetLastLogs(id, companyId);

            if (logs.Count() == 0)
            {
                return;
            }

            var dic = logs.SelectMany(s => s.AggregationHierarchy.Select(d => new { s, d.UserId }))
                .Where(k => k.UserId != 0)
                .GroupBy(s => s.UserId)
                .Select(s => new { Key = s.Key, data = s.Select(f => f.s).Distinct().ToList() })
                .ToDictionary(s => s.Key, s => s.data);

            var userPositionMapDict = logs.SelectMany(s => s.AggregationHierarchy)
                .Where(k => k.PositionCodeId != 0)
                .GroupBy(s => s.UserId)
                .ToDictionary(s => s.Key, s => s.Select(f => f.PositionCodeId).Distinct().ToList());

            var message = await fAEngageService.GetNotificationAggregationMessage(id, companyId);

            var randomMessageText = RandomMessage(message.MessageTestList);

            foreach (var item in dic)
            {
                var employee = await employeeRepository.GetActiveEmployeeById(item.Key);
                if (employee == null)
                    continue;
                var empName = employee.Name;
                var empMobileNo = employee.ContactNo;

                var underEmpNames = (
                    await employeeRepository.GetAllEmployees(
                        companyId,
                        item.Value.Select(a => a.UserId).Distinct().ToList()
                    )
                )
                    .Select(a => a.Name)
                    .ToList();

                var replacedMessageText = await ReplaceVariableOfMessage(
                    randomMessageText,
                    id,
                    companyId,
                    empName,
                    item.Value.Count,
                    null,
                    string.Join(", ", underEmpNames),
                    ""
                );

                var positionCodeIds = userPositionMapDict.GetValueOrDefault(item.Key);

                //saving manager alert as well
                var alertId = await managerAlertsRepository.SaveManagerAlertFromFaEngage(
                    employee.Id,
                    employee.UserRole,
                    companyId,
                    message.Title,
                    replacedMessageText,
                    id,
                    positionCodeIds
                );

                var res = await NotifiyUser(
                    employee.OldTableId,
                    message,
                    replacedMessageText,
                    logs.First().NotificationTransactionId,
                    companyId,
                    true,
                    alertId
                );

                //for sending notifications on WhatsApp
                var companySettingsDict = await companySettingsRepository.GetSettings(companyId);
                var companySettings = new CompanySettings(companySettingsDict);
                var usesWhatsApp = companySettings.UsesEngageWhatsappIntegration;
                var data = new NotificationResponseReturn();
                if (usesWhatsApp && message.ForWhatsapp)
                {
                    var TemplateVariables = await ReplaceVariablesOfMessage(
                        message.WhatsAppVariablesList,
                        id,
                        companyId,
                        empName,
                        item.Value.Count,
                        null,
                        string.Join(", ", underEmpNames)
                    );
                    var TemplateId = message.TemplateID;
                    if (companyId == 172305)
                    {
                        data = await SendToWhatsAppForSARGroup(
                            res.SendId,
                            empMobileNo,
                            TemplateId,
                            TemplateVariables,
                            employee.Id,
                            companyId,
                            replacedMessageText
                        );
                    }

                    if (companyId == 11417)
                    {
                        data = await SendToWhatsAppForSignify(
                            res.SendId,
                            empName,
                            empMobileNo,
                            TemplateId,
                            TemplateVariables
                        );
                    }

                    //saving whatsApp log to database
                    await fAEngageService.AddWhatsAppLogToDB(
                        id,
                        companyId,
                        employee.Id,
                        logs[0].NotificationTransactionId,
                        null,
                        data.Success,
                        data.Message,
                        data.SendId
                    );
                }

                //saving log to database
                await fAEngageService.AddAnalyticsLogToDB(
                    id,
                    companyId,
                    item.Key,
                    res.Success,
                    res.Message,
                    res.SendId,
                    logs[0].NotificationTransactionId,
                    alertId,
                    positionCodeIds,
                    false
                );
            }
        }

        private async Task<NotificationResponseReturn> NotifiyUser(
            long userId,
            NotificationMessage notificationMessage,
            string message,
            Guid transactionId,
            long companyId,
            bool ForAnalytics = false,
            long? alertId = null
        )
        {
            var sendId = Guid.NewGuid();
            try
            {
                var companySettingsDict = await companySettingsRepository.GetSettings(companyId);
                var companySettings = new CompanySettings(companySettingsDict);
                var isE11 = companySettings.E11Company;
                var token = string.Empty;
                var notificationSource = AlertSource.Default;
                if (ForAnalytics)
                {
                    token = await employeeRepository.GetManagerToken(userId, companyId);
                    notificationSource = AlertSource.ManagerAnalytics;
                }
                else
                {
                    notificationSource = AlertSource.GTPlayStore;
                    var deviceInfo = await employeeRepository.GetFieldUserAlertTypeAsync(
                        userId,
                        companyId
                    );
                    if (deviceInfo != null)
                    {
                        token = deviceInfo.GcmId;
                        notificationSource = deviceInfo.AlertSource;
                    }

                    if (isE11)
                    {
                        // Currently using Default for E11 source as json is not provided for E11
                        notificationSource = AlertSource.Default;
                    }
                }

                if (token == null)
                {
                    return new NotificationResponseReturn
                    {
                        Message = $"Error occured , user gcm token is null",
                        Success = false,
                        SendId = sendId,
                    };
                }

                var pushNotificationMessage = CreatePushNotificationMessage(
                    notificationMessage,
                    NotificationType.Engage,
                    notificationSource,
                    token,
                    sendId,
                    transactionId,
                    message,
                    alertId
                );

                await appNotificationQueueHandler.AddToGridQueue(
                    sendId.ToString(),
                    pushNotificationMessage
                );

                return new NotificationResponseReturn
                {
                    Message = $"Message sent , id is {sendId}",
                    Success = true,
                    SendId = sendId,
                };
            }
            catch (Exception ex)
            {
                return new NotificationResponseReturn
                {
                    Message = ex.GetBaseException().Message,
                    Success = false,
                    SendId = sendId,
                };
            }
        }

        private static PushNotificationMessage CreatePushNotificationMessage(
            NotificationMessage notificationMessage,
            NotificationType notificationType,
            AlertSource notificationSource,
            string token,
            Guid sendId,
            Guid transactionId,
            string message,
            long? alertId = null
        )
        {
            var pushNotificationMessage = new PushNotificationMessage
            {
                AlertSource = notificationSource,
                GcmIds = new List<string> { token },
                Message = new ApiMessage
                {
                    title = notificationMessage.Title,
                    message = message,
                    NotificationType = notificationType,
                    metaData = JsonSerializer.Serialize(
                        new EngageMetaData
                        {
                            id = sendId,
                            transactionId = transactionId,
                            alertId = alertId,
                            placement = (
                                notificationMessage.NotificationType ?? AppNotificationType.InApp
                            ),
                            inAppScreen = notificationMessage.AppScreen,
                            actionLink = notificationMessage.Uri,
                            actionScreen = notificationMessage.OnClickScreen,
                            emoji = notificationMessage.Emoji,
                            message = message,
                            title = notificationMessage.Title,
                        }
                    ),
                },
            };

            return pushNotificationMessage;
        }

        public async Task<NotificationResponseReturn> SendToWhatsAppForSARGroup(
            Guid sendId,
            string empMobileNo,
            string TemplateId,
            List<string> TemplateVariables,
            long employeeId,
            long companyId,
            string message
        )
        {
            try
            {
                var productDivisionForEmp =
                    await productDivRepository.GetProductDivisionForEmployee(companyId, employeeId);

                var API_KEY =
                    "eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2FwaS5teXZhbHVlZmlyc3QuY29tL3BzbXMiLCJzdWIiOiJsaXZmYXN0d2EiLCJleHAiOjMyNTA5Nzc1NDZ9.Bs_EdjtPjXsPhbUaUkZa_3KExwHBJbvonmKS1OC4bN8";
                var from = "************";
                if (productDivisionForEmp == 467657)
                {
                    API_KEY =
                        "eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2FwaS5teXZhbHVlZmlyc3QuY29tL3BzbXMiLCJzdWIiOiJsaXZmYXN0d2EiLCJleHAiOjMyNTA5Nzc1NDZ9.Bs_EdjtPjXsPhbUaUkZa_3KExwHBJbvonmKS1OC4bN8";
                    from = "************";
                }
                else if (productDivisionForEmp == 426742)
                {
                    API_KEY =
                        "eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2FwaS5teXZhbHVlZmlyc3QuY29tL3BzbXMiLCJzdWIiOiJsaXZndWFyZHdhIiwiZXhwIjozMjU4OTY3MzMwfQ.QuVlcb2zji_itJN0RGdu7dhTwOeL6DXuawdhr6Axx3k";
                    from = "919205778444";
                }

                var TemplateInfo = $"{TemplateId}~{string.Join("~", TemplateVariables)}";

                var data = new MessageSAR
                {
                    @VER = "1.2",
                    USER = new User() { @CH_TYPE = "4", @UNIXTIMESTAMP = "" },
                    SMS = new List<SMS>()
                    {
                        new SMS()
                        {
                            @UDH = "0",
                            @CODING = "1",
                            @TEXT = message,
                            @TEMPLATEINFO = TemplateInfo,
                            @PROPERTY = "0",
                            @MSGTYPE = "1",
                            @ID = sendId.ToString(),

                            ADDRESS = new List<ADDRESS>()
                            {
                                new ADDRESS()
                                {
                                    @FROM = from,
                                    @TO = "91" + empMobileNo,
                                    @SEQ = "1",
                                    @TAG = "some clientside random data",
                                },
                            },
                        },
                    },
                };

                var response = await "https://api.myvfirst.com/psms/servlet/psms.JsonEservice"
                    .WithHeader("Authorization", $"Bearer {API_KEY}")
                    .PostJsonAsync(data);

                var responseContent = await response.GetStringAsync();

                if (response.StatusCode != 200)
                {
                    return new NotificationResponseReturn
                    {
                        Message = responseContent,
                        Success = false,
                        SendId = sendId,
                    };
                }
                else
                {
                    var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                    try
                    {
                        var objectJson = JsonSerializer.Deserialize<WhatsAppResponseSARGroup>(
                            responseContent,
                            options
                        );
                        if (objectJson.MESSAGEACK.Err == null && objectJson.MESSAGEACK.GUID.ERROR == null)
                        {
                            return new NotificationResponseReturn
                            {
                                Message = JsonSerializer.Serialize(objectJson),
                                Success = true,
                                SendId = sendId,
                            };
                        }
                        else
                        {
                            return new NotificationResponseReturn
                            {
                                Message = JsonSerializer.Serialize(objectJson),
                                Success = false,
                                SendId = sendId,
                            };
                        }
                    }
                    catch (Exception ex)
                    {
                        return new NotificationResponseReturn
                        {
                            Message = responseContent,
                            Success = false,
                            SendId = sendId,
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                return new NotificationResponseReturn
                {
                    Message = ex.GetBaseException().Message,
                    Success = false,
                    SendId = sendId,
                };
            }
        }

        public async Task<NotificationResponseReturn> SendToWhatsAppForSignify(
            Guid sendId,
            string userName,
            string userMobileNo,
            string TemplateId,
            List<string> TemplateVariables
        )
        {
            try
            {
                var API_KEY =
                    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjYzYmVjNDhjZDcyNDdkMTY1ZWUwMjgyZSIsIm5hbWUiOiJTaWduaWZ5IEluZGlhIDIiLCJhcHBOYW1lIjoiQWlTZW5zeSIsImNsaWVudElkIjoiNjM1MTBjZGI0YzI5NmM0N2MxYmEyNDIwIiwiYWN0aXZlUGxhbiI6Ik5PTkUiLCJpYXQiOjE2NzM0NDY1NDB9.jfLRww6aA03bBSZRA7CIYZQeNFP1PgU-vJ3ubxCfO7c";

                var data = new MessageSignify
                {
                    apiKey = API_KEY,
                    campaignName = TemplateId,
                    destination = userMobileNo,
                    userName = userName,
                    media = new MEDIA { url = null, fileName = null },
                    templateParams = TemplateVariables,
                };

                var response = await "https://backend.aisensy.com/campaign/t1/api"
                    .WithHeader("Authorization", API_KEY)
                    .PostJsonAsync(data);

                var responseContent = await response.GetStringAsync();

                if (response.StatusCode != 200)
                {
                    var objectJson = JsonSerializer.Deserialize<WhatsAppResponseSignify>(
                        responseContent
                    );
                    return new NotificationResponseReturn
                    {
                        Message = objectJson.errorMessage,
                        SendId = sendId,
                        Success = false,
                    };
                }
                else
                {
                    return new NotificationResponseReturn
                    {
                        Message = "WhatsApp Message sent",
                        SendId = sendId,
                        Success = true,
                    };
                }
            }
            catch (FlurlHttpException ex)
            {
                var errorResponse = await ex.GetResponseStringAsync();
                return new NotificationResponseReturn
                {
                    Message = errorResponse ?? ex.GetBaseException().Message,
                    Success = false,
                    SendId = sendId,
                };
            }
            catch (Exception ex)
            {
                return new NotificationResponseReturn
                {
                    Message = ex.GetBaseException().Message,
                    Success = false,
                    SendId = sendId,
                };
            }
        }

        public async Task<List<LocationDto>> GetFilteredOutlets(
            List<GeographyInfo> geographyInfos,
            OutletAttributes outletAttributes,
            List<LocationDto> outletUniverse,
            long companyId
        )
        {
            var outlets = new List<LocationDto>();
            var filteredData = new List<LocationDto>();
            if (geographyInfos is not null)
            {
                foreach (var geographyInfo in geographyInfos)
                {
                    var data = new List<LocationDto>();
                    switch (geographyInfo.Level)
                    {
                        case GeographyHierarchy.Beat:
                            data = outletUniverse
                                .Where(s =>
                                    geographyInfo?.GeographyIds?.Contains(s.BeatId.Value) == true
                                )
                                .ToList();
                            break;

                        case GeographyHierarchy.Territory:
                            data = outletUniverse
                                .Where(s =>
                                    geographyInfo?.GeographyIds?.Contains(s.TerritoryId.Value)
                                    == true
                                )
                                .ToList();
                            break;

                        case GeographyHierarchy.Region:
                            data = outletUniverse
                                .Where(s =>
                                    geographyInfo?.GeographyIds?.Contains(s.RegionId.Value) == true
                                )
                                .ToList();
                            break;

                        case GeographyHierarchy.Zone:
                            data = outletUniverse
                                .Where(s =>
                                    geographyInfo?.GeographyIds?.Contains(s.ZoneId.Value) == true
                                )
                                .ToList();
                            break;
                            // TODO : Geography Hierarchy Level1, Level2, Level3 implementation left as of now
                    }

                    outlets.AddRange(data);
                }
            }

            if (outletAttributes is not null)
            {
                var outletSegmentations = outletAttributes.OutletSegmentation;
                var outletChannels = outletAttributes.OutletChannel;
                var outletShopTypes = outletAttributes.OutletShopType;
                var outletsForShopTypes = new List<LocationDto>();
                var outletsForChannels = new List<LocationDto>();
                var outletsForSegmentations = new List<LocationDto>();
                if (outletShopTypes?.Count > 0)
                {
                    var companyShopTypes = await locationRepository.GetShopTypes(companyId);
                    var cohortShopTypeIds = companyShopTypes
                        .Where(s => outletShopTypes.Contains(s.ShopTypeName))
                        .Select(s => s.Id)
                        .ToList();
                    outletsForShopTypes = outlets
                        .Where(s =>
                            (s.ShopTypeId != null && cohortShopTypeIds.Contains(s.ShopTypeId.Value))
                            || (s.ShopTypeId == null && outletShopTypes.Contains(s.ShopType))
                        )
                        .ToList();
                }

                if (outletSegmentations?.Count > 0)
                {
                    outletsForSegmentations = outlets
                        .Where(s => outletSegmentations.Contains(s.Segmentation))
                        .ToList();
                }

                if (outletChannels?.Count > 0)
                {
                    outletsForChannels = outlets
                        .Where(s => outletChannels.Contains(s.OutletChannel))
                        .ToList();
                }

                var dataToReturn = new List<LocationDto>();
                dataToReturn.AddRange(outletsForChannels);
                dataToReturn.AddRange(outletsForShopTypes);
                dataToReturn.AddRange(outletsForSegmentations);

                filteredData = dataToReturn
                    .Where(s => s.IsFocused == outletAttributes.IsFocused)
                    .ToList();
            }

            if (filteredData.Count > 0)
            {
                return filteredData;
            }
            else
            {
                return outletUniverse;
            }
        }
    }
}
