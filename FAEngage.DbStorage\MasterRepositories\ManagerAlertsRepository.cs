﻿using FAEngage.Core.MasterRepositories;
using FAEngage.Core.Models.MasterDbModels;
using FAEngage.DbStorage.DbContexts;
using Libraries.CommonEnums;

namespace FAEngage.DbStorage.MasterRepositories;

public class ManagerAlertsRepository(WritableMasterDbContext writableMasterDb) : IManagerAlertsRepository
{
    public async Task<long> SaveManagerAlertFromFaEngage(long managerId, PortalUserRole managerRole,
           long companyId, string title, string message, long notificationId, List<long?> positionCodeIds, bool isNotAggregated = false)
    {
        if (positionCodeIds?.Count > 0)
        {
            var alertIds = new List<long>();
            foreach (var positionCodeId in positionCodeIds)
            {
                var alert = new ManagerAlert()
                {
                    UserId = managerId,
                    UserRole = managerRole,
                    Action = ManagerAlertAction.Pending,
                    AlertServerTime = DateTime.UtcNow,
                    CompanyId = companyId,
                    CreatedAt = DateTime.UtcNow,
                    CreationContext = $"FAEngage-{notificationId}",
                    DeviceTime = DateTime.UtcNow,
                    AlertType = isNotAggregated ? AlertType.FaEngageNotAggregated : AlertType.FaEngage,
                    AlertTitle = title,
                    AlertDescription = message,
                    ReportingManager = managerId,
                    ReportingManagerLevel2 = managerId,
                    ReportingManagerLevel2UserRole = managerRole,
                    ReportingManagerUserRole = managerRole,
                    ReportingManagerPosition = positionCodeId,
                };
                await writableMasterDb.ManagerAlerts.AddAsync(alert);
                await writableMasterDb.SaveChangesAsync();
                alertIds.Add(alert.Id);
            }
            return alertIds[0];
        }
        else
        {
            var alert = new ManagerAlert()
            {
                UserId = managerId,
                UserRole = managerRole,
                Action = ManagerAlertAction.Pending,
                AlertServerTime = DateTime.UtcNow,
                CompanyId = companyId,
                CreatedAt = DateTime.UtcNow,
                CreationContext = $"FAEngage-{notificationId}",
                DeviceTime = DateTime.UtcNow,
                AlertType = isNotAggregated ? AlertType.FaEngageNotAggregated : AlertType.FaEngage,
                AlertTitle = title,
                AlertDescription = message,
                ReportingManager = managerId,
                ReportingManagerLevel2 = managerId,
                ReportingManagerLevel2UserRole = managerRole,
                ReportingManagerUserRole = managerRole
            };
            await writableMasterDb.ManagerAlerts.AddAsync(alert);
            await writableMasterDb.SaveChangesAsync();
            return alert.Id;
        }
    }
}