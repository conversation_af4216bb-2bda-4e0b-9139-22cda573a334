﻿using System;
using Libraries.CommonEnums;

namespace Libraries.CommonModels;

public class EntityMinGuid
{
    public Guid Id { get; set; }
}

public class CountMin
{
    public int Count { get; set; }
}

public class EntityMin
{
    public long Id { get; set; }

    public string Name { get; set; }
}

public class EntityMinWithIsDeactive : EntityMin
{
    public bool IsDeactive { get; set; }
}

public class EntityMinType : EntityMin
{
    public UserType UserType { get; set; }
}

public class EntityMinWithRankRole : EntityMin
{
    public EmployeeRank Rank { get; set; }
    public PortalUserRole Role { get; set; }
}

public class EntityMinSecondaryId : EntityMin
{
    public long? SecondaryId { get; set; }
}

public class EntityMinCount : CountMin
{
    public long Id { get; set; }
}

public class EntityMinDecimal
{
    public decimal Id { get; set; }
}

public class CountWithMaxItemMin
{
    public int Count { get; set; }
    public long? Id { get; set; }

    public long MaxAttendanceId { get; set; }
}

public class EntityMinIds
{
    public long Id1 { get; set; }

    public long Id2 { get; set; }
}

public class EntityMinInt
{
    public int Id { get; set; }

    public string Name { get; set; }
}

public class EntityMinlong
{
    public long Id { get; set; }
}

public class EntityMinMapDec
{
    public long Id { get; set; }
    public decimal Value { get; set; }
}

public class EntityMinMapDecimal
{
    public long Id { get; set; }
    public decimal Value { get; set; }
}

public class EntityMinMapLong
{
    public long Id { get; set; }
    public long Value { get; set; }
}

public class EntityNameValue
{
    public string Name { get; set; }
    public string Value { get; set; }
}

public class EntityMinWithErp : EntityMin
{
    public string ErpId { get; set; }
}

public class EntityMinWithEmployeeId
{
    public long Id { get; set; }
    public long EmployeeId { get; set; }
}
public class EntityMinWithStatus : EntityMin
{
    public bool IsActive { get; set; }
}

public class EntityMinWithStatusandUserType : EntityMin
{
    public UserType UserType { get; set; }
    public bool IsActive { get; set; }
}

public class EntityMinWithErpStatus : EntityMinWithStatus
{
    public string ErpId { get; set; }
}

public class EntityMinWithDate : EntityMinWithErp
{
    public DateTime Date { get; set; }
}

public class VanMinEntity : EntityMin
{
    public string VanName { get; set; }
    public string ChassisId { get; set; }
    public string WarehouseErpId { get; set; }
}
//TODO: Delete Below

#region Delete

public class MappingUpdateModel
{
    public long Id { get; set; }

    public UpdateMappingEntityType EntityType { get; set; }

    public long EntityId { get; set; }
    public long CompanyId { get; set; }
}

public class BeatMinEntity
{
    public long Id { get; set; }

    public string Name { get; set; }

    public string ErpId { get; set; }
    public long BeatId { get; set; }
}

public class EntityMinWithReportingManager : EntityMin
{
    public string ReportingManager { get; set; }
    public string ERPId { get; set; }
    public PortalUserRole UserRole { get; set; }
    public string Designation { get; set; }
}

public class EntityMinIncludeParent : EntityMin
{
    public long? ParentId { get; set; }

    public string ParentName { get; set; }
}

#endregion
