﻿using System.Collections.Generic;
using Libraries.CommonEnums;

namespace Library.CommonHelpers;

public static class EmployeeHelper
{
    public static bool IsValidUserRole(PortalUserRole userRole, PortalUserRole companyUsesHighestUserRole)
    {
        var validUserRoles = new List<PortalUserRole>
        {
            PortalUserRole.ClientEmployee,
            PortalUserRole.AreaSalesManager,
            PortalUserRole.RegionalSalesManager,
            PortalUserRole.ZonalSalesManager,
            PortalUserRole.NationalSalesManager,
            PortalUserRole.GlobalSalesManager
        };

        return validUserRoles.Contains(userRole) && userRole >= companyUsesHighestUserRole;
    }

    public static PortalUserRole GetParentRole(this PortalUserRole portalUserRole)
    {
        switch (portalUserRole)
        {
            case PortalUserRole.NationalSalesManager:
                return PortalUserRole.GlobalSalesManager;
            case PortalUserRole.ZonalSalesManager:
                return PortalUserRole.NationalSalesManager;
            case PortalUserRole.RegionalSalesManager:
                return PortalUserRole.ZonalSalesManager;
            case PortalUserRole.AreaSalesManager:
                return PortalUserRole.RegionalSalesManager;
            case PortalUserRole.ClientEmployee:
                return PortalUserRole.AreaSalesManager;
            default:
                return PortalUserRole.Unknown;
        }
    }
}
