using System;
using Libraries.CommonEnums;

namespace Library.CommonHelpers;

    public static class DataTypeHelper
    {
        public static bool IsNumeric(object obj)
        {
            return IsNumeric(obj.GetType());
        }
        public static bool IsNumeric(Type type)
        {
            return type.IsPrimitive && type != typeof(bool) && type != typeof(char);
        }

        public static UserType? SaleTypeToUserType(SaleType saletype)
        {
            switch (saletype)
            {
                case SaleType.All:
                    return null;
                case SaleType.PreSales:
                    return UserType.SR;
                case SaleType.VanSales:
                    return UserType.DSR;
            }

            return null;
        }
    }

