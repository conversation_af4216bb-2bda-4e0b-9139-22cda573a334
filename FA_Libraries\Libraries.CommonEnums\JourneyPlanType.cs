﻿namespace Libraries.CommonEnums;

public enum JourneyPlanType
{
    Default = 0,
    BeatPlan = 1,
    PJPFourWeekly = 2,
    PJPOpen = 3,
    TourPlan = 4,
    RoutePlan = 5
}

public enum JourneyPlanVersion
{
    OldJourneyPlan,
    NewJourneyPlan
}

public enum JourneyPlanningEntity
{
    Beat,
    Route,
    BeatOrRoute
}

public enum UserJourneyPlanningEntity
{
    InHerited,
    Beat,
    Route
}

public enum JourneyType
{
    NotCreated = 0,
    UserCreated = 1,
    AdminCreated = 2,
    UserCreatedWithoutApproval = 3
}

public enum JourneyCreationSettingType
{
    NotCreated = 0,
    UserCreated = 1,
    AdminCreated = 2,
    UserCreatedWithoutApproval = 3
}
