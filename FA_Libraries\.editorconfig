﻿; EditorConfig to support per-solution formatting.
; Use the EditorConfig VS add-in to make this work.
; http://editorconfig.org/
;
; Here are some resources for what's supported for .NET/C#
; https://kent-boogaart.com/blog/editorconfig-reference-for-c-developers
; https://learn.microsoft.com/en-us/visualstudio/ide/editorconfig-code-style-settings-reference
;
; Be **careful** editing this because some of the rules don't support adding a severity level
; For instance if you change to `dotnet_sort_system_directives_first = true:warning` (adding `:warning`)
; then the rule will be silently ignored.
; src: https://github.com/dotnet/aspnetcore/blob/main/.editorconfig

; This is the default for the codeline.
root = true

[*]
indent_style = space
charset = utf-8-bom
trim_trailing_whitespace = true
insert_final_newline = true

[*.cs]
indent_size = 4
dotnet_sort_system_directives_first = true

# Don't use this. qualifier
dotnet_style_qualification_for_field = false:suggestion
dotnet_style_qualification_for_property = false:suggestion

# use int x = .. over Int32
dotnet_style_predefined_type_for_locals_parameters_members = true:suggestion

# use int.MaxValue over Int32.MaxValue
dotnet_style_predefined_type_for_member_access = true:suggestion

# Require var all the time.
csharp_style_var_for_built_in_types = true:suggestion
csharp_style_var_when_type_is_apparent = true:suggestion
csharp_style_var_elsewhere = true:suggestion

# Disallow throw expressions.
csharp_style_throw_expression = false:suggestion

# Newline settings
csharp_new_line_before_open_brace = all
csharp_new_line_before_else = true
csharp_new_line_before_catch = true
csharp_new_line_before_finally = true
csharp_new_line_before_members_in_object_initializers = true
csharp_new_line_before_members_in_anonymous_types = true

# Namespace settings
csharp_style_namespace_declarations = file_scoped

# Brace settings
csharp_prefer_braces = true # Prefer curly braces even for one line of code

# name all constant fields using PascalCase
dotnet_naming_rule.constant_fields_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.constant_fields_should_be_pascal_case.symbols = constant_fields
dotnet_naming_rule.constant_fields_should_be_pascal_case.style = pascal_case_style
dotnet_naming_symbols.constant_fields.applicable_kinds = field
dotnet_naming_symbols.constant_fields.required_modifiers = const
dotnet_naming_style.pascal_case_style.capitalization = pascal_case

# internal and private fields should be _camelCase
dotnet_naming_rule.camel_case_for_private_internal_fields.severity = suggestion
dotnet_naming_rule.camel_case_for_private_internal_fields.symbols = private_internal_fields
dotnet_naming_rule.camel_case_for_private_internal_fields.style = camel_case_underscore_style
dotnet_naming_symbols.private_internal_fields.applicable_kinds = field
dotnet_naming_symbols.private_internal_fields.applicable_accessibilities = private, internal
dotnet_naming_style.camel_case_underscore_style.required_prefix = _
dotnet_naming_style.camel_case_underscore_style.capitalization = camel_case

[*.{xml,config,*proj,nuspec,props,resx,targets,yml,tasks}]
indent_size = 2

# Xml config files
[*.{props,targets,ruleset,config,nuspec,resx,vsixmanifest,vsct}]
indent_size = 2

[*.json]
indent_size = 2

[*.{ps1,psm1}]
indent_size = 4

[*.sh]
indent_size = 4
end_of_line = lf

[*.{razor,cshtml}]
charset = utf-8-bom

[*.{cs,vb}]

# SYSLIB1054: Use 'LibraryImportAttribute' instead of 'DllImportAttribute' to generate P/Invoke marshalling code at compile time
dotnet_diagnostic.SYSLIB1054.severity = warning

# CA1018: Mark attributes with AttributeUsageAttribute
dotnet_diagnostic.CA1018.severity = warning

# CA1047: Do not declare protected member in sealed type
dotnet_diagnostic.CA1047.severity = warning

# CA1305: Specify IFormatProvider
dotnet_diagnostic.CA1305.severity = warning

# CA1507: Use nameof to express symbol names
dotnet_diagnostic.CA1507.severity = warning

# CA1510: Use ArgumentNullException throw helper
dotnet_diagnostic.CA1510.severity = warning

# CA1511: Use ArgumentException throw helper
dotnet_diagnostic.CA1511.severity = warning

# CA1512: Use ArgumentOutOfRangeException throw helper
dotnet_diagnostic.CA1512.severity = warning

# CA1513: Use ObjectDisposedException throw helper
dotnet_diagnostic.CA1513.severity = warning

# CA1725: Parameter names should match base declaration
dotnet_diagnostic.CA1725.severity = suggestion

# CA1802: Use literals where appropriate
dotnet_diagnostic.CA1802.severity = warning

# CA1805: Do not initialize unnecessarily
dotnet_diagnostic.CA1805.severity = warning

# CA1810: Do not initialize unnecessarily
dotnet_diagnostic.CA1810.severity = warning

# CA1821: Remove empty Finalizers
dotnet_diagnostic.CA1821.severity = warning

# CA1822: Make member static
dotnet_diagnostic.CA1822.severity = warning
dotnet_code_quality.CA1822.api_surface = private, internal

# CA1823: Avoid unused private fields
dotnet_diagnostic.CA1823.severity = warning

# CA1825: Avoid zero-length array allocations
dotnet_diagnostic.CA1825.severity = warning

# CA1826: Do not use Enumerable methods on indexable collections. Instead use the collection directly
dotnet_diagnostic.CA1826.severity = suggestion

# CA1827: Do not use Count() or LongCount() when Any() can be used
dotnet_diagnostic.CA1827.severity = warning

# CA1828: Do not use CountAsync() or LongCountAsync() when AnyAsync() can be used
dotnet_diagnostic.CA1828.severity = warning

# CA1829: Use Length/Count property instead of Count() when available
dotnet_diagnostic.CA1829.severity = warning

# CA1830: Prefer strongly-typed Append and Insert method overloads on StringBuilder
dotnet_diagnostic.CA1830.severity = warning

# CA1831: Use AsSpan or AsMemory instead of Range-based indexers when appropriate
dotnet_diagnostic.CA1831.severity = warning

# CA1832: Use AsSpan or AsMemory instead of Range-based indexers when appropriate
dotnet_diagnostic.CA1832.severity = warning

# CA1833: Use AsSpan or AsMemory instead of Range-based indexers when appropriate
dotnet_diagnostic.CA1833.severity = warning

# CA1834: Consider using 'StringBuilder.Append(char)' when applicable
dotnet_diagnostic.CA1834.severity = warning

# CA1835: Prefer the 'Memory'-based overloads for 'ReadAsync' and 'WriteAsync'
dotnet_diagnostic.CA1835.severity = warning

# CA1836: Prefer IsEmpty over Count
dotnet_diagnostic.CA1836.severity = warning

# CA1837: Use 'Environment.ProcessId'
dotnet_diagnostic.CA1837.severity = warning

# CA1838: Avoid 'StringBuilder' parameters for P/Invokes
dotnet_diagnostic.CA1838.severity = warning

# CA1839: Use 'Environment.ProcessPath'
dotnet_diagnostic.CA1839.severity = warning

# CA1840: Use 'Environment.CurrentManagedThreadId'
dotnet_diagnostic.CA1840.severity = warning

# CA1841: Prefer Dictionary.Contains methods
dotnet_diagnostic.CA1841.severity = warning

# CA1842: Do not use 'WhenAll' with a single task
dotnet_diagnostic.CA1842.severity = warning

# CA1843: Do not use 'WaitAll' with a single task
dotnet_diagnostic.CA1843.severity = warning

# CA1844: Provide memory-based overrides of async methods when subclassing 'Stream'
dotnet_diagnostic.CA1844.severity = warning

# CA1845: Use span-based 'string.Concat'
dotnet_diagnostic.CA1845.severity = warning

# CA1846: Prefer AsSpan over Substring
dotnet_diagnostic.CA1846.severity = warning

# CA1847: Use string.Contains(char) instead of string.Contains(string) with single characters
dotnet_diagnostic.CA1847.severity = warning

# CA1852: Seal internal types
dotnet_diagnostic.CA1852.severity = warning

# CA1854: Prefer the IDictionary.TryGetValue(TKey, out TValue) method
dotnet_diagnostic.CA1854.severity = warning

# CA1855: Prefer 'Clear' over 'Fill'
dotnet_diagnostic.CA1855.severity = warning

# CA1856: Incorrect usage of ConstantExpected attribute
dotnet_diagnostic.CA1856.severity = error

# CA1857: A constant is expected for the parameter
dotnet_diagnostic.CA1857.severity = warning

# CA1858: Use 'StartsWith' instead of 'IndexOf'
dotnet_diagnostic.CA1858.severity = warning

# CA2007: Consider calling ConfigureAwait on the awaited task
dotnet_diagnostic.CA2007.severity = warning

# CA2008: Do not create tasks without passing a TaskScheduler
dotnet_diagnostic.CA2008.severity = warning

# CA2009: Do not call ToImmutableCollection on an ImmutableCollection value
dotnet_diagnostic.CA2009.severity = warning

# CA2011: Avoid infinite recursion
dotnet_diagnostic.CA2011.severity = warning

# CA2012: Use ValueTask correctly
dotnet_diagnostic.CA2012.severity = warning

# CA2013: Do not use ReferenceEquals with value types
dotnet_diagnostic.CA2013.severity = warning

# CA2014: Do not use stackalloc in loops.
dotnet_diagnostic.CA2014.severity = warning

# CA2016: Forward the 'CancellationToken' parameter to methods that take one
dotnet_diagnostic.CA2016.severity = warning

# CA2022: Avoid inexact read with `Stream.Read`
dotnet_diagnostic.CA2022.severity = warning

# CA2200: Rethrow to preserve stack details
dotnet_diagnostic.CA2200.severity = warning

# CA2201: Do not raise reserved exception types
dotnet_diagnostic.CA2201.severity = warning

# CA2208: Instantiate argument exceptions correctly
dotnet_diagnostic.CA2208.severity = warning

# CA2245: Do not assign a property to itself
dotnet_diagnostic.CA2245.severity = warning

# CA2246: Assigning symbol and its member in the same statement
dotnet_diagnostic.CA2246.severity = warning

# CA2249: Use string.Contains instead of string.IndexOf to improve readability.
dotnet_diagnostic.CA2249.severity = warning

# IDE0005: Remove unnecessary usings
dotnet_diagnostic.IDE0005.severity = warning

# IDE0011: Curly braces to surround blocks of code
dotnet_diagnostic.IDE0011.severity = warning

# IDE0020: Use pattern matching to avoid is check followed by a cast (with variable)
dotnet_diagnostic.IDE0020.severity = warning

# IDE0029: Use coalesce expression (non-nullable types)
dotnet_diagnostic.IDE0029.severity = warning

# IDE0030: Use coalesce expression (nullable types)
dotnet_diagnostic.IDE0030.severity = warning

# IDE0031: Use null propagation
dotnet_diagnostic.IDE0031.severity = warning

# IDE0035: Remove unreachable code
dotnet_diagnostic.IDE0035.severity = warning

# IDE0036: Order modifiers
csharp_preferred_modifier_order = public, private, protected, internal, static, extern, new, virtual, abstract, sealed, override, readonly, unsafe, volatile, async:suggestion
dotnet_diagnostic.IDE0036.severity = warning

# IDE0038: Use pattern matching to avoid is check followed by a cast (without variable)
dotnet_diagnostic.IDE0038.severity = warning

# IDE0043: Format string contains invalid placeholder
dotnet_diagnostic.IDE0043.severity = warning

# IDE0044: Make field readonly
dotnet_diagnostic.IDE0044.severity = warning

# IDE0051: Remove unused private members
dotnet_diagnostic.IDE0051.severity = warning

# IDE0055: All formatting rules
dotnet_diagnostic.IDE0055.severity = suggestion

# IDE0059: Unnecessary assignment to a value
dotnet_diagnostic.IDE0059.severity = warning

# IDE0060: Remove unused parameter
dotnet_code_quality_unused_parameters = non_public
dotnet_diagnostic.IDE0060.severity = warning

# IDE0062: Make local function static
dotnet_diagnostic.IDE0062.severity = warning

# IDE0073: File header
dotnet_diagnostic.IDE0073.severity = warning
file_header_template = Licensed to the.NET Foundation under one or more agreements.\nThe.NET Foundation licenses this file to you under the MIT license.

# IDE0161: Convert to file-scoped namespace
dotnet_diagnostic.IDE0161.severity = warning

# IDE0200: Lambda expression can be removed
dotnet_diagnostic.IDE0200.severity = warning

# IDE2000: Disallow multiple blank lines
dotnet_style_allow_multiple_blank_lines_experimental = false
dotnet_diagnostic.IDE2000.severity = warning

[{eng/tools/**.cs,**/{test,testassets,samples,Samples,perf,benchmarkapps,scripts,stress}/**.cs,src/Hosting/Server.IntegrationTesting/**.cs,src/Servers/IIS/IntegrationTesting.IIS/**.cs,src/Shared/Http2cat/**.cs,src/Testing/**.cs}]
# CA1018: Mark attributes with AttributeUsageAttribute
dotnet_diagnostic.CA1018.severity = suggestion
# CA1507: Use nameof to express symbol names
dotnet_diagnostic.CA1507.severity = suggestion
# CA1510: Use ArgumentNullException throw helper
dotnet_diagnostic.CA1510.severity = suggestion
# CA1511: Use ArgumentException throw helper
dotnet_diagnostic.CA1511.severity = suggestion
# CA1512: Use ArgumentOutOfRangeException throw helper
dotnet_diagnostic.CA1512.severity = suggestion
# CA1513: Use ObjectDisposedException throw helper
dotnet_diagnostic.CA1513.severity = suggestion
# CA1802: Use literals where appropriate
dotnet_diagnostic.CA1802.severity = suggestion
# CA1805: Do not initialize unnecessarily
dotnet_diagnostic.CA1805.severity = suggestion
# CA1810: Do not initialize unnecessarily
dotnet_diagnostic.CA1810.severity = suggestion
# CA1822: Make member static
dotnet_diagnostic.CA1822.severity = suggestion
# CA1823: Avoid zero-length array allocations
dotnet_diagnostic.CA1825.severity = suggestion
# CA1826: Do not use Enumerable methods on indexable collections. Instead use the collection directly
dotnet_diagnostic.CA1826.severity = suggestion
# CA1827: Do not use Count() or LongCount() when Any() can be used
dotnet_diagnostic.CA1827.severity = suggestion
# CA1829: Use Length/Count property instead of Count() when available
dotnet_diagnostic.CA1829.severity = suggestion
# CA1831: Use AsSpan or AsMemory instead of Range-based indexers when appropriate
dotnet_diagnostic.CA1831.severity = suggestion
# CA1832: Use AsSpan or AsMemory instead of Range-based indexers when appropriate
dotnet_diagnostic.CA1832.severity = suggestion
# CA1833: Use AsSpan or AsMemory instead of Range-based indexers when appropriate
dotnet_diagnostic.CA1833.severity = suggestion
# CA1834: Consider using 'StringBuilder.Append(char)' when applicable
dotnet_diagnostic.CA1834.severity = suggestion
# CA1835: Prefer the 'Memory'-based overloads for 'ReadAsync' and 'WriteAsync'
dotnet_diagnostic.CA1835.severity = suggestion
# CA1837: Use 'Environment.ProcessId'
dotnet_diagnostic.CA1837.severity = suggestion
# CA1838: Avoid 'StringBuilder' parameters for P/Invokes
dotnet_diagnostic.CA1838.severity = suggestion
# CA1841: Prefer Dictionary.Contains methods
dotnet_diagnostic.CA1841.severity = suggestion
# CA1844: Provide memory-based overrides of async methods when subclassing 'Stream'
dotnet_diagnostic.CA1844.severity = suggestion
# CA1845: Use span-based 'string.Concat'
dotnet_diagnostic.CA1845.severity = suggestion
# CA1846: Prefer AsSpan over Substring
dotnet_diagnostic.CA1846.severity = suggestion
# CA1847: Use string.Contains(char) instead of string.Contains(string) with single characters
dotnet_diagnostic.CA1847.severity = suggestion
# CA1852: Seal internal types
dotnet_diagnostic.CA1852.severity = suggestion
# CA1854: Prefer the IDictionary.TryGetValue(TKey, out TValue) method
dotnet_diagnostic.CA1854.severity = suggestion
# CA1855: Prefer 'Clear' over 'Fill'
dotnet_diagnostic.CA1855.severity = suggestion
# CA1856: Incorrect usage of ConstantExpected attribute
dotnet_diagnostic.CA1856.severity = suggestion
# CA1857: A constant is expected for the parameter
dotnet_diagnostic.CA1857.severity = suggestion
# CA1858: Use 'StartsWith' instead of 'IndexOf'
dotnet_diagnostic.CA1858.severity = suggestion
# CA2007: Consider calling ConfigureAwait on the awaited task
dotnet_diagnostic.CA2007.severity = suggestion
# CA2008: Do not create tasks without passing a TaskScheduler
dotnet_diagnostic.CA2008.severity = suggestion
# CA2012: Use ValueTask correctly
dotnet_diagnostic.CA2012.severity = suggestion
# CA2022: Avoid inexact read with `Stream.Read`
dotnet_diagnostic.CA2022.severity = suggestion
# CA2201: Do not raise reserved exception types
dotnet_diagnostic.CA2201.severity = suggestion
# CA2249: Use string.Contains instead of string.IndexOf to improve readability.
dotnet_diagnostic.CA2249.severity = suggestion
# IDE0005: Remove unnecessary usings
dotnet_diagnostic.IDE0005.severity = suggestion
# IDE0020: Use pattern matching to avoid is check followed by a cast (with variable)
dotnet_diagnostic.IDE0020.severity = suggestion
# IDE0029: Use coalesce expression (non-nullable types)
dotnet_diagnostic.IDE0029.severity = suggestion
# IDE0030: Use coalesce expression (nullable types)
dotnet_diagnostic.IDE0030.severity = suggestion
# IDE0031: Use null propagation
dotnet_diagnostic.IDE0031.severity = suggestion
# IDE0038: Use pattern matching to avoid is check followed by a cast (without variable)
dotnet_diagnostic.IDE0038.severity = suggestion
# IDE0044: Make field readonly
dotnet_diagnostic.IDE0044.severity = suggestion
# IDE0051: Remove unused private members
dotnet_diagnostic.IDE0051.severity = suggestion
# IDE0059: Unnecessary assignment to a value
dotnet_diagnostic.IDE0059.severity = suggestion
# IDE0060: Remove unused parameters
dotnet_diagnostic.IDE0060.severity = suggestion
# IDE0062: Make local function static
dotnet_diagnostic.IDE0062.severity = suggestion
# IDE0200: Lambda expression can be removed
dotnet_diagnostic.IDE0200.severity = suggestion

# CA2016: Forward the 'CancellationToken' parameter to methods that take one
dotnet_diagnostic.CA2016.severity = suggestion

# Defaults for content in the shared src/ and shared runtime dir

[{**/Shared/runtime/**.{cs,vb},src/Shared/test/Shared.Tests/runtime/**.{cs,vb},**/microsoft.extensions.hostfactoryresolver.sources/**.{cs,vb}}]
# CA1822: Make member static
dotnet_diagnostic.CA1822.severity = silent
# IDE0011: Use braces
dotnet_diagnostic.IDE0011.severity = silent
# IDE0055: Fix formatting
dotnet_diagnostic.IDE0055.severity = silent
# IDE0060: Remove unused parameters
dotnet_diagnostic.IDE0060.severity = silent
# IDE0062: Make local function static
dotnet_diagnostic.IDE0062.severity = silent
# IDE0161: Convert to file-scoped namespace
dotnet_diagnostic.IDE0161.severity = silent

[{**/Shared/**.cs,**/microsoft.extensions.hostfactoryresolver.sources/**.{cs,vb}}]
# IDE0005: Remove unused usings. Ignore for shared src files since imports for those depend on the projects in which they are included.
dotnet_diagnostic.IDE0005.severity = silent