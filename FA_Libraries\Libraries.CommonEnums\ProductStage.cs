﻿namespace Libraries.CommonEnums;

public enum ProductStage
{
    /// <summary>
    ///     SKU which is a �New launch� and has been launched in all geographies with all the Distributors pan company
    /// </summary>
    Available = 0,

    /// <summary>
    ///     SKU is a �New launch� which are launched with selective Distributors described in Product ID and Distributor ID mapping
    /// </summary>
    NewLaunch = 1,

    /// <summary>
    ///     SKU which is no more manufactured but might be available with certain Distributors.
    /// </summary>
    Discontinued = 2
}

public enum PricingMastertype
{
    NotDefined = 0,
    Default = 1,
    Regional = 2,
    Stockist = 3,
    StockistCategory = 4
}
