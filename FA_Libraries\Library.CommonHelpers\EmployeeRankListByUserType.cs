﻿using System.Collections.Generic;
using Libraries.CommonEnums;

namespace Library.CommonHelpers;

public static class EmployeeRankListByUserType
{
    public static List<string> GetEmployeeRanks(FieldUserType fieldUserType)
    {
        var result = new List<string>();
        switch (fieldUserType)
        {
            case FieldUserType.All:
                result.Add(EmployeeRank.ESM.ToString());
                result.Add(EmployeeRank.ASM.ToString());
                result.Add(EmployeeRank.RSM.ToString());
                result.Add(EmployeeRank.ZSM.ToString());
                result.Add(EmployeeRank.NSM.ToString());
                result.Add(EmployeeRank.GSM.ToString());
                break;

            case FieldUserType.ESM:
                result.Add(EmployeeRank.ESM.ToString());
                break;

            case FieldUserType.Managers:
                result.Add(EmployeeRank.ASM.ToString());
                result.Add(EmployeeRank.RSM.ToString());
                result.Add(EmployeeRank.ZSM.ToString());
                result.Add(EmployeeRank.NSM.ToString());
                result.Add(EmployeeRank.GSM.ToString());
                break;
        }

        return result;
    }

    public static List<int> GetEmployeeRanksInt(FieldUserType fieldUserType)
    {
        var result = new List<int>();
        switch (fieldUserType)
        {
            case FieldUserType.All:
                result.Add((int)EmployeeRank.ESM);
                result.Add((int)EmployeeRank.ASM);
                result.Add((int)EmployeeRank.RSM);
                result.Add((int)EmployeeRank.ZSM);
                result.Add((int)EmployeeRank.NSM);
                result.Add((int)EmployeeRank.GSM);
                break;

            case FieldUserType.ESM:
                result.Add((int)EmployeeRank.ESM);
                break;

            case FieldUserType.Managers:
                result.Add((int)EmployeeRank.ASM);
                result.Add((int)EmployeeRank.RSM);
                result.Add((int)EmployeeRank.ZSM);
                result.Add((int)EmployeeRank.NSM);
                result.Add((int)EmployeeRank.GSM);
                break;
        }

        return result;
    }
}
