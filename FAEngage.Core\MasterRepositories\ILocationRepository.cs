﻿using FAEngage.Core.Models;
using FAEngage.Core.Models.MasterDbModels;

namespace FAEngage.Core.MasterRepositories
{
    public interface ILocationRepository
    {
        Task<List<LocationDto>> GetLocationsForCompany(long companyId);
        Task<List<TheShopType>> GetShopTypes(long companyId);
        Task<List<FATradeUser>> GetFATradeUserForOwnersNumber(List<string> ownerNos);
        Task<List<FATradeToken>> GetFATradeTokens(List<long> userIds, long companyId);
    }
}
