﻿using Libraries.CommonEnums;

namespace FAEngage.Core.Models.MasterDbModels;

public class ClientEmployee
{
    public long Id { get; set; }
    public Guid GUID { get; set; }
    public string Name { get; set; }
    public bool IsDeactive { get; set; }
    public bool Deleted { set; get; }
    public long Company { get; set; }
    public string ClientSideId { get; set; }
    public long? ParentId { get; set; }
    public PortalUserRole UserRole { set; get; }
    public EmployeeRank Rank { get; set; }
    public long? OldTableId { get; set; }
    public string LocalName { get; set; }
    public string ContactNo { get; set; }
    public ClientEmployee Parent { get; set; }
    public long? RegionId { get; set; }
    public bool IsFieldAppuser { get; set; }
    public DateTime? DateOfJoining { set; get; }
    public Region Region { get; set; }
    public bool IsTrainingUser { get; set; }
    public bool IsOrderBookingDisabled { get; set; }
    public string EmailId { get; set; }
    public long? KRATagId { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public EmployeeType EmployeeType { get; set; }
    public long? DesignationId { get; set; }
    public Designation Designations { get; set; }
    public string UserProfilePicture { get; set; }
    public long? AreaSalesManagerId { get; set; }
    public string EmployeeAttributeText1 { get; set; }
    public string EmployeeAttributeText2 { get; set; }
    public bool? EmployeeAttributeBoolean1 { get; set; }
    public bool? EmployeeAttributeBoolean2 { get; set; }
    public double? EmployeeAttributeNumber1 { get; set; }
    public double? EmployeeAttributeNumber2 { get; set; }
    public DateTime? EmployeeAttributeDate { get; set; }
    public string Zone { get; set; }
    public string SecondaryEmailId { get; set; }
    public ClientEmployee ShallowCopy()
    {
        return (ClientEmployee)this.MemberwiseClone();
    }
}