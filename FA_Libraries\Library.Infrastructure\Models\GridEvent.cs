﻿using System;

namespace Library.Infrastructure.Models;

public class QueueEvent<T>
{
    public QueueEvent()
    {
    }

    public QueueEvent(T data, DateTime eventTime, string id, string source)
    {
        Data = data;
        EventTime = eventTime;
        Id = id;
        Source = source;
    }

    public T Data { get; set; }
    public DateTime EventTime { get; set; }
    public string Id { get; set; }
    public string Source { get; set; }
}

public class GridEvent<T> : QueueEvent<T>
{
    public GridEvent()
    {
    }

    public GridEvent(T data, DateTime eventTime, string eventType, string id, string subject,
        string source,
        bool forceResyncData)
    {
        Data = data;
        EventTime = eventTime;
        EventType = eventType;
        Id = id;
        Subject = subject;
        Source = source;
        ForceResyncData = forceResyncData;
    }

    public string EventType { get; set; }
    public string Subject { get; set; }
    public bool ForceResyncData { get; set; }

    public static GridEvent<T> FromData<T>(T data, string id = null, string queueSource = "Unknown",
        bool forceResyncData = false)
    {
        return new GridEvent<T>
        {
            Data = data,
            EventTime = DateTime.UtcNow,
            Id = id ?? new Guid().ToString(),
            Source = queueSource,
            ForceResyncData = forceResyncData
        };
    }
}
