﻿using System;
using System.Text;
using System.Threading.Tasks;
using FAEngage.Core.Interfaces;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;

namespace FAEngage
{
    public class InMemoryCacheProvider : ICacheProvider
    {
        private readonly IMemoryCache cache;

        public InMemoryCacheProvider(IMemoryCache cache)
        {
            this.cache = cache;
        }

        /// <summary>
        ///     does not support expiration
        /// </summary>
        /// <param name="cacheKey"></param>
        /// <param name="value"></param>
        /// <param name="expiresIn">expiration</param>
        public void Insert<T>(string cacheKey, T value, TimeSpan expiresIn)
        {
            cache.Set(cacheKey, value, expiresIn);
        }

        public bool TryGet<T>(string cacheKey, out T result)
        {
            result = default;
            if (cache.TryGetValue(cacheKey, out var v))
                if (v is T t)
                {
                    result = t;
                    return true;
                }

            return false;
        }

        public Task<T> GetAsJsonAsync<T>(string key, Encoding encoding = null)
        {
            throw new NotImplementedException();
        }

        public Task SetAsJsonAsync<T>(string key, T value, Encoding encoding = null, DistributedCacheEntryOptions options = null)
        {
            throw new NotImplementedException();
        }
    }
}