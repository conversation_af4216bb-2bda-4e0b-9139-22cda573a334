﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using Libraries.CommonEnums;

namespace FAEngage.Core.Models.MasterDbModels
{
    public class EngageNotification
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public long CompanyId { get; set; }
        public NotificationCondition NotificationType { get; set; }
        public bool Deleted { get; set; }
        public UserPlatform UserPlatform { get; set; }
        public string CohortIds { get; set; }
        public string Cron { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public Guid Guid { get; set; }
        public bool IsAggregated { get; set; }

        public string AggregationCron { get; set; }

        // this field in minutes
        public int? AggregationDelay { get; set; }
        public string ManagerLevel { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreationContext { get; set; }
        public DateTime LastUpdatedAt { get; set; }

        [NotMapped]
        public List<long> CohortIdsLong => string.IsNullOrWhiteSpace(CohortIds) ? [] : JsonSerializer.Deserialize<List<long>>(CohortIds);

        [NotMapped]
        public List<PortalUserRole?> ManagerLevelRoles => string.IsNullOrWhiteSpace(ManagerLevel) ? [] : JsonSerializer.Deserialize<List<PortalUserRole?>>(ManagerLevel);

        [NotMapped]
        public PortalUserRole? ManagerLevelRole => ManagerLevelRoles.First();
    }

    public class NotificationMessage
    {
        public long Id { get; set; }
        public long NotificationId { get; set; }
        public long CompanyId { get; set; }
        public UserPlatform NotificationApp { get; set; }
        public string Title { get; set; }
        public AppNotificationType? NotificationType { get; set; }
        public int? AppScreen { get; set; }
        public string MessageText { get; set; }
        public string Uri { get; set; }
        public int? OnClickScreen { get; set; }
        public string Emoji { get; set; }
        public bool IsAggregated { get; set; }
        public bool IsDeactive { get; set; }
        public string TemplateID { get; set; }
        public string WhatsAppVariables { get; set; }
        public bool ForWhatsapp { get; set; }
        public string? Image { get; set; }
        [NotMapped]
        public List<string> WhatsAppVariablesList => JsonSerializer.Deserialize<List<string>>(WhatsAppVariables);

        [NotMapped]
        public List<string> MessageTestList => JsonSerializer.Deserialize<List<string>>(MessageText);
    }

    public class KPITriggers
    {
        public long Id { get; set; }
        public long KpiId { get; set; }
        public ComparisonOperator ComparisonOperator { get; set; }
        public string Value { get; set; }
        public long NotificationId { get; set; }
        public long CompanyId { get; set; }
        public bool IsDeactive { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreationContext { get; set; }
        public DateTime LastUpdatedAt { get; set; }
    }

    public class Cohort
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public long CompanyId { get; set; }
        public bool Deleted { get; set; }
        public UserPlatform UserPlatform { get; set; }
        public string ExtraInfoJson { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreationContext { get; set; }
        public DateTime LastUpdatedAt { get; set; }

        [NotMapped]
        public ExtraInfoJson ExtraInfoJsonModel => JsonSerializer.Deserialize<ExtraInfoJson>(ExtraInfoJson);
    }


    [NotMapped]
    public class ExtraInfoJson
    {
        public List<UserInfo> UserInfos { get; set; }
        public List<PositionInfo> PositionInfos { get; set; }
        public List<PositionCodeLevel> PositionCodeLevels { get; set; }
        public List<EmployeeRank> EmployeeRanks { get; set; }
        public List<EmployeeType> EmployeeTypes { get; set; }
        public List<long> ProductDivisions { get; set; }
        public List<GeographyInfo> GeographyInfos { get; set; }
        public OutletAttributes OutletAttributes { get; set; }
    }

    [NotMapped]
    public class PositionInfo
    {
        public List<long> PositionId { get; set; }
        public PositionCodeLevel Level { get; set; }
    }

    [NotMapped]
    public class UserInfo
    {
        public List<long> Id { get; set; }
        public EmployeeRank Rank { get; set; }
    }

    [NotMapped]
    public class GeographyInfo
    {
        public List<long> GeographyIds { get; set; }
        public GeographyHierarchy Level { get; set; }
    }

    [NotMapped]
    public class OutletAttributes
    {
        public List<OutletChannel> OutletChannel { get; set; }
        public List<string> OutletShopType { get; set; }
        public List<OutletSegmentation> OutletSegmentation { get; set; }
        public bool IsFocused { get; set; }
    }
}