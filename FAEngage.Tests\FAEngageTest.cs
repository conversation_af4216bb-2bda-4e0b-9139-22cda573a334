﻿using Microsoft.Extensions.DependencyInjection;

namespace FAEngage.Tests
{
    [TestClass]
    public class FaEngageTest
    {
        private ServiceProvider? _serviceProvider;

        [TestInitialize]
        public void Initialize()
        {
            //Environment.SetEnvironmentVariable("BuildEnvironment", "ManageTesting");
            //Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3ManageReadOnly.vault.azure.net/");
            Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3ManageWritable.vault.azure.net/");
            var configuration = Configuration.GetConfiguration();

            IServiceCollection serviceCollection = new ServiceCollection();
            Configurations.Dependencies.SetUp(serviceCollection, configuration);
            _serviceProvider = serviceCollection.BuildServiceProvider();
        }

        [TestMethod]
        public async Task Process()
        {
            if (_serviceProvider != null)
            {
                var repo = _serviceProvider.GetRequiredService<INotificationProcessor>();
                var date = new DateTime(2023, 3, 16, 11, 32, 1);
                await repo.ProcessUserApp(date);
            }
        }

        [TestMethod]
        public async Task ProcessNotification()
        {
            if (_serviceProvider != null)
            {
                var repo = _serviceProvider.GetRequiredService<INotificationProcessor>();
                var companyId = 203267;
                var notificationId = 41511;
                await repo.ProcessNotification(notificationId, companyId);
            }
        }

        [TestMethod]
        public async Task ProcessUserApp()
        {
            if (_serviceProvider != null)
            {
                var repo = _serviceProvider.GetRequiredService<INotificationProcessor>();
                var date = DateTime.Now;
                await repo.ProcessUserApp(date);
            }
        }

        [TestMethod]
        public async Task ProcessNotificationAggregated()
        {
            if (_serviceProvider != null)
            {
                var repo = _serviceProvider.GetRequiredService<INotificationProcessor>();
                var companyId = 172305;
                var notificationId = 20618;
                await repo.ProcessNotificationAggregated(notificationId, companyId);
            }
        }

        [TestMethod]
        public async Task ProcessEmployeeNotification()
        {
            if (_serviceProvider != null)
            {
                var repo = _serviceProvider.GetRequiredService<INotificationProcessor>();
                var companyId = 10408;
                var notificationId = 84;
                long userId = 85302;
                Guid transactionId = Guid.NewGuid();
                await repo.ProcessEmployeeNotification(notificationId, companyId, userId, transactionId, null);
            }
        }

        [TestMethod]
        public async Task ProcessMessageNotification()
        {
            if (_serviceProvider != null)
            {
                var repo = _serviceProvider.GetRequiredService<INotificationProcessor>();
                var companyId = 10185;
                var notificationId = 146;
                long userId = 70018;
                Guid transactionId = Guid.NewGuid();
                await repo.ProcessMessageNotification(notificationId, companyId, userId, transactionId, null, null, null, null, null);
            }
        }
    }
}