﻿namespace Libraries.CommonEnums;

public enum DistributorGeographicalHierarchy
{
    Zone = 10,
    Region = 20,
    Distributor = 30
}

public enum EmployeeGeographicalHierarchy
{
    Zone = 10,
    Region = 20,
    AreaSalesManager = 30,
    ClientEmployee = 40
}

public enum OpeningClosingDistributor
{
    Stdunit = 0,
    Revenue = 1,
    Both = 2
}

public enum PSODShowDataForEnum
{
    All = 0,
    OnlyProductsThatWereSold = 1,
    OnlyProductiveCalls = 2
}

public enum PSODShowDataFor
{
    All = 0,
    OnlyProductsThatWereSold = 1,
    OnlyProductiveCalls = 2
}

public enum SKUWiseOutletReportEnum
{
    BeatWise = 0,
    OutletWise = 1
}
