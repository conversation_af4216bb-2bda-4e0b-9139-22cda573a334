﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <OutputType>Library</OutputType>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Flurl.Http" Version="4.0.2" />
    <PackageReference Include="NewtonSoft.json" Version="13.0.3" />
    <PackageReference Include="Polly" Version="8.6.2" />
    <PackageReference Include="Polly.Contrib.WaitAndRetry" Version="1.1.1" />
    <PackageReference Include="Polly.Extensions.Http" Version="3.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Libraries.CommonModels\Libraries.CommonModels.csproj" />
  </ItemGroup>

</Project>
