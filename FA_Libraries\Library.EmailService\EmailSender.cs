﻿using System;
using System.Threading.Tasks;
using Library.EmailService.Interface;
using Library.Infrastructure.QueueService;

namespace Library.EmailService;

public class EmailSender
{
    private readonly QueueHandler<EmailMessage> emailClient;
    private readonly string fromDisplayName;
    private readonly string fromEmail;
    private readonly string masterStorageConnString;

    public EmailSender(string masterStorageConnString) : this("<EMAIL>", "Field Assist", masterStorageConnString)
    {
    }

    public EmailSender(string masterStorageConnString, QueueType queuetype) : this("<EMAIL>", "Field Assist", masterStorageConnString, queuetype)
    {
    }

    public EmailSender(string fromEmail, string fromDisplayName, string masterStorageConnString, QueueType queueType = QueueType.SendEmail)
    {
        this.fromDisplayName = fromDisplayName;
        this.masterStorageConnString = masterStorageConnString;
        this.fromEmail = fromEmail;
        emailClient = new QueueHandler<EmailMessage>(queueType, masterStorageConnString);
    }

    public async Task Send(string to, string subject, string body, bool isHTML = false, string refId = null, string eventSubject = "")
    {
        var message = new EmailMessage
        {
            Message = body,
            Subject = subject,
            FromEmail = fromEmail,
            FromName = fromDisplayName,
            IsHTML = true,
            To = to
            // Bcc = "<EMAIL>"
        };
        await Send(message, refId).ConfigureAwait(false);
    }

    public async Task Send(EmailMessage emailMessage, string refId = null)
    {
        emailMessage.FromEmail = fromEmail;
        emailMessage.FromName = fromDisplayName;

        refId ??= Guid.NewGuid().ToString();
        await emailMessage.VerifyContent(masterStorageConnString).ConfigureAwait(false);
        await emailClient.AddToGridQueue(refId, emailMessage).ConfigureAwait(false);
    }
}
