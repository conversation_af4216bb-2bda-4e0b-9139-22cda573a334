﻿using Libraries.CommonEnums;

namespace FAEngage.Core
{
    public class PositionCodeHierarchy
    {
        public long? PositionLevel1 { get; set; }
        public DateTime CreatedAt { get; set; }
        public long CompanyId { get; set; }
        public long? PositionLevel2 { get; set; }
        public long? PositionLevel3 { get; set; }
        public long? PositionLevel4 { get; set; }
        public long? PositionLevel5 { get; set; }
        public long? PositionLevel6 { get; set; }
        public long? PositionLevel7 { get; set; }
        public long? PositionLevel8 { get; set; }
        public long? ZoneId { get; set; }
        public long? RegionId { get; set; }
        public long? GeographyLevel5 { get; set; }
        public long? GeographyLevel6 { get; set; }
        public long? GeographyLevel7 { get; set; }
        public long Id { get; set; }
        public PositionCodeLevel? PositionCodeLevel { get; set; }
        public long? PositionLevel1UserId { get; set; }
        public long? PositionLevel2UserId { get; set; }
        public long? PositionLevel3UserId { get; set; }
        public long? PositionLevel4UserId { get; set; }
        public long? PositionLevel5UserId { get; set; }
        public long? PositionLevel6UserId { get; set; }
        public long? PositionLevel7UserId { get; set; }
        public long? PositionLevel8UserId { get; set; }
    }
}
