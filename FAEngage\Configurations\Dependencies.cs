﻿using System;
using FAEngage.Core.Helpers;
using FAEngage.Core.Interfaces;
using FAEngage.Core.MasterRepositories;
using FAEngage.Core.ReportRepositories;
using FAEngage.Core.Services;
using FAEngage.Core.Services.MasterServices;
using FAEngage.Core.TransactionRepositories;
using FAEngage.DbStorage.DbContexts;
using FAEngage.DbStorage.MasterRepositories;
using FAEngage.DbStorage.ReportRepositories;
using FAEngage.DbStorage.TransactionRepositories;
using Library.Infrastructure.QueueService;
using Library.ResiliencyHelpers;
using Library.SlackService;
using Library.StorageWriter.Reader_Writer;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace FAEngage.Configurations
{
    public static class Dependencies
    {
        public const string NotificationMainQueue = "engagenotificationqueue";
        public const string NotificationAggregatedQueue = "engageaggregatenotificationqueue";
        public const string NotificationEmployeeQueue = "engageempnotificationqueue";
        public const string NotificationMessageQueue = "engagemessagenotificationqueue";
        public const string TradeAppNotificationQueue = "tradeapp-fcm-notification-queue";
        public const string AppNotificationQueue = "app-notification-queue";
        public const string EngageLogQueue = "engage-logs";


        private static string storageConnectionString;

        public static void SetUp(IServiceCollection services, IConfiguration config)
        {
            services.AddDbContext<MasterDbContext>(options =>
                options.UseSqlServer(config.GetConnectionString("MasterDbConnectionString"),
                    op => { op.CommandTimeout(1200); }));

            services.AddScoped(e =>
                new MasterDbSqlDataReader(config.GetConnectionString("MasterDbConnectionString"), 300));


            services.AddScoped(e =>
                new ReportDbSqlDataReader(config.GetConnectionString("ReportDbConnectionString"), 1000));

            services.AddDbContext<WritableMasterDbContext>(options =>
                options.UseSqlServer(config.GetConnectionString("WritableMasterDbConnectionString")));


            storageConnectionString = config.GetConnectionString("StorageConnectionString");
            var masterStorageConnectionString = config.GetConnectionString("MasterStorageConnectionString");

            services.AddDbContext<TransactionDbContext>(options =>
                options.UseSqlServer(config.GetConnectionString("TransactionDbConnectionString")));

            services.AddScoped(e =>
                new TransactionDbSqlDataReader(config.GetConnectionString("TransactionDbConnectionString")));

            services.AddDbContext<WritableTransactionDbContext>(options =>
                options.UseSqlServer(config.GetConnectionString("WritableTransactionDbConnectionString")));


            services.AddScoped<ResilientAction>();

            services.AddScoped<FAEngageService>();
            services.AddScoped<IFAEngageRepository, FAEngageRepository>();
            services.AddScoped<IEmployeeRepository, EmployeeRepository>();
            services.AddScoped<ICompanySettingsRepository, CompanySettingsRepository>();
            services.AddScoped<IFaEngageTransactionRepository, FAEngageTransactionRepository>();
            services.AddSingleton<ICacheProvider, InMemoryCacheProvider>();
            services.AddScoped<IKpiRepository, KpiRepository>();
            services.AddScoped<ITransactionKPIAchievementRepository, TransactionKPIAchievementRepository>();
            services.AddScoped<IMasterKpiAchievementRepository, MasterKpiAchievementRepository>();
            services.AddScoped<IKpiAchievementRepository, KpiAchievementRepository>();
            services.AddScoped<IFAEngageReportService, FAEngageReportService>();
            services.AddScoped<MTDService>();
            services.AddScoped<IJourneyCycleRepository, JourneyCycleRepository>();
            services.AddScoped<IManagerAlertsRepository, ManagerAlertsRepository>();
            services.AddScoped<IPositionCodeEntityMappingRepository, PositionCodeEntityMappingRepository>();
            services.AddScoped<IProductDivRepository, ProductDivRepository>();
            services.AddScoped<ILocationRepository, LocationRepository>();

            services.AddHttpClient<NotificationProcessor>()
                .SetHandlerLifetime(TimeSpan.FromMinutes(5));


            services.AddSingleton(_ => config);
            services.AddSingleton<QueueProcessor>();
            services.AddScoped<INotificationProcessor, NotificationProcessor>();

            services.AddScoped<IQueueManager, QueueManager>();
            services.AddScoped(_ => new QueueHandlerService(storageConnectionString));

            services.AddScoped(q => new AppNotificationQueueHandler(QueueType.GcmNotification, storageConnectionString));

            services.AddScoped(s => new CrouselBannerBlobReader(masterStorageConnectionString));

            services.AddSingleton(_ => new ErrorMessenger(masterStorageConnectionString, "FAEngageProcessor", "#faengage"));
        }
    }
}