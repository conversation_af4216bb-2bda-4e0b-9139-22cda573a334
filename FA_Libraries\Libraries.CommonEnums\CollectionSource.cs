﻿using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums;

public enum CollectionSource
{
    SFA,
    DMS,
    ExtAPI
}

public enum AssetOutletConfidenceScore
{
    High,
    Medium,
    Low
}

public enum AssetOutletHealthStatus
{
    None,
    Good,
    Degrading,
    NeedsMaintainence
}

public enum InvoiceSource
{
    [Display(Name = "")]
    none,

    [Display(Name = "SFA")]
    sfa,

    [Display(Name = "DMS")]
    dms
}

public enum OrderSource
{
    [Display(Name = "Direct Sales")]
    direct_sales,

    [Display(Name = "SFA")]
    sfa
}

public enum SourceType
{
    FA = 0,
    External = 1
}
