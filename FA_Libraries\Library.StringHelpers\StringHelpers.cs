﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;
using Library.CommonHelpers;

namespace Library.StringHelpers;

public static class StringHelper
{
    //csvSplit regex expression
    private static readonly Regex csvSplit = new("((?<=\")[^\"]*(?=\"(,|$)+)|(?<=,|^)[^,\"]*(?=,|$))",
        RegexOptions.Compiled);

    private static bool isHexaDecimal(char c)
    {
        var testchar = char.ToUpper(c);
        var list = "01234567890ABCDEF";
        return list.Contains(testchar);
    }

    private static T ParseEnum_Unchecked<T>(this string displayName, T defaultData = default)
    {
        if (typeof(T).IsEnum)
        {
            try
            {
                var res = (T)Enum.Parse(typeof(T), displayName);
                return !Enum.IsDefined(typeof(T), res) ? defaultData : res;
            }
            catch
            {
                try
                {
                    try
                    {
                        return Enum.GetValues(typeof(T))
                            .Cast<T>()
                            .Single(v => (v as Enum).GetDisplayName().isSameWhenNormalized(displayName));
                    }
                    catch
                    {
                        return defaultData;
                    }
                }
                catch (Exception)
                {
                    return defaultData;
                }
            }
        }

        return defaultData;
    }

    public static string ExtractPinCode(string input, int size = 6)
    {
        var Pattern = new Regex("\\d{5," + size + "}", RegexOptions.IgnoreCase);
        var m = Pattern.Match(input);
        return m.Success ? m.Value : new string(input.Reverse().Take(6).Reverse().ToArray());
    }

    public static bool IncludesAny(this string outer, IEnumerable<string> inner)
    {
        return outer.Split(',').Any(x => inner.Contains(x.Trim()));
    }

    public static bool Is_HashColor(string v)
    {
        if (v.FirstOrDefault() == '#' && v.Length == 7)
        {
            var testString = v.Substring(1);
            if (testString.All(c => isHexaDecimal(c)))
            {
                return true;
            }
        }

        return false;
    }

    public static bool Is_InList(List<string> CheckList, string val, bool allowNullandEmpty)
    {
        var checklistLocal = new List<string>(CheckList);
        if (allowNullandEmpty)
        {
            checklistLocal.Add(null!);
            checklistLocal.Add(string.Empty);
        }

        return checklistLocal.Any(x => x == val);
    }

    public static bool Is_StringBool(string v)
    {
        bool returnable;
        try
        {
            returnable = bool.Parse(v);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    ///     Checks If a string can be converted to dateTime
    /// </summary>
    /// <param name="v">STring To be Tested</param>
    /// <returns>True or False</returns>
    public static bool Is_StringDateTime(string v)
    {
        var returnable = new DateTime(1900, 1, 1);
        try
        {
            var integerFormat = Is_StringInt(v);
            if (integerFormat != -1)
            {
                returnable = returnable.AddDays(integerFormat);
                return returnable.Year is >= 2010 and <= 2030;
            }

            var ar = new CultureInfo("en-IN");
            returnable = DateTime.Parse(v, ar);
            // = DateTime.Parse(v);
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    public static bool Is_StringDecimal(string v, bool onlyPositive = false)
    {
        decimal returnable;
        try
        {
            returnable = decimal.Parse(v);
            return !onlyPositive || returnable > 0;
        }
        catch
        {
            return false;
        }
    }

    public static bool Is_StringEnglish(string inputText)
    {
        if (string.IsNullOrEmpty(inputText))
        {
            return false;
        }

        var regex = new Regex(@"[A-Za-z0-9 .,&-=+(){}\[\]\\]");
        var matches = regex.Matches(inputText);

        return matches.Count.Equals(inputText.Length);
    }

    /// <summary>
    ///     Checks if a string can be converted to Int
    /// </summary>
    /// <param name="v">String To be Tested</param>
    /// <returns>'-1' for invalid Data</returns>
    public static int Is_StringInt(string v)
    {
        var returnable = -1;
        if (v.All(char.IsDigit))
        {
            try
            {
                returnable = int.Parse(v);
            }
            catch
            {
                returnable = -1;
            }
        }
        else
        {
            returnable = -1;
        }

        return returnable;
    }

    public static bool Is_StringInteger(string v, bool onlyPositive = false)
    {
        int returnable;
        if (v.All(char.IsDigit))
        {
            try
            {
                returnable = int.Parse(v);
                return !onlyPositive || returnable > 0;
            }
            catch
            {
                return false;
            }
        }

        return false;
    }

    public static bool isSameWhenNormalized(this string st1, string st2, bool areNullEqual = true)
    {
        return (areNullEqual || st1 != null || st2 != null) && NormalizeCaps(st1) == NormalizeCaps(st2);
    }

    public static string ListToString<T>(IEnumerable<T> values)
    {
        return string.Join(", ", values.Select(X => $"'{X?.ToString()}'"));
    }

    public static string ListToStringValues<T>(IEnumerable<T> values)
    {
        return string.Join(", ", values.Select(X => $"{X?.ToString()}"));
    }

    public static string MinutesToDatTimeStringConvertor(this int? minutes)
    {
        return minutes.HasValue ? minutes.Value / 60 + " : " + Math.Abs(minutes.Value) % 60 : "00 : 00";
    }

    public static string NormalizeCaps(this string name)
    {
        return string.IsNullOrEmpty(name) ? string.Empty : Regex.Replace(name.Trim().ToUpperInvariant(), @"\s", "");
    }

    public static string NumberConversion(this double num)
    {
        var number = Math.Round(num, 2);
        var numberOfDigits = number.ToString().Split('.')[0].Length;
        var newNumber = "";
        var returnNumber = "";
        switch (numberOfDigits)
        {
            case 1:
                returnNumber = number.ToString();
                break;

            case 2:
                returnNumber = number.ToString();
                break;

            case 3:
                returnNumber = number.ToString();
                break;

            case 4:
                newNumber = number.ToString().Split('.')[0].Insert(1, ".");
                returnNumber = Math.Round(Convert.ToDouble(newNumber), 2) + "k";
                break;

            case 5:
                newNumber = number.ToString().Split('.')[0].Insert(2, ".");
                returnNumber = Math.Round(Convert.ToDouble(newNumber), 2) + "k";
                break;

            case 6:
                newNumber = number.ToString().Split('.')[0].Insert(1, ".");
                returnNumber = Math.Round(Convert.ToDouble(newNumber), 2) + "l";
                break;

            case 7:
                newNumber = number.ToString().Split('.')[0].Insert(2, ".");
                returnNumber = Math.Round(Convert.ToDouble(newNumber), 2) + "l";
                break;

            case 8:
                newNumber = number.ToString().Split('.')[0].Insert(1, ".");
                returnNumber = Math.Round(Convert.ToDouble(newNumber), 2) + "cr";
                break;

            case 9:
                newNumber = number.ToString().Split('.')[0].Insert(2, ".");
                returnNumber = Math.Round(Convert.ToDouble(newNumber), 2) + "cr";
                break;

            case 10:
                newNumber = number.ToString().Split('.')[0].Insert(3, ".");
                returnNumber = Math.Round(Convert.ToDouble(newNumber), 2) + "cr";
                break;

            case 11:
                newNumber = number.ToString().Split('.')[0].Insert(1, ".");
                returnNumber = Math.Round(Convert.ToDouble(newNumber), 2) + "k cr";
                break;

            default:
                returnNumber = number.ToString();
                break;
        }

        return returnNumber;
    }

    public static DateTime? ParseDateTime(this string v)
    {
        var returnable = new DateTime(1900, 1, 1);
        var ar = new CultureInfo("en-IN");

        try
        {
            var integerFormat = Is_StringInt(v);
            if (string.IsNullOrEmpty(v))
            {
                return null;
            }

            if (integerFormat != -1)
            {
                returnable = returnable.AddDays(integerFormat - 2);
                return returnable;
            }

            returnable = DateTime.Parse(v, ar);
            // = DateTime.Parse(v);
            return returnable;
        }
        catch
        {
            return null;
        }
    }

    public static bool PhoneDigitsCheck(string item)
    {
        return !string.IsNullOrEmpty(item) && item.Length is <= 10 and >= 9 && item.All(char.IsDigit);
    }

    public static bool PinCodeCheck(string v)
    {
        return v.All(char.IsDigit) && v.Length is <= 6 and >= 5;
    }

    //https://stackoverflow.com/a/25756010/11519765
    //Addd a null check before calling
    public static List<string> SplitCsv(this string inputText)
    {
        var list = new List<string>();
        var curr = "";
        foreach (Match match in csvSplit.Matches(inputText))
        {
            curr = match.Value;
            if (0 == curr.Length)
            {
                list.Add("");
            }

            list.Add(curr.TrimStart(','));
        }

        return list;
    }

    public static T TestNullAssign<T>(this string value, T whenNull, T whenEmpty = default)
    {
        var t = Nullable.GetUnderlyingType(typeof(T)) ?? typeof(T);
        if (t == typeof(DateTime))
        {
            var dateTimeValue = ParseDateTime(value);
            return (T)(value == null ? whenNull :
                string.IsNullOrEmpty(value) ? whenEmpty : Convert.ChangeType(dateTimeValue, t));
        }

        return t.IsEnum
            ? value == null ? whenNull :
            string.IsNullOrEmpty(value) ? whenEmpty : ParseEnum_Unchecked(value, whenEmpty)
            : (T)(value == null ? whenNull :
                string.IsNullOrEmpty(value) ? whenEmpty : Convert.ChangeType(value, t));
    }

    public static string TrimToLength(this string value, int count, bool addDots = true)
    {
        return value == null || value.Length <= count
            ? value
            : count > 10 && addDots
                ? string.Concat(value.AsSpan(0, count - 3), "...")
                : value.Substring(0, count);
    }

    public static bool IsBase64String(this string base64)
    {
        var buffer = new Span<byte>(new byte[base64.Length]);
        return Convert.TryFromBase64String(base64, buffer, out _);
    }

    public static string ToTitleCase(this string str)
    {
        if (string.IsNullOrWhiteSpace(str))
        {
            return "";
        }

        var textInfo = new CultureInfo("en-US", false).TextInfo;
        return textInfo.ToTitleCase(str.ToLower());
    }

    public static bool IsValidEmail(string email)
    {
        return Regex.IsMatch(email, "^[a-zA-Z0-9+_.-]+@[a-zA-Z0-9.-]+$");
    }

    public static string ConvertListToString<T>(this List<T> ids)
    {
        JsonSerializerOptions options = new() { DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull };
        return JsonSerializer.Serialize(ids, options);
    }

    public static List<string> ConvertStringToList(this string str)
    {
        return string.IsNullOrEmpty(str) ? new List<string>() : JsonSerializer.Deserialize<List<string>>(str);
    }

    /// <summary>
    ///     Returns all strings between char [pre] and [suf] in original.
    /// </summary>
    /// <param name="pre">
    ///     '<'</param>
    /// <param name="suf">'>'</param>
    /// <param name="original">
    ///     "con
    ///     <sideri>
    ///         <ng> some <test>cases"
    /// </param>
    /// <returns>["sideri", "ng", "test "]</returns>
    public static List<string> GetStringsBetweenChar(char pre, char suf, string original)
    {
        var rx = new Regex($"{pre}(.*?){suf}");
        var matches = rx.Matches(original).ToList();
        return matches.Select(s => s.Groups[1].Value).ToList();
    }

    /// <summary>
    ///     Replaces all string between char [pre] and [suf] in [original], with [replace]
    /// </summary>
    /// <param name="pre">
    ///     '<'</param>
    /// <param name="suf">'>'</param>
    /// <param name="original">
    ///     "con
    ///     <sideri>
    ///         <ng> some <test>cases"
    /// </param>
    /// <param name="replace">"sk"</param>
    /// <returns>consksk some skcases</returns>
    public static string ReplaceStringBetweenChar(char pre, char suf, string original, string replace)
    {
        var rx = new Regex($"{pre}(.*?){suf}");
        var returnValue = original;
        var matches = rx.Matches(original).ToList();

        foreach (var match in matches)
        {
            returnValue = returnValue.Replace(match.Groups[0].Value, replace);
        }

        return returnValue;
    }

    /// <summary>
    ///     In [replaceDic] key will be values between [pre] and [suf] without those two char.
    ///     Replaces all string between char [pre] and [suf] in [original], with [replace]
    /// </summary>
    /// <param name="pre">
    ///     '<'</param>
    /// <param name="suf">'>'</param>
    /// <param name="original">
    ///     "con
    ///     <sideri>
    ///         <ng> some <test>cases"
    /// </param>
    /// <param name="replaceDic">{"sideri": "sk"}</param>
    /// <returns>consk<ng> some <test>cases</returns>
    public static string ReplaceStringBetweenChar(char pre, char suf, string original, Dictionary<string, string> replaceDic)
    {
        var rx = new Regex($"{pre}(.*?){suf}");
        var returnValue = original;
        var matches = rx.Matches(original).ToList();

        foreach (var match in matches)
        {
            if (replaceDic.TryGetValue(match.Groups[1].Value, out var value))
            {
                returnValue = returnValue.Replace(match.Groups[0].Value, value);
            }
        }

        return returnValue;
    }

    public static List<long> ConvertCSVStringToList(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
        {
            return new List<long>();
        }

        return input.Split(',')
            .Select(id => long.TryParse(id, out var parsedId) ? parsedId : 0)
            .ToList();
    }
}
