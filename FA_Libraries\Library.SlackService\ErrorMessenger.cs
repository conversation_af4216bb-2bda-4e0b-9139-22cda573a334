﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Library.Infrastructure.QueueService;
using Library.SlackService.Model;
using Library.StorageWriter.Reader_Writer;

namespace Library.SlackService;

public class ErrorMessenger
{
    private readonly string channel;
    private readonly string masterStorageConnectionString;
    private readonly QueueType queue;
    private readonly string username;

    public ErrorMessenger(string masterStorageConnectionString, string username, string channel, QueueType queue = QueueType.SlackGeneric)
    {
        this.channel = channel;
        this.masterStorageConnectionString = masterStorageConnectionString;
        this.username = username;
        this.queue = queue;
    }

    private async Task<List<Field>> GetFieldsForExceptionAsync(Exception exception)
    {
        var fields = new List<Field>();
        if (exception != null)
        {
            var i = 0;
            var ex = exception;
            do
            {
                fields.Add(new Field { Short = false, Title = $"Exception Level{i++}", Value = ex.Message });
                ex = ex.InnerException;
            } while (ex != null);

            try
            {
                var blobWriter = new AnonymousBlobWriter(masterStorageConnectionString);
                var url = await blobWriter.WriteToBlob(Guid.NewGuid() + ".txt", exception.ToString()).ConfigureAwait(false);
                fields.Add(new Field { Short = true, Title = "StackTrace", Value = url });
            }
            catch
            {
                fields.Add(new Field { Short = true, Title = "StackTrace", Value = "Some Error Occured while Attaching Details!" });
            }
        }
        else
        {
            return null;
        }

        return fields;
    }

    public async Task SendErorToSlackChannel(Exception ex, string info, string channelName)
    {
        var action = new QueueHandler<SlackMessage>(queue, masterStorageConnectionString);
        await action.AddToGridQueue("Fake",
            new SlackMessage
            {
                Channel = channelName,
                Username = username,
                Text = info + $" at {DateTime.UtcNow}",
                Attachments = new List<Attachment> { new() { Title = $"Error: {ex?.GetBaseException().Message}", Fields = await GetFieldsForExceptionAsync(ex).ConfigureAwait(false), Color = "danger" } }
            }).ConfigureAwait(false);
    }

    public async Task SendToSlack(Exception ex, string info)
    {
        var action = new QueueHandler<SlackMessage>(queue, masterStorageConnectionString);
        await action.AddToGridQueue("Fake",
            new SlackMessage
            {
                Channel = channel,
                Username = username,
                Text = info + $" at {DateTime.UtcNow}",
                Attachments = new List<Attachment> { new() { Title = $"Error: {ex?.GetBaseException().Message}", Fields = await GetFieldsForExceptionAsync(ex).ConfigureAwait(false), Color = "danger" } }
            }).ConfigureAwait(false);
    }

    public async Task SendToSlack(string info, string detailedInfo)
    {
        var action = new QueueHandler<SlackMessage>(queue, masterStorageConnectionString);
        var blobWriter = new AnonymousBlobWriter(masterStorageConnectionString);

        var url = await blobWriter.WriteToBlob(Guid.NewGuid().ToString(), detailedInfo).ConfigureAwait(false);

        await action.AddToGridQueue("Fake", new SlackMessage { Channel = channel, Username = username, Text = $"{info}: {url} at {DateTime.UtcNow}" }).ConfigureAwait(false);
    }

    public async Task SendToSlack(string info)
    {
        var action = new QueueHandler<SlackMessage>(queue, masterStorageConnectionString);
        await action.AddToGridQueue("Fake", new SlackMessage { Channel = channel, Username = username, Text = info + $" at {DateTime.UtcNow}" }).ConfigureAwait(false);
    }

    public async Task SendToSlack(string info, string detailedInfo, string channelName)
    {
        var action = new QueueHandler<SlackMessage>(queue, masterStorageConnectionString);
        var blobWriter = new AnonymousBlobWriter(masterStorageConnectionString);

        var url = string.IsNullOrWhiteSpace(detailedInfo) ? "" : await blobWriter.WriteToBlob(Guid.NewGuid().ToString(), detailedInfo).ConfigureAwait(false);

        await action.AddToGridQueue("Fake", new SlackMessage { Channel = channelName, Username = username, Text = $"{info}: {url} at {DateTime.UtcNow}" }).ConfigureAwait(false);
    }

    public async Task SendToSlackWithAttachments(string info, string message, string channelName)
    {
        var action = new QueueHandler<SlackMessage>(queue, masterStorageConnectionString);
        await action.AddToGridQueue("Fake", new SlackMessage { Channel = channel, Username = username, Text = info, Attachments = new List<Attachment> { new() { Title = message + $" at {DateTime.UtcNow}", Color = "good" } } })
            .ConfigureAwait(false);
    }

    public async Task SendGeoCodingErorToSlack(string info, string channelName)
    {
        var action = new QueueHandler<SlackMessage>(queue, masterStorageConnectionString);
        await action.AddToGridQueue("Fake", new SlackMessage { Channel = channelName, Username = username, Text = info }).ConfigureAwait(false);
    }
}
