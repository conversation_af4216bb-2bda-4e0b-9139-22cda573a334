﻿using FAEngage.Core.Models.MasterDbModels;

namespace FAEngage.Core.MasterRepositories;

public interface IFAEngageRepository
{
    Task<List<EngageNotification>> GetActiveUSerAppNotificationsForDate(DateTime date);
    Task<EngageNotification> GetNotification(long id, long companyId);
    Task<Cohort> GetCohort(long cohort, long companyId);
    Task<List<KPITriggers>> GetKpiTriggers(long notificationId, long companyId);
    Task<NotificationMessage> GetNotificationMessage(long notificationId, long companyId);
    Task<NotificationMessage> GetAggregatedNotificationMessage(long notificationId, long companyId);
}