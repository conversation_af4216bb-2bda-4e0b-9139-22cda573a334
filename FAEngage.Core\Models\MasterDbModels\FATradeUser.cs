﻿using Libraries.CommonEnums;

namespace FAEngage.Core.Models.MasterDbModels
{
    public class FATradeUser
    {
        public long Id { get; set; }
        public string ShopName { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Address { get; set; }
        public string OwnersName { get; set; }
        public string PhoneNo { get; set; }
        public string GSTN { get; set; }
        public string AlternateImageId { get; set; }
        public string PhotoProofId { get; set; }
        public string TypeOfIdProof { get; set; }
        public string Email { get; set; }
        public string MarketName { get; set; }
        public string PAN { get; set; }
        public string Aadhar { get; set; }
        public string ShopType { get; set; }
        public string PinCode { get; set; }
        public Guid? ImageId { get; set; }
        public string Landmark { get; set; }
        public string LandlineNumber { get; set; }
        public string ModeOfDataCollection { get; set; }
        public string BankAccountNumber { get; set; }
        public string AccountHoldersName { get; set; }
        public string IFSCCode { get; set; }
        public string CreationContext { get; set; }
        public bool IsKYC { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public PortalUserRole UserRole { get; set; }
        public Guid? UniqueId { get; set; }
        public OutletChannel? OutletChannel { get; set; }
        public bool GSTRegistered { get; set; }
        public OutletSegmentation? Segmentation { get; set; }
    }
}
