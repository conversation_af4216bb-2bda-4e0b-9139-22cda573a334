﻿using System;

namespace Library.GuidGenerator;

public class CustomGuid
{
    private static readonly Guid nsId = GuidUtility.Create(GuidUtility.DnsNamespace, "www.fieldassist.io");

    public static Guid CreateDayRecordId(long empId, long qualifiedDateKey)
    {
        var guid = GuidUtility.Create(nsId, $"emp/{empId}/dk/{qualifiedDateKey}");
        return guid;
    }

    public static Guid CreateDayRecordId(long empId, string qualifiedDateKey)
    {
        var guid = GuidUtility.Create(nsId, $"emp/{empId}/dk/{qualifiedDateKey}");
        return guid;
    }

    public static Guid CreateSessionIdFromV2DayRecordId(long dayRecordId)
    {
        var guid = GuidUtility.Create(nsId, $"ds/{dayRecordId}");
        return guid;
    }

    public static Guid CreateWorkflowEmployeeId(long empId)
    {
        var guid = GuidUtility.Create(nsId, $"emp/{empId}");
        return guid;
    }
}
