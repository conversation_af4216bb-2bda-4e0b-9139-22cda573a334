﻿using System;
using System.Collections.Generic;
using System.Text.Json;
using Libraries.PerspectiveColumns.Interface;

namespace Libraries.PerspectiveColumns;

public class ProductDemandVsSalesPerspective : IPerspective
{
    private readonly Dictionary<string, string> _nomenclatureDict;

    public ProductDemandVsSalesPerspective(Dictionary<string, string> nomenclatureDict, string jsonDefinition)
    {
        _nomenclatureDict = nomenclatureDict ?? new Dictionary<string, string>();
        Columns = GenerateColumnsFromJson(jsonDefinition);
    }

    List<PerspectiveColumnModel> IPerspective.Columns
    {
        get => Columns;
        set => throw new NotImplementedException();
    }

    public List<PerspectiveColumnModel> Columns { get; }

    private List<PerspectiveColumnModel> GenerateColumnsFromJson(string jsonDefinition)
    {
        var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
        var columns = JsonSerializer.Deserialize<List<PerspectiveColumnModel>>(jsonDefinition, options);

        foreach (var column in columns)
        {
            if (column.IsUsesNomenclature)
            {
                column.DisplayName = ProcessNomenclature(column.DisplayName, column.NomenclatureKey);
                column.SubGroup = ProcessNomenclature(column.SubGroup, column.NomenclatureKey);
            }
        }

        return columns;
    }

    private string ProcessNomenclature(string value, string nomenclatureKey)
    {
        if (string.IsNullOrEmpty(value))
        {
            return value;
        }

        // Replace nomenclature placeholders with actual values
        return value.Replace("$", _nomenclatureDict.TryGetValue(nomenclatureKey, out var result) ? result : "");
    }
}
