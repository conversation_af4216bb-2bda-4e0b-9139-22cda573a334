﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;

namespace Library.SqlHelper;

public class SqlDataReader
{
    /// <summary>
    /// </summary>
    /// <param name="connectionString">connectionString of the DataSource</param>
    /// <param name="commandTimeout">timeout in Seconds</param>
    public SqlDataReader(string connectionString, int commandTimeout = 30)
    {
        this.connectionString = connectionString;
        CommandTimeout = commandTimeout;
    }

    private string connectionString { get; }

    /// <summary>
    ///     Command Timeout in seconds
    /// </summary>
    public int CommandTimeout { get; set; }


    public async Task<string> GetSingleResultOfQuery(string queryString, List<SqlParameter> parameters = null,
        int? commandTimeout = null)
    {
        using (var connection = new SqlConnection(connectionString))
        {
            try
            {
                string result = null;
                await connection.OpenAsync().ConfigureAwait(false);
                using (var command = new SqlCommand(queryString, connection))
                {
                    try
                    {
                        CommandTimeout = commandTimeout ?? CommandTimeout;
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters.ToArray());
                        }

                        using (var reader = await command.ExecuteReaderAsync().ConfigureAwait(false))
                        {
                            if (reader.HasRows)
                            {
                                reader.Read();
                                result = reader[0].ToString();
                            }

                            return result;
                        }
                    }
                    finally
                    {
                        command.Parameters.Clear();
                    }
                }
            }
            finally
            {
                connection.Close();
            }
        }
    }

    public async Task<string> GetSingleResultOfQuery(string queryString, List<long> ids, List<SqlParameter> parameters = null,
        int? commandTimeout = null, string idPlaceHolders = "$$Ids$$")
    {
        using (var connection = new SqlConnection(connectionString))
        {
            try
            {
                string result = null;
                await connection.OpenAsync().ConfigureAwait(false);
                queryString = await GetQueryStringWithIds(connection, queryString, ids, idPlaceHolders).ConfigureAwait(false);
                using (var command = new SqlCommand(queryString, connection))
                {
                    CommandTimeout = commandTimeout ?? CommandTimeout;
                    if (parameters != null)
                    {
                        command.Parameters.AddRange(parameters.ToArray());
                    }

                    using (var reader = await command.ExecuteReaderAsync().ConfigureAwait(false))
                    {
                        if (reader.HasRows)
                        {
                            reader.Read();
                            result = reader[0].ToString();
                        }

                        return result;
                    }
                }
            }
            finally
            {
                connection.Close();
            }
        }
    }

    public async Task<List<Dictionary<string, object>>> GetMultiResultOfQuery(string queryString, List<SqlParameter> parameters = null,
        int? commandTimeout = null)
    {
        using (var connection = new SqlConnection(connectionString))
        {
            try
            {
                await connection.OpenAsync().ConfigureAwait(false);
                using (var command = new SqlCommand(queryString, connection))
                {
                    var results = new List<Dictionary<string, object>>();
                    try
                    {
                        CommandTimeout = commandTimeout ?? CommandTimeout;
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters.ToArray());
                        }

                        using (var reader = await command.ExecuteReaderAsync().ConfigureAwait(false))
                        {
                            while (await reader.ReadAsync().ConfigureAwait(false))
                            {
                                var row = new Dictionary<string, object>();

                                for (var i = 0; i < reader.FieldCount; i++)
                                {
                                    row[reader.GetName(i)] = reader.GetValue(i);
                                }

                                results.Add(row);
                            }
                        }

                        return results;
                    }
                    finally
                    {
                        command.Parameters.Clear();
                    }
                }
            }
            finally
            {
                connection.Close();
            }
        }
    }

    /// <summary>
    ///     Converts a query to enumerable of the Type Provided
    /// </summary>
    /// <typeparam name="T">Return Data Type in the Enumerable</typeparam>
    /// <param name="queryString">sql query to be executed</param>
    /// <param name="parameters"> sql Parameters if any</param>
    /// <param name="commandTimeout"></param>
    /// <returns>Enumerable of Type T</returns>
    public async Task<IEnumerable<T>> GetModelFromQueryAsync<T>(string queryString,
        List<SqlParameter> parameters = null, int? commandTimeout = null, bool isSp = false, bool retryMechanism = false, int attempt = 1)
    {
        using (var connection = new SqlConnection(connectionString))
        {
            try
            {
                await connection.OpenAsync().ConfigureAwait(false);
                var data = await GetModelFromQueryAsync<T>(connection, queryString, parameters, commandTimeout,
                    isSp).ConfigureAwait(false);
                connection.Close();
                return data;
            }
            finally
            {
                connection.Close();
            }
        }
    }


    private async Task<IEnumerable<T>> GetModelFromQueryAsync<T>(SqlConnection connection, string queryString,
        List<SqlParameter> parameters = null, int? commandTimeout = null, bool isSp = false)
    {
        var data = new List<T>();
        using (var command = new SqlCommand(queryString, connection))
        {
            try
            {
                if (isSp)
                {
                    command.CommandType = CommandType.StoredProcedure;
                }

                command.CommandTimeout = commandTimeout ?? CommandTimeout;
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters.ToArray());
                }

                using (var reader = await command.ExecuteReaderAsync().ConfigureAwait(false))
                {
                    if (reader.HasRows)
                    {
                        var numFields = reader.FieldCount;
                        var rangeValues = Enumerable.Range(0, numFields).ToList();

                        var columnTypes = rangeValues.Select(i => reader.GetFieldType(i)).ToArray();
                        var columnNames = rangeValues.Select(i => reader.GetName(i)).ToArray();
                        var type = typeof(T);
                        var props = rangeValues
                            .Select(i => type.GetProperty(columnNames[i]))
                            .ToArray();

                        var fieldExceptions = rangeValues.Where(i => props[i] == null).ToList();
                        if (fieldExceptions.Any())
                        {
                            var errors = fieldExceptions.Where(i => fieldExceptions.Contains(i))
                                .Select(i => columnNames[i]).ToList();
                            throw new InvalidOperationException(
                                $"Some of the Field(s) from query [{string.Join(",", errors)}] are not matching in the class");
                        }

                        var nonNullprops = rangeValues
                            .Select(i =>
                                Nullable.GetUnderlyingType(type.GetProperty(columnNames[i]).PropertyType) ??
                                type.GetProperty(columnNames[i]).PropertyType)
                            .ToArray();
                        var castExceptions = rangeValues.Where(i =>
                            !props[i].PropertyType.IsAssignableFrom(columnTypes[i]) &&
                            !(columnTypes[i].FullName == typeof(int).FullName && (props[i].PropertyType.IsEnum ||
                                                                                  IsNullableEnum(props[i].PropertyType)))).ToList();
                        if (castExceptions.Any())
                        {
                            var errors = castExceptions.Select(i =>
                                    $"'from {props[i].PropertyType} to {columnTypes[i]} for field {columnNames[i]}'")
                                .ToList();
                            throw new InvalidCastException($"Cannot convert {string.Join(",", errors)}");
                        }

                        while (reader.Read())
                        {
                            var item = Activator.CreateInstance(type);
                            rangeValues.ForEach(i =>
                            {
                                var val = reader.GetValue(i);
                                if (val is DBNull || val == null)
                                {
                                    props[i].SetValue(item, null);
                                }
                                else
                                {
                                    if (nonNullprops[i].IsEnum)
                                    {
                                        props[i].SetValue(item, Enum.ToObject(nonNullprops[i], reader.GetValue(i)));
                                    }
                                    else
                                    {
                                        props[i].SetValue(item,
                                            Convert.ChangeType(reader.GetValue(i), nonNullprops[i]));
                                    }
                                }
                            });
                            data.Add((T)item);
                        }
                    }

                    return data;
                }
            }
            finally
            {
                command.Parameters.Clear();
            }
        }
    }

    private static bool IsNullableEnum(Type t)
    {
        var u = Nullable.GetUnderlyingType(t);
        return u != null && u.IsEnum;
    }


    public async Task<IEnumerable<T>> GetModelFromQueryAsync<T>(string queryString, List<Guid> ids,
        string idPlaceHolders = "$$Ids$$", List<SqlParameter> parameters = null, int? commandTimeout = null)
    {
        using (var connection = new SqlConnection(connectionString))
        {
            try
            {
                await connection.OpenAsync().ConfigureAwait(false);
                var data = await GetModelFromQueryAsync<T>(connection,
                    await GetQueryStringWithIds(connection, queryString, ids, idPlaceHolders).ConfigureAwait(false), parameters,
                    commandTimeout).ConfigureAwait(false);
                connection.Close();
                return data;
            }
            finally
            {
                connection.Close();
            }
        }
    }


    public async Task<IEnumerable<T>> GetModelFromQueryAsync<T>(string queryString, List<long> ids,
        string idPlaceHolders = "$$Ids$$", List<SqlParameter> parameters = null, int? commandTimeout = null, bool retryMechanism = false, int attempt = 1)
    {
        using (var connection = new SqlConnection(connectionString))
        {
            try
            {
                await connection.OpenAsync().ConfigureAwait(false);
                var data = await GetModelFromQueryAsync<T>(connection,
                    await GetQueryStringWithIds(connection, queryString, ids, idPlaceHolders).ConfigureAwait(false), parameters,
                    commandTimeout).ConfigureAwait(false);
                connection.Close();
                return data;
            }
            finally
            {
                connection.Close();
            }
        }
    }


    public async Task<IEnumerable<T>> GetModelFromQueryWithTempTableAsync<T>(string queryString, List<long> ids,
        string idPlaceHolders = "$$Ids$$", List<SqlParameter> parameters = null, int? commandTimeout = null, bool retryMechanism = false, int attempt = 1)
    {
        using (var connection = new SqlConnection(connectionString))
        {
            try
            {
                await connection.OpenAsync().ConfigureAwait(false);
                var data = await GetModelFromQueryAsync<T>(connection,
                    await GetTableNameWithIds(connection, queryString, ids, idPlaceHolders).ConfigureAwait(false), parameters,
                    commandTimeout).ConfigureAwait(false);
                connection.Close();
                return data;
            }
            finally
            {
                connection.Close();
            }
        }
    }


    private static async Task<string> GetQueryStringWithIds(SqlConnection connection, string queryString, List<long> ids,
        string idPlaceHolders = "$$Ids$$")
    {
        await new SqlCommand(@"DROP TABLE if exists  #TempTable ;
                                 CREATE TABLE #TempTable(Id bigint)", connection).ExecuteNonQueryAsync().ConfigureAwait(false);

        for (var i = 0; i < ids.Count; i += 1000)
        {
            await new SqlCommand(
                $@"INSERT INTO #TempTable Values{string.Join(",", ids.Select(id => "(" + id + ")").Skip(i).Take(1000))}",
                connection).ExecuteNonQueryAsync().ConfigureAwait(false);
        }

        return queryString.Replace(idPlaceHolders, "select Id from #TempTable");
    }


    private static async Task<string> GetQueryStringWithIds(SqlConnection connection, string queryString, List<Guid> ids,
        string idPlaceHolders = "$$Ids$$")
    {
        await new SqlCommand(@"DROP TABLE if exists  #TempTable ;
                                 CREATE TABLE #TempTable(Id UNIQUEIDENTIFIER)", connection).ExecuteNonQueryAsync().ConfigureAwait(false);

        for (var i = 0; i < ids.Count; i += 1000)
        {
            await new SqlCommand(
                $@"INSERT INTO #TempTable Values{string.Join(",", ids.Select(id => "('" + id + "')").Skip(i).Take(1000))}",
                connection).ExecuteNonQueryAsync().ConfigureAwait(false);
        }

        return queryString.Replace(idPlaceHolders, "select Id from #TempTable");
    }


    private static async Task<string> GetTableNameWithIds(SqlConnection connection, string queryString, List<long> ids,
        string idPlaceHolders = "$$Ids$$")
    {
        await new SqlCommand(@"DROP TABLE if exists  #TempTable ;
                                 CREATE TABLE #TempTable(Id bigint)", connection).ExecuteNonQueryAsync().ConfigureAwait(false);

        for (var i = 0; i < ids.Count; i += 1000)
        {
            await new SqlCommand(
                $@"INSERT INTO #TempTable Values{string.Join(",", ids.Select(id => "(" + id + ")").Skip(i).Take(1000))}",
                connection).ExecuteNonQueryAsync().ConfigureAwait(false);
        }

        return queryString.Replace(idPlaceHolders, "#TempTable");
    }


    public async Task<DataTable> GetDataTableForQueryDisposeConn(string queryString,
        List<SqlParameter> parameters = null, int? commandTimeout = null, bool isSp = false,
        string idPlaceHolders = "$$Ids$$", List<long> ids = null)
    {
        using (var connection = new SqlConnection(connectionString))
        {
            try
            {
                await connection.OpenAsync().ConfigureAwait(false);
                if (!isSp && ids != null)
                {
                    queryString = await GetQueryStringWithIds(connection, queryString, ids, idPlaceHolders).ConfigureAwait(false);
                }

                using (var command = new SqlCommand(queryString, connection))
                {
                    try
                    {
                        command.CommandTimeout = commandTimeout ?? CommandTimeout;
                        if (isSp)
                        {
                            command.CommandType = CommandType.StoredProcedure;
                        }

                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters.ToArray());
                        }

                        using (var reader = await command.ExecuteReaderAsync().ConfigureAwait(false))
                        {
                            DataTable dt = null;
                            dt = new DataTable();
                            dt.Load(reader);
                            command.Parameters.Clear();
                            return dt;
                        }
                    }
                    finally
                    {
                        command.Parameters.Clear();
                    }
                }
            }
            finally
            {
                connection.Close();
            }
        }
    }


    public async Task<DataTable> GetDataTableForQuery(string queryString, List<SqlParameter> parameters = null,
        int? commandTimeout = null, bool isSp = false, string idPlaceHolders = "$$Ids$$", List<long> ids = null)
    {
        using (var connection = new SqlConnection(connectionString))
        {
            try
            {
                await connection.OpenAsync().ConfigureAwait(false);
                if (!isSp && ids != null)
                {
                    queryString = await GetQueryStringWithIds(connection, queryString, ids, idPlaceHolders).ConfigureAwait(false);
                }

                using (var command = new SqlCommand(queryString, connection))
                {
                    try
                    {
                        command.CommandTimeout = commandTimeout ?? CommandTimeout;
                        if (isSp)
                        {
                            command.CommandType = CommandType.StoredProcedure;
                        }

                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters.ToArray());
                        }

                        using (var reader = await command.ExecuteReaderAsync().ConfigureAwait(false))
                        {
                            DataTable dt = null;
                            dt = new DataTable();
                            dt.Load(reader);
                            command.Parameters.Clear();
                            return dt;
                        }
                    }
                    finally
                    {
                        command.Parameters.Clear();
                    }
                }
            }
            finally
            {
                connection.Close();
            }
        }
    }


    public async Task<DataTable> GetDataTableForQuery(SqlConnection connection, string queryString, List<SqlParameter> parameters = null,
        int? commandTimeout = null, bool isSp = false, string idPlaceHolders = "$$Ids$$", List<long> ids = null)
    {
        await connection.OpenAsync().ConfigureAwait(false);
        if (!isSp && ids != null)
        {
            queryString = await GetQueryStringWithIds(connection, queryString, ids, idPlaceHolders).ConfigureAwait(false);
        }

        using (var command = new SqlCommand(queryString, connection))
        {
            try
            {
                command.CommandTimeout = commandTimeout ?? CommandTimeout;
                if (isSp)
                {
                    command.CommandType = CommandType.StoredProcedure;
                }

                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters.ToArray());
                }

                using (var reader = await command.ExecuteReaderAsync().ConfigureAwait(false))
                {
                    DataTable dt = null;
                    dt = new DataTable();
                    dt.Load(reader);
                    command.Parameters.Clear();
                    return dt;
                }
            }
            finally
            {
                command.Parameters.Clear();
            }
        }
    }

    public async Task<DataTable> RunQueryAndGetDataTableAsync(string nonQuery, string dataQuery, List<SqlParameter> parameters = null, int? commandTimeout = null)
    {
        using (var connection = new SqlConnection(connectionString))
        {
            try
            {
                await connection.OpenAsync().ConfigureAwait(false);
                if (!string.IsNullOrWhiteSpace(nonQuery))
                {
                    await ExecuteNonQueryAsync(connection, nonQuery, parameters, commandTimeout).ConfigureAwait(false);
                }

                return await GetDataTableForQuery(dataQuery, parameters, commandTimeout).ConfigureAwait(false);
            }
            finally
            {
                connection.Close();
            }
        }
    }

    public async Task<int> RunQuery(string queryString, List<SqlParameter> parameters = null, int? commandTimeout = null)
    {
        using var connection = new SqlConnection(connectionString);

        try
        {
            await connection.OpenAsync().ConfigureAwait(false);
            return await ExecuteNonQueryAsync(connection, queryString, parameters, commandTimeout).ConfigureAwait(false);
        }
        finally
        {
            connection.Close();
        }
    }

    private static async Task<int> ExecuteNonQueryAsync(SqlConnection connection, string queryString, List<SqlParameter> parameters, int? commandTimeout)
    {
        using var command = new SqlCommand(queryString, connection);
        try
        {
            command.CommandTimeout = commandTimeout ?? command.CommandTimeout;
            if (parameters != null)
            {
                command.Parameters.AddRange(parameters.ToArray());
            }

            return await command.ExecuteNonQueryAsync().ConfigureAwait(false);
        }
        finally
        {
            command.Parameters.Clear();
        }
    }
}
