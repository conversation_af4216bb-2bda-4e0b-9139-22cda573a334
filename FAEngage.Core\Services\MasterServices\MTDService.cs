﻿using FAEngage.Core.Helpers;
using FAEngage.Core.MasterRepositories;
using Library.DateTimeHelpers;

namespace FAEngage.Core.Services.MasterServices;

public class MTDService(
    IJourneyCycleRepository journeyCycleRepository,
    ICompanySettingsRepository companySettingsRepository)
{
    public async Task<FA_MTD_LMTD> GetMTD_LMTD(DateTime date, long companyId, bool includeToday = false,
        long? zoneId = null, int? yearStartMonth = 4, CompanySettings companySettings = null)
    {
        if (companySettings == null)
        {
            var companySettingsDict = await companySettingsRepository.GetSettings(companyId);
            companySettings = new CompanySettings(companySettingsDict);
        }

        var today = DateTime.UtcNow.Add(companySettings.TimeZoneOffset).Date;
        if (date.Date == today && !includeToday) date = date.AddDays(-1); //MTD can't be calculate for Today
        IEnumerable<FADateRange> journeyCycles = null;
        if (companySettings.UsesJourneyCalendar)
            journeyCycles = (await journeyCycleRepository.GetJourneyCalendar(companyId, date, zoneId))
                ?.Select(c => new FADateRange
                {
                    StartDate = c.MonthStartDate,
                    EndDate = c.MonthEndDate,
                    MonthNumber = c.Month,
                    YearNumber = c.Year,
                    MonthName = c.MonthName,
                    CycleWeeks = c.Weeks.Select(x => new CycleWeek
                    {
                        QuarterNumber = x.QuarterNumber,
                        WeekForQuarter = x.WeekForQuarter,
                        WeekForMonth = x.WeekForMonth,
                        WeekForYear = x.WeekForYear,
                        WeekStartDate = x.WeekStartDate,
                        WeekEndDate = x.WeekEndDate
                    }).ToList()
                }).ToList();
        var monthStartDate = companySettings.MonthStartDate;
        return date.Get_MTD_LMTD(journeyCycles, monthStartDate, yearStartMonth.Value);
    }

    public async Task<List<FA_MTD_LMTD>> GetMTD_LMTDByMonthAndYearWithNPreviousMonths(long companyId,
        DateTime today, int noOfMonths, bool isRelative = false, bool includeToday = false, long? zoneId = null)
    {
        var lmtdMtdList = new List<FA_MTD_LMTD>();
        var fA_MTD_LMTD = new FA_MTD_LMTD();
        fA_MTD_LMTD = await GetMTD_LMTD(today, companyId, includeToday, zoneId);
        lmtdMtdList.Add(fA_MTD_LMTD);
        while (noOfMonths > 1)
        {
            fA_MTD_LMTD = await GetMTD_LMTD(fA_MTD_LMTD.LMTD.Today, companyId, includeToday, zoneId);
            lmtdMtdList.Add(fA_MTD_LMTD);
            noOfMonths--;
        }

        return lmtdMtdList;
    }
}