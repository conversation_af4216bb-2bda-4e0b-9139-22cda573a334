﻿namespace Library.ApiLogger.CosmosLogs;

public class CosmosSource
{
    public CosmosSource(string cosmosConnString, string databaseId, string collectionId)
    {
        var items = cosmosConnString.Split(';');
        AuthorizationKey = items[1].Substring(items[1].IndexOf("AccountKey=") + "AccountKey=".Length);
        EndpointUrl = items[0].Substring(items[0].IndexOf("AccountEndpoint=") + "AccountEndpoint=".Length);

        DatabaseId = databaseId;
        CollectionId = collectionId;
    }

    public string AuthorizationKey { get; }
    public string CollectionId { get; }
    public string DatabaseId { get; }
    public string EndpointUrl { get; }
}
