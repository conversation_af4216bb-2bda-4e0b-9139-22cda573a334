﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace Library.ConnectionStringParsor;

public class ConnectionStringBase
{
    private readonly string connectionString;

    public ConnectionStringBase(string connectionString)
    {
        this.connectionString = connectionString;
    }

    protected Dictionary<string, string> Parse()
    {
        ValidateConnString();

        return connectionString.Split(';').Where(i => !string.IsNullOrWhiteSpace(i) && i.Contains('='))
            .Select(i => i.Split('=')).ToDictionary(i => i[0].ToLower(), i => string.Join("=", i.Skip(1)));
    }

    protected void ValidateConnString()
    {
        if (string.IsNullOrWhiteSpace(connectionString))
        {
            throw new Exception("Empty Conn String");
        }

        if (!connectionString.Contains('='))
        {
            throw new Exception("MalFormed Connection String");
        }
    }
}
