﻿using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using FAEngage.Configurations;
using jjm.one.Serilog.Sinks.SlackWebHook;
using Serilog;
using Serilog.Events;

namespace FAEngage
{
    internal class Program
    {
        private static string GetKeyVaultEndpoint() => Environment.GetEnvironmentVariable("KEYVAULT_ENDPOINT");

        public static async Task Main(string[] args)
        {
            var builder = new HostBuilder()
                .ConfigureAppConfiguration((config) =>
                {
                    var keyVaultEndpoint = GetKeyVaultEndpoint();
                    if (!string.IsNullOrEmpty(keyVaultEndpoint))
                    {
                        config.AddAzureKeyVault(keyVaultEndpoint);
                    }

                    config.AddEnvironmentVariables();
                })
                .UseSerilog((hostingContext, loggerConfiguration) =>
                {
                    var slackUri = "*********************************************************************************";
                    var env = hostingContext.Configuration.GetValue<string>("AppSettings:Deployment");

#if DEBUG
                    loggerConfiguration
                        .MinimumLevel.Information();
#else
                    if (env == "dev")
                        loggerConfiguration
                            .MinimumLevel.Information();
                    else
                        loggerConfiguration
                            .MinimumLevel.Warning();
#endif
                    loggerConfiguration
                        .Enrich.FromLogContext()
                        .WriteTo.Console()
                        .WriteTo.Slack(
                        slackWebHookUrl: slackUri,
                        slackChannel: "faengageprocessorv2",
                        slackUsername: $"{env}-Engage Processor Logger",
                        slackEmojiIcon: ":radioactive_sign:",
                        periodicBatchingSinkOptionsBatchSizeLimit: 1,
                        periodicBatchingSinkOptionsPeriod: TimeSpan.FromMilliseconds(1000),
                        periodicBatchingSinkOptionsQueueLimit: 10000,
                        sinkRestrictedToMinimumLevel: LogEventLevel.Error
                                      ); 
                })
                .ConfigureWebJobs(b =>
                {
                    b.AddTimers();
                    b.AddAzureStorageQueues(c =>
                    {
                        c.BatchSize =
#if DEBUG
                            1;
#else
                            10;
#endif
                        c.MaxPollingInterval = TimeSpan.FromSeconds(5);
                    });
                })
                .ConfigureServices((context, services) => { Dependencies.SetUp(services, context.Configuration); })
                .UseConsoleLifetime();
            var host = builder.Build();
            using (host)
            {
                await host.RunAsync();
            }
        }
    }
}