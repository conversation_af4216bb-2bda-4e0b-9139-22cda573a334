﻿using FAEngage.Core.Models;

namespace FAEngage.Core.MasterRepositories
{
    public interface IPositionCodeEntityMappingRepository
    {
        Task<List<PositionCodeEntityMappingMin>> GetAllPositionCodesUnderUser(long companyId, List<long> positionCodeIds, bool isAdmin, bool includeUser);
        Task<List<PositionCodeEntityMappingMin>> GetPositionUsers(long companyId, List<long> pcIds);
        Task<List<PositionCodeHierarchy>> GetPositionCodeHierarchies(long companyId, List<long> positionCodeIds);
    }
}