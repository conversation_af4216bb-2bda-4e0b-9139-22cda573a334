﻿using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums;

public enum DerivedKPIMeasures
{
    [Display(Name = "Value")]
    Value,

    [Display(Name = "NetValue")]
    NetValue,

    [Display(Name = "Order Qty (Std Unit)")]
    OrderQtyInStdUnit,

    [Display(Name = "Order Qty (Unit)")]
    OrderInUnits,

    [Display(Name = "TC")]
    TC,

    [Display(Name = "PC")]
    PC,

    [Display(Name = "Avg. TC (per day)")]
    AvgTCPerday,

    [Display(Name = "Avg. PC (per day)")]
    AvgPCPerday,

    [Display(Name = "LPC")]
    LPC,

    [Display(Name = "Avg Retailing Time")]
    AvgRetailingTime,

    [Display(Name = "Avg Sales (per day)")]
    AvgValue
}
