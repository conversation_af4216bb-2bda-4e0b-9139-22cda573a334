﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Azure;
using Azure.Core;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Libraries.Cryptography;

namespace Library.StorageWriter;

public class BlobReader
{
    protected readonly BlobContainerClient _container;

    public BlobReader(string storageConnectionString, string containerName)
    {
        var blobServiceClient = new BlobServiceClient(storageConnectionString, new BlobClientOptions { Retry = { Mode = RetryMode.Exponential, Delay = TimeSpan.FromSeconds(1), MaxDelay = TimeSpan.FromSeconds(10), MaxRetries = 3 } });
        _container = blobServiceClient.GetBlobContainerClient(containerName);
    }

    public async Task<Stream> DownloadFileAsStream(string name)
    {
        var blob = _container.GetBlobClient(name);
        using (var memorystream = new MemoryStream())
        {
            await blob.DownloadToAsync(memorystream).ConfigureAwait(false);
            memorystream.Position = 0;
            return memorystream;
        }
    }

    [Obsolete("Use method 'ReadBlobContentAsync'")]
    public async Task<string> GetBlobAsString(string sourceName)
    {
        var str = await ReadBlobContentAsync(sourceName).ConfigureAwait(false);
        return str;
    }

    public async Task<string> ReadBlobContentAsync(string name)
    {
        var blob = _container.GetBlobClient(name);
        var response = await blob.OpenReadAsync().ConfigureAwait(false);
        using (var reader = new StreamReader(response))
        {
            return await reader.ReadToEndAsync().ConfigureAwait(false);
        }
    }

    public BlobClient GetBlobClient(string fileName)
    {
        return _container.GetBlobClient(fileName);
    }

    /// <summary>
    /// Retrieves the names of all blobs asynchronously within the container.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of blob names.</returns>
    public async Task<List<string>> GetAllBlobNamesAsync()
    {
        var blobNames = new List<string>();

        await foreach (var blobItem in _container.GetBlobsAsync().ConfigureAwait(false))
        {
            blobNames.Add(blobItem.Name);
        }

        return blobNames;
    }

    //public Page<BlobItem> GetBlobNames(string lastToken)
    //{
    //    AsyncPageable<BlobItem> results = _container.GetBlobsAsync();
    //    var pages = results.AsPages(lastToken);
    //    return pages;
    //}

    public async Task<byte[]> GetFileBytes(string fileName)
    {
        var blockBlob = _container.GetBlobClient(fileName);

        var stream = await blockBlob.OpenReadAsync().ConfigureAwait(false);
        var buffer = new byte[16 * 1024];
        using var ms = new MemoryStream();
        int read;
        while ((read = await stream.ReadAsync(buffer).ConfigureAwait(false)) > 0)
        {
            ms.Write(buffer, 0, read);
        }

        return ms.ToArray();
    }

    //TODO: delete this since dependant on new dashboard
    public virtual string GetPrivatePath(string filename, string portalPath, long userId, bool isNewDashboard = false)
    {
        if (filename == null)
        {
            return null;
        }

        var encryptedFilename = AESEncryptor.Encrypt(filename, $"Nm5BycFeL3AucSmN_{userId}");
        encryptedFilename = Uri.EscapeDataString(encryptedFilename);
        return $"{portalPath}/FieldAssistPOC/Download/ReportsDownload?encryptedFileName={encryptedFilename}&downloadedFromNewDashboard={isNewDashboard}";
    }

    public virtual string GetPrivateAnonymousPath(string filename, string portalPath, long userId, bool isNewDashboard = false)
    {
        if (filename == null)
        {
            return null;
        }

        var encryptedFilename = AESEncryptor.Encrypt(filename, $"Nm5BycFeL3AucSmN_{userId}");
        encryptedFilename = Uri.EscapeDataString(encryptedFilename);
        return $"{portalPath}/FieldAssistPOC/Download/ReportsDownloadAnonymous?encryptedFileName={encryptedFilename}&userId={userId}";
    }

    public string GetPublicPath(string filename)
    {
        var path = _container.Uri.AbsoluteUri + "/" + filename;
        path = path.Replace("https://locationsnetwork.blob.core.windows.net", "https://static.fieldassist.io");
        //25 jan 2022; Asana: https://app.asana.com/0/305436650865282/1201691116024935/f; Change: Add WhiteSpace Check
        return !string.IsNullOrWhiteSpace(filename) ? path : null;
    }

    public async Task<bool> IsExists(string file)
    {
        var blockBlob = _container.GetBlobClient(file);
        return await blockBlob.ExistsAsync().ConfigureAwait(false);
    }

    public async Task<T> GetJson<T>(string name)
    {
        return JsonSerializer.Deserialize<T>(await ReadBlobContentAsync(name).ConfigureAwait(false));
    }

    /// <summary>
    /// Asynchronously retrieves and deserializes JSON content from a blob using streaming.
    /// </summary>
    /// <typeparam name="T">The type to deserialize the JSON content into.</typeparam>
    /// <param name="name">The name of the blob to read from.</param>
    /// <returns>A Task representing the asynchronous operation, containing the deserialized object of type T.</returns>
    /// <remarks>
    /// This method is designed to efficiently handle JSON blobs of any size, including large ones.
    /// It uses streaming to read the blob content, which helps to minimize memory usage.
    /// This approach is particularly useful when:
    /// - Dealing with large JSON blobs that might cause memory issues if loaded entirely into memory.
    /// </remarks>
    /// <exception cref="JsonException">Thrown when the JSON is invalid or cannot be deserialized into the specified type.</exception>
    /// <exception cref="RequestFailedException">Thrown when there's an issue accessing the blob in Azure Storage.</exception>
    public async Task<T> GetJsonByStream<T>(string name)
    {
        var blobClient = _container.GetBlobClient(name);

        await using (var stream = await blobClient.OpenReadAsync())
        {
            return await JsonSerializer.DeserializeAsync<T>(stream);
        }
    }

    public async Task<List<T>> GetJsonResultsAsync<T>(string folderPath, JsonSerializerOptions jsonSerializerOptions = null, string fileName = null)
    {
        List<T> resultList = [];

        // List all blobs in the specified folder
        await foreach (var blobItem in _container.GetBlobsAsync(prefix: folderPath).ConfigureAwait(false))
        {
            var fileExtension = fileName != null ? $"{fileName}.json" : ".json";
            // Skip if not a JSON file
            if (!blobItem.Name.EndsWith(fileExtension, StringComparison.OrdinalIgnoreCase))
            {
                continue;
            }

            // Download the blob content
            var blobClient = _container.GetBlobClient(blobItem.Name);

            using var memoryStream = new MemoryStream();
            await blobClient.DownloadToAsync(memoryStream).ConfigureAwait(false);
            memoryStream.Position = 0; // Reset to beginning of stream

            using var reader = new StreamReader(memoryStream);
            var json = await reader.ReadToEndAsync().ConfigureAwait(false);
            try
            {
                // Handle double-encoded JSON
                var newString = JsonSerializer.Deserialize<string>(json, jsonSerializerOptions);
                if (newString != null)
                {
                    var data = JsonSerializer.Deserialize<T>(newString, jsonSerializerOptions);
                    resultList.Add(data);
                }
                else
                {
                    var data = JsonSerializer.Deserialize<T>(json, jsonSerializerOptions);
                    resultList.Add(data);
                }
            }
            catch (JsonException ex)
            {
                // Log or handle deserialization errors
                Console.WriteLine($"Error deserializing {blobItem.Name}: {ex.Message}");
            }
        }

        return resultList;
    }
}

public class BlobReaderUnknown
{
    protected readonly BlobServiceClient _service;

    public BlobReaderUnknown(string storageConnectionString)
    {
        _service = new BlobServiceClient(storageConnectionString);
    }

    public async Task<T> GetJsonByStream<T>(string containerName, string name)
    {
        var container = _service.GetBlobContainerClient(containerName);
        var blobClient = container.GetBlobClient(name);
        await using (var stream = await blobClient.OpenReadAsync())
        {
            return await JsonSerializer.DeserializeAsync<T>(stream);
        }
    }

    public string GetPublicPath(string containerName, string filename)
    {
        var container = _service.GetBlobContainerClient(containerName);
        var path = container.Uri.AbsoluteUri + "/" + filename;
        path = path.Replace("https://locationsnetwork.blob.core.windows.net", "http://static.fieldassist.io");
        //25 jan 2022; Asana: https://app.asana.com/0/305436650865282/1201691116024935/f; Change: Add WhiteSpace Check
        return !string.IsNullOrWhiteSpace(filename) ? path : null;
    }

    public async Task<Stream> DownloadFileAsStreamAsync(string containerName, string blobName, CancellationToken ct)
    {
        var container = _service.GetBlobContainerClient(containerName);
        var blobClient = container.GetBlobClient(blobName);
        var memoryStream = new MemoryStream();
        await blobClient.DownloadToAsync(memoryStream, ct).ConfigureAwait(false);
        memoryStream.Position = 0;
        return memoryStream;
    }
}
