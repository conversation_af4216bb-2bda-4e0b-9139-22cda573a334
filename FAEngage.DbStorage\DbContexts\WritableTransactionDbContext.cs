﻿using FAEngage.Core.Models.TransactionDbModels;
using Microsoft.EntityFrameworkCore;

namespace FAEngage.DbStorage.DbContexts;

public class WritableTransactionDbContext(DbContextOptions<WritableTransactionDbContext> options) : DbContext(options)
{
    public DbSet<FAManagerEngageLog> FAManagerEngageLogs { get; set; }
    public DbSet<FAEngageLogWhatsapp> FAEngageLogWhatsapps { get; set; }
    public DbSet<FAEngageLog> FAEngageLogs { get; set; }
}