﻿using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums;

public enum DistributorChannel
{
    [Display(Name = "-- ALL --")]
    All = 0,

    [Display(Name = "Super Stockist > Sub-Stockist > Retailer")]
    SuperStockist = 1,

    [Display(Name = "Stockist > Retailer")]
    Stockist = 2
}

public enum DistributorGrade
{
    [Display(Name = "")]
    Unknown = 0,

    [Display(Name = "Urban")]
    Urban = 1,

    [Display(Name = "Semi Urban")]
    SemiUrban = 2,

    [Display(Name = "Metro")]
    Metro = 3,

    [Display(Name = "Non Metro")]
    NonMetro = 4,

    [Display(Name = "Rural")]
    Rural = 5
}
