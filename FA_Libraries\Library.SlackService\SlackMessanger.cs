﻿using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Library.SlackService.Model;

namespace Library.SlackService;

public class CSTSlackMessanger : SlackMessanger
{
    public CSTSlackMessanger() : base(
        "*****************************************************************************")
    {
    }
}

public abstract class SlackMessanger
{
    private readonly HttpClient _client;
    private readonly JsonSerializerOptions _jsonSerializerSettings;
    private readonly string _webhookurl;

    protected SlackMessanger(
        string webhookurl = "*****************************************************************************")
    {
        _webhookurl = webhookurl;
        _client = new HttpClient();
        _jsonSerializerSettings = new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
    }

    public async Task SendToSlack(string message, string botName, string channel)
    {
        var slackMessage = new SlackMessage { Channel = channel, Username = botName, Text = message };
        await SendToSlack(slackMessage).ConfigureAwait(false);
    }

    public async Task SendToSlack(ISlackMessage slackMessage)
    {
        var content = new StringContent(JsonSerializer.Serialize(slackMessage, _jsonSerializerSettings),
            Encoding.UTF8, "application/json");
        var result = await _client.PostAsync(_webhookurl, content).ConfigureAwait(false);
        result.EnsureSuccessStatusCode();
    }
}

public class SMSConfigSlackMessanger : SlackMessanger
{
}
