﻿using FAEngage.Core.Models;

namespace FAEngage.Core.MasterRepositories;

public interface IJourneyCycleRepository
{
    //Task<JourneyCycleMonthYear> GetJCMonthYear(long companyId, DateTime date);
    Task<IEnumerable<JourneyCycleMonthYear>> GetJourneyCalendar(long companyId, DateTime date, long? zoneId = null);
    Task<DateRangeForMonthRange> GetDateRangeForMonthRange(long companyId, int startMonth, int endMonth, int year);
}