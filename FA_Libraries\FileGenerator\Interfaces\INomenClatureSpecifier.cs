﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Libraries.CommonEnums;

namespace FileGenerator.Interfaces;

public interface INomenclatureSpecifier
{
    Task<CompanyNomenclatureSpecifier> GetSpecifierForCompany(long companyId, bool getAll = false);
    Task<Dictionary<string, string>> GetDictionary(long companyId, bool getAll = false);
}

public class CompanyNomenclatureSpecifier
{
    private readonly Dictionary<string, string> AdditionalNomenclature;
    private readonly Dictionary<string, string> dictionary;

    public CompanyNomenclatureSpecifier(Dictionary<string, string> dictionary)
    {
        this.dictionary = dictionary;
    }

    public string GetHeaderNameConsideringAN(string name)
    {
        if (AdditionalNomenclature == null)
        {
            return GetHeaderName(name);
        }

        return dictionary.TryGetValue(name, out var value) ? value :
            AdditionalNomenclature.TryGetValue(name, out var additonalNom) ? additonalNom : name;
    }

    public Dictionary<string, string> GetAllNomenClaturesForCompany()
    {
        return dictionary;
    }

    public string GetHeaderName(string name)
    {
        return dictionary.TryGetValue(name, out var value) ? value : name;
    }

    public string GetNomenclatureForRank(string rankNo)
    {
        var roleDictionary = new Dictionary<string, string>
        {
            ["0"] = "ESM",
            ["1"] = "ASM",
            ["2"] = "RSM",
            ["3"] = "ZSM",
            ["4"] = "NSM",
            ["5"] = "GSM"
        };
        var name = roleDictionary.TryGetValue(rankNo, out var value) ? value : "Unknown";
        return GetHeaderName(name);
    }

    public Dictionary<string, string> GetRankNomenclatureDictionary()
    {
        var roleItems = new List<string>
        {
            "ESM",
            "ASM",
            "RSM",
            "ZSM",
            "NSM",
            "GSM"
        };
        return roleItems.Where(r => dictionary.ContainsKey(r)).ToDictionary(r => r, r => dictionary[r]);
    }

    public string GetUserNomeclature(PortalUserRole portalRole)
    {
        var roleDictionary = new Dictionary<PortalUserRole, string>
        {
            [PortalUserRole.ClientEmployee] = "ESM",
            [PortalUserRole.AreaSalesManager] = "ASM",
            [PortalUserRole.RegionalSalesManager] = "RSM",
            [PortalUserRole.ZonalSalesManager] = "ZSM",
            [PortalUserRole.NationalSalesManager] = "NSM",
            [PortalUserRole.GlobalSalesManager] = "GSM"
        };
        var name = roleDictionary.ContainsKey(portalRole) ? roleDictionary[portalRole] : "Unknown";
        return GetHeaderName(name);
    }

    public string ConvertStringUsingDictionary(string input)
    {
        return string.Join(" ", input.Split(' ')
            .Select(word => dictionary.TryGetValue(word, out var replacement) ? replacement : word));
    }

    public string GetKeyValue(string name)
    {
        return dictionary.TryGetValue(name, out var value) ? value : name;
    }
}
