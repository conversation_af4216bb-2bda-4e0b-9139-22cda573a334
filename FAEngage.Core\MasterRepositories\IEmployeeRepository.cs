﻿using FAEngage.Core.Models;
using Libraries.CommonEnums;

namespace FAEngage.Core.MasterRepositories
{
    public interface IEmployeeRepository
    {
        Task<List<Employee>> GetAllEmployeesSRAndDSR(long companyId);
        Task<List<Employee>> GetAllEmployees(long companyId, List<long> userIds, bool isIncludeDeactiveUsers = false);
        Task<UserWithManagers?> GetEmployeeWithHierarchyWithNewId(long companyId, long empId);
        Task<Employee> GetActiveEmployeeById(long id);
        Task<string?> GetManagerToken(long userId, long companyId);
        Task<string?> GetFieldUserToken(long userId, long companyId);
        Task<List<Employee>> GetFieldUserIdsUnderManagerModel(long companyId, PortalUserRole userRole, List<long> userId, EmployeeType? userType = null);
        Task<DeviceEntityMin> GetFieldUserAlertTypeAsync(long userId, long companyId);
        Task<List<Employee>> GetAllEmployees(long companyId);
    }
}