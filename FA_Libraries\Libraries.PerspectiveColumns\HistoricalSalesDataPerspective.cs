﻿using System;
using System.Collections.Generic;
using Libraries.CommonEnums;
using Libraries.CommonEnums.Helpers;
using Libraries.PerspectiveColumns.Interface;

namespace Libraries.PerspectiveColumns;

public class HistoricalSalesDataPerspective : IPerspective
{
    private readonly LinkNames linkNames;

    public HistoricalSalesDataPerspective(Dictionary<string, string> nomenclatureDict)
    {
        linkNames = LinkNames.GetLinkNames(nomenclatureDict);
    }

    public List<PerspectiveColumnModel> Columns => new()
    {
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.GlobalSalesManager,
            Name = "GSM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.NationalSalesManager,
            Name = "NSM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.ZonalSalesManager,
            Name = "ZSM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.RegionalSalesManager,
            Name = "RSM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.AreaSalesManager,
            Name = "ASM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.Employee,
            Name = "ESM",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "Field User Name",
            Name = "FieldUserName",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "Field User Rank",
            Name = "FieldUserRank",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "Field User HQ",
            Name = "FieldUserHQ",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "Field User ERP ID",
            Name = "FieldUserERPID",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "User Designation",
            Name = "UserDesignation",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales",
            DisplayName = "Distributor",
            Name = "Distributor",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales",
            DisplayName = "Distributor Erp Id",
            Name = "DistributorErpId",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Beat,
            Name = "Beat",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Territory,
            Name = "Territory",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Zone,
            Name = "Zone",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Region,
            Name = "Region",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsPivot = true,
            IsOtherMeasure = true
        },
        new PerspectiveColumnModel
        {
            Attribute = linkNames.Outlets,
            DisplayName = linkNames.Outlets,
            Name = "Shop",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new PerspectiveColumnModel
        {
            Attribute = linkNames.Outlets,
            DisplayName = linkNames.Outlets + " ERPID",
            Name = "ShopERPID",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new PerspectiveColumnModel
        {
            Attribute = linkNames.Outlets,
            DisplayName = linkNames.Outlets + " Owners Name",
            Name = "ShopOwnersName",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = linkNames.Outlets,
            DisplayName = linkNames.Outlets + " Owners Number",
            Name = "ShopOwnersNumber",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = linkNames.Outlets,
            DisplayName = linkNames.Outlets + " Address",
            Name = "ShopAddress",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = linkNames.Outlets,
            DisplayName = linkNames.Outlets + " Market",
            Name = "ShopMarket",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = linkNames.Outlets,
            DisplayName = linkNames.Outlets + " Town",
            Name = "ShopSubCity",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new PerspectiveColumnModel
        {
            Attribute = linkNames.Outlets,
            DisplayName = linkNames.Outlets + " City",
            Name = "ShopCity",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new PerspectiveColumnModel
        {
            Attribute = linkNames.Outlets,
            DisplayName = linkNames.Outlets + " State",
            Name = "ShopState",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new PerspectiveColumnModel
        {
            Attribute = linkNames.Outlets,
            DisplayName = linkNames.Outlets + " Type",
            Name = "ShopType",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = linkNames.Outlets,
            DisplayName = linkNames.Outlets + " Segmentation",
            Name = "ShopSegmentation",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = "Conversion Factor",
            Name = "ProductStandardUnitConversionFactor",
            IsMeasure = false,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = $"{linkNames.StdUnit}",
            Name = "ProductStandardUnit",
            IsMeasure = false,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = $"{linkNames.Unit}",
            Name = "ProductUnit",
            IsMeasure = false,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = "ProductERPID",
            Name = "ProductERPID",
            IsMeasure = true,
            IsDimension = true,
            IsPivot = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = "Product",
            Name = "ProductName",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = "SecondaryCategory",
            Name = "SecondaryCategory",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = "PrimaryCategory",
            Name = "PrimaryCategory",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = "ProductDivision",
            Name = "ProductDivision",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Time",
            DisplayName = "Date",
            Name = "InvoiceDate",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Time",
            DisplayName = "Month",
            Name = "Month",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = $"Order Qty ({linkNames.Unit})",
            Name = "OrderInUnits",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = $"Order Qty ({linkNames.StdUnit})",
            Name = "OrderQtyInStdUnit",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Value",
            Name = "Value",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Orders",
            DisplayName = "InvoiceNumber",
            Name = "InvoiceNumber",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        }
    };

    List<PerspectiveColumnModel> IPerspective.Columns
    {
        get => Columns;
        set => throw new NotImplementedException();
    }
}
