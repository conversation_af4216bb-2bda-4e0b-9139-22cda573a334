﻿using System;
using System.Collections.Generic;
using Libraries.CommonEnums;
using Libraries.CommonEnums.Helpers;
using Libraries.PerspectiveColumns.Interface;

namespace Libraries.PerspectiveColumns;

public class DistributorPerformancePerspective : IPerspective
{
    private readonly LinkNames linkNames;

    public DistributorPerformancePerspective(Dictionary<string, string> nomenclatureDict)
    {
        linkNames = LinkNames.GetLinkNames(nomenclatureDict);
    }

    public List<PerspectiveColumnModel> Columns => new()
    {
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L2Position,
            Name = "L2Position",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L2Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L2Position + " Code",
            Name = "L2Position_Code",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L2Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L2Position + " User ErpId",
            Name = "L2Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L2Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L2Position + " User",
            Name = "L2Position_User",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L2Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L3Position,
            Name = "L3Position",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L3Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L3Position + " Code",
            Name = "L3Position_Code",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L3Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L3Position + " User ErpId",
            Name = "L3Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L3Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L3Position + " User",
            Name = "L3Position_User",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L3Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L4Position,
            Name = "L4Position",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L4Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L4Position + " Code",
            Name = "L4Position_Code",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L4Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L4Position + " User ErpId",
            Name = "L4Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L4Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L4Position + " User",
            Name = "L4Position_User",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L4Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L5Position,
            Name = "L5Position",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L5Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L5Position + " Code",
            Name = "L5Position_Code",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L5Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L5Position + " User ErpId",
            Name = "L5Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L5Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L5Position + " User",
            Name = "L5Position_User",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L5Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L6Position,
            Name = "L6Position",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L6Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L6Position + " Code",
            Name = "L6Position_Code",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L6Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L6Position + " User ErpId",
            Name = "L6Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L6Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L6Position + " User",
            Name = "L6Position_User",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L6Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L7Position,
            Name = "L7Position",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L7Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L7Position + " Code",
            Name = "L7Position_Code",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L7Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L7Position + " User ErpId",
            Name = "L7Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L7Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L7Position + " User",
            Name = "L7Position_User",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L7Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L8Position,
            Name = "L8Position",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L8Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L8Position + " Code",
            Name = "L8Position_Code",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L8Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L8Position + " User ErpId",
            Name = "L8Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L8Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L8Position + " User",
            Name = "L8Position_User",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            SubGroup = linkNames.L8Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "FieldUser Name",
            Name = "L1Position_FieldUser",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Rank",
            Name = "L1Position_UserRank",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "FieldUser HQ",
            Name = "L1Position_UserHQ",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Field User ERP ID",
            Name = "L1Position_UserERP",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "User Designation",
            Name = "L1Position_UserDesignation",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Employee " + linkNames.AttributeText1,
            Name = "EmployeeAttributeText1",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Employee " + linkNames.AttributeText2,
            Name = "EmployeeAttributeText2",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Employee " + linkNames.AttributeNumber1,
            Name = "EmployeeAttributeNumber1",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Employee " + linkNames.AttributeNumber2,
            Name = "EmployeeAttributeNumber2",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Date Of Joining",
            Name = "DateOfJoining",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Email Id",
            Name = "EmailId",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "User Type",
            Name = "UserType",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Contact Number",
            Name = "ContactNumber",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.GlobalSalesManager + " User ErpId",
            Name = "GSMErpId",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.GlobalSalesManager,
            Name = "GSM",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.NationalSalesManager + " User ErpId",
            Name = "NSMErpId",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.NationalSalesManager,
            Name = "NSM",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.ZonalSalesManager + " User ErpId",
            Name = "ZSMErpId",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.ZonalSalesManager,
            Name = "ZSM",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.RegionalSalesManager + " User ErpId",
            Name = "RSMErpId",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.RegionalSalesManager,
            Name = "RSM",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.AreaSalesManager + " User ErpId",
            Name = "ASMErpId",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = linkNames.AreaSalesManager,
            Name = "ASM",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "User Designation",
            Name = "L1Position_UserDesignation",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Level5,
            Name = "Level5",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Level6,
            Name = "Level6",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Level7,
            Name = "Level7",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Region,
            Name = "Region",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Zone,
            Name = "Zone",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Channel Partner",
            DisplayName = linkNames.Distributor,
            Name = "Distributor",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Channel Partner",
            DisplayName = linkNames.Distributor + " Erp ID",
            Name = "DistributorErpId",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Channel Partner",
            DisplayName = linkNames.Distributor + " Address",
            Name = "DistributorAddress",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Channel Partner",
            DisplayName = "Stockist Type",
            Name = "StockistType",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Channel Partner",
            DisplayName = linkNames.SuperStockist,
            Name = "SuperStockist",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Channel Partner",
            DisplayName = linkNames.SuperStockist + " Erp ID",
            Name = "SuperStockistErpID",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Channel Partner",
            DisplayName = linkNames.SuperStockist + " Address",
            Name = "SuperstockistAddress",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Time",
            DisplayName = "Date",
            Name = "Date",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsPivot = true,
            pivotCategory = PivotCategory.Time
        },
        new PerspectiveColumnModel
        {
            Attribute = "Time",
            DisplayName = "Week",
            Name = "Week",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsPivot = true,
            pivotCategory = PivotCategory.Time
        },
        new PerspectiveColumnModel
        {
            Attribute = "Time",
            DisplayName = "Month",
            Name = "Month",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsPivot = true,
            pivotCategory = PivotCategory.Time
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "Opening Stock Unit",
            Name = "OpeningStockQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "Opening Stock StdUnit",
            Name = "OpeningStockInStdUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "Opening Stock SuperUnit",
            Name = "OpeningStockSuperUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "Opening Stock Value",
            Name = "OpeningStockInRevenue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "Closing Stock Unit",
            Name = "ClosingStockInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "Closing Stock StdUnit",
            Name = "ClosingStockInStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "Closing Stock SuperUnit",
            Name = "ClosingStockSuperUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "Closing Stock Value",
            Name = "ClosingStockInRevenue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "Damaged Stock Unit",
            Name = "DamagedQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "Damaged Stock StdUnit",
            Name = "DamagedQtyStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "Damaged Stock SuperUnit",
            Name = "DamagedQtySuperUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "Damaged Stock Value",
            Name = "DamagedValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "Expired Stock Unit",
            Name = "ExpiredQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "Expired Stock StdUnit",
            Name = "ExpiredQtyStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "Expired Stock SuperUnit",
            Name = "ExpiredQtySuperUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "Expired Stock Value",
            Name = "ExpiredValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "Stock Cover",
            Name = "StockCover",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "GRN Qty Unit",
            Name = "GRNQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "GRN Qty StdUnit",
            Name = "GRNQtyStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "GRN Qty SuperUnit",
            Name = "GRNQtySuperUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "GRN Value",
            Name = "GRNValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "Pending GRN Count",
            Name = "PendingGRNCount",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "Pending GRN Qty Unit",
            Name = "PendingGRNQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "Pending GRN Qty StdUnit",
            Name = "PendingGRNQtyStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "Pending GRN Qty SuperUnit",
            Name = "PendingGRNQtySuperUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Inventory",
            DisplayName = "Pending GRN Value",
            Name = "PendingGRNValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Primary Sales",
            DisplayName = "Primary Sales Count",
            Name = "PrimaryOrderCount",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Primary Sales",
            DisplayName = "Primary Order Qty Unit",
            Name = "PrimarySalesOrderQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Primary Sales",
            DisplayName = "Primary Order Qty StdUnit",
            Name = "PrimarySalesOrderQtyStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Primary Sales",
            DisplayName = "Primary Order Qty SuperUnit",
            Name = "PrimarySalesOrderQtySuperUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Primary Sales",
            DisplayName = "Primary Order Value",
            Name = "PrimarySalesOrderValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Primary Sales",
            DisplayName = "Primary Invoice Count",
            Name = "PrimaryInvoiceCount",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Primary Sales",
            DisplayName = "Primary Invoice Qty Unit",
            Name = "PrimaryInvoiceQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Primary Sales",
            DisplayName = "Primary Invoice Qty StdUnit",
            Name = "PrimaryInvoiceQtyStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Primary Sales",
            DisplayName = "Primary Invoice Qty SuperUnit",
            Name = "PrimaryInvoiceQtySuperUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Primary Sales",
            DisplayName = "Primary Invoice Value",
            Name = "PrimaryInvoiceValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Primary Sales",
            DisplayName = "Primary Return Qty Unit",
            Name = "PrimaryReturnInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Primary Sales",
            DisplayName = "Primary Return Qty StdUnit",
            Name = "PrimaryReturnInStdUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Primary Sales",
            DisplayName = "Primary Return Qty SuperUnit",
            Name = "PrimaryReturnInSuperUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Primary Sales",
            DisplayName = "Primary Return Value",
            Name = "PrimaryReturnValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Reprimary Sales",
            DisplayName = "RePrimary Order Count",
            Name = "RePrimaryOrderCount",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Reprimary Sales",
            DisplayName = "RePrimary Order Qty Unit",
            Name = "RePrimaryOrderQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Reprimary Sales",
            DisplayName = "RePrimary Order Qty StdUnit",
            Name = "RePrimaryOrderQtyStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Reprimary Sales",
            DisplayName = "RePrimary Order Qty SuperUnit",
            Name = "RePrimaryOrderQtySuperUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Reprimary Sales",
            DisplayName = "RePrimary Order Value",
            Name = "RePrimaryOrderValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Reprimary Sales",
            DisplayName = "RePrimary Invoice Count",
            Name = "RePrimaryInvoiceCount",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Reprimary Sales",
            DisplayName = "RePrimary Invoice Qty Unit",
            Name = "RePrimaryInvoiceQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Reprimary Sales",
            DisplayName = "RePrimary Invoice Qty StdUnit",
            Name = "RePrimaryInvoiceQtyStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Reprimary Sales",
            DisplayName = "RePrimary Invoice Qty SuperUnit",
            Name = "RePrimaryInvoiceQtySuperUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Reprimary Sales",
            DisplayName = "RePrimary Invoice Value",
            Name = "RePrimaryInvoiceValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Reprimary Sales",
            DisplayName = "RePrimary Return Qty Unit",
            Name = "RePrimaryReturnInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Reprimary Sales",
            DisplayName = "RePrimary Return Qty StdUnit",
            Name = "RePrimaryReturnInStdUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Reprimary Sales",
            DisplayName = "RePrimary Return Qty SuperUnit",
            Name = "RePrimaryReturnInSuperUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Reprimary Sales",
            DisplayName = "RePrimary Return Value",
            Name = "RePrimaryReturnValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "TC",
            Name = "TC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Secondary Order Count",
            Name = "SOCount",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Secondary Order Qty Unit",
            Name = "SOQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Secondary Order Qty StdUnit",
            Name = "SOQtyStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Secondary Order Qty SuperUnit",
            Name = "SOQtySuperUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Secondary Order Value",
            Name = "SOValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Secondary Cancelled Qty Unit",
            Name = "SecondaryCancelled",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Secondary Cancelled Qty StdUnit",
            Name = "SecondaryCancelledinStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Secondary Cancelled Qty SuperUnit",
            Name = "SecondaryCancelledinSuperUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Secondary Cancelled Value",
            Name = "SecondaryCancelledValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "SFA Order Count",
            Name = "SOCount_SFA",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "SFA Order Qty Unit",
            Name = "SOQty_SFA",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "SFA Order Qty StdUnit",
            Name = "SOQtyStdUnit_SFA",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "SFA Order Qty SuperUnit",
            Name = "SOQtySuperUnit_SFA",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "SFA Order Value",
            Name = "SOValue_SFA",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Direct Order Count",
            Name = "SOCount_Direct",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Direct Order Qty Unit",
            Name = "SOQty_Direct",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Direct Order Qty StdUnit",
            Name = "SOQtyStdUnit_Direct",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Direct Order Qty SuperUnit",
            Name = "SOQtySuperUnit_Direct",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Direct Order Value",
            Name = "SOValue_Direct",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Total Lines Cut",
            Name = "LinesCut_Sales",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Total Invoice Count",
            Name = "InvoiceCount",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Total Invoice Qty Unit",
            Name = "SecondaryInvoiceQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Total Invoice Qty StdUnit",
            Name = "SecondaryInvoiceQtyStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Total Invoice Qty SuperUnit",
            Name = "SecondaryInvoiceQtySuperUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Total Invoice Value",
            Name = "SecondaryInvoiceValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "L3M Avg. Total Invoice Qty Unit",
            Name = "L3MAvgSecondaryInvoiceQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "L3M Avg. Total Invoice Qty StdUnit",
            Name = "L3MAvgSecondaryInvoiceQtyStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "L3M Avg. Total Invoice Qty SuperUnit",
            Name = "L3MAvgSecondaryInvoiceQtySuperUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "L3M Avg. Total Invoice Value",
            Name = "L3MAvgSecondaryInvoiceValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Same Day Invoice  Count",
            Name = "SICount_AgainstSameDaySO",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Same Day Invoice Qty unit",
            Name = "SIQty_AgainstSameDaySO",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Same Day Invoice Qty StdUnit",
            Name = "SIQtyStdUnit_AgainstSameDaySO",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Same Day Invoice Qty SuperUnit",
            Name = "SIQtySuperUnit_AgainstSameDaySO",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Same Day Invoice Value",
            Name = "SIValue_AgainstSameDaySO",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Partial Invoice Count",
            Name = "SecondarySICount_Partial",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Partial Invoice Qty Unit",
            Name = "SecondarySIQty_Partial",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Partial Invoice Qty StdUnit",
            Name = "SecondarySIQtyStdUnit_Partial",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Partial Invoice Qty SuperUnit",
            Name = "SecondarySIQtySuperUnit_Partial",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Secondary Return Qty Unit",
            Name = "SecondaryReturnInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Secondary Return Qty StdUnit",
            Name = "SecondaryReturnInStdUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Secondary Return Qty SuperUnit",
            Name = "SecondaryReturnInSuperUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Secondary Sales",
            DisplayName = "Secondary Return Value",
            Name = "SecondaryReturnValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        }
    };

    List<PerspectiveColumnModel> IPerspective.Columns
    {
        get => Columns;
        set => throw new NotImplementedException();
    }
}
