﻿using System.Linq;
using Libraries.CommonEnums;
using Microsoft.AspNetCore.Authorization;

namespace Libraries.Authorization;

public class F2KAuthorizeAttribute : AuthorizeAttribute
{
    public F2KAuthorizeAttribute(params PortalUserRole[] Roles)
    {
        base.Roles = string.Join(", ", Roles.Select(r => r.ToString()));
        this.Roles = Roles;
    }

    public PortalUserRole[] Roles { get; set; }
    //[AllowAnonymous]
    //public override void OnAuthorization(AuthorizationContext filterContext)
    //{
    //    var action = filterContext.ActionDescriptor;
    //    if (action.IsDefined(typeof(F2KAuthorizeOverrideAttribute), true))
    //    {
    //        return;
    //    }

    //    if (action.IsDefined(typeof(F2KAuthorizeAllowAnonymousAttribute), true))
    //    {
    //        return;
    //    }

    //    base.OnAuthorization(filterContext);
}
