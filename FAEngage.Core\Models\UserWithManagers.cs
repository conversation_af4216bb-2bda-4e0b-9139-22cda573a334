﻿using Libraries.CommonEnums;

namespace FAEngage.Core.Models;

public class UserWithManagers
{
    public long Id { get; set; }
    public string Name { get; set; }
    public long? OldTableId { get; set; }
    public long? ParentId { get; set; }
    public string ParentName { get; set; }
    public long? Parent2Id { get; set; }
    public string Parent2Name { get; set; }
    public long? Parent3Id { get; set; }
    public string Parent3Name { get; set; }
    public long? Parent4Id { get; set; }
    public string Parent4Name { get; set; }
    public long? Parent5Id { get; set; }
    public string Parent5Name { get; set; }
    public bool IsDeactive { get; set; }
    public EmployeeRank Rank { get; set; }
    public long CompanyId { get; set; }
    public PortalUserRole UserRole { get; set; }
    public string ContactNo { get; set; }
    public string LocalName { get; set; }
    public string ErpId { get; set; }
    public bool IsOrderBookingDisabled { get; set; }
    public EmployeeType EmployeeType { get; set; }
    public string Zone { get; set; }
    public string Region { get; set; }
    public DateTime? DateOfJoining { get; set; }
    public DateTime? DateOfLeaving { get; set; }
    public DateTime DateOfCreation { get; set; }
    public long NewId { get; set; }
    public long? DesignationId { get; set; }
    public string EmployeeAttributeText1 { get; set; }
    public string EmployeeAttributeText2 { get; set; }
    public bool? EmployeeAttributeBoolean1 { get; set; }
    public bool? EmployeeAttributeBoolean2 { get; set; }
    public double? EmployeeAttributeNumber1 { get; set; }
    public double? EmployeeAttributeNumber2 { get; set; }
    public DateTime? EmployeeAttributeDate { get; set; }
}