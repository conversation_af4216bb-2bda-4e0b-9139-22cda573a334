﻿namespace Libraries.Cryptography;

public class TokenHelper
{
    private const int rotateDigits = 3;
    private const uint salt1 = 89854;
    private const uint salt2 = 32784;

    private static uint RotateRight(uint v)
    {
        return (v >> rotateDigits) | (v << (32 - rotateDigits));
    }

    public static long GetConnectionId(uint token)
    {
        var t = RotateRight(token) ^ salt2;
        return RotateRight(t) ^ salt1;
    }

    public static uint GetToken(long connId)
    {
        return (uint)connId;
    }

    //for gt app apis
    public static long GetConnectionId(long token)
    {
        // var t = RotateRight(token) ^ salt2;
        return token;
        /*(RotateRight(t) ^ salt1)*/
    }
}
