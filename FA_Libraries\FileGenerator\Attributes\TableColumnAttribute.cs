﻿using System;

namespace FileGenerator.Attributes;

public enum ColGroupType
{
    None,
    Toggle1,
    Toggle2
}

public enum CompanySetting
{
    NotApplicable,
    UsesPositionCodes,
    HighestPositionLevel,
    HighestGeoHierarchy,
    UsesFAUnify
}

public enum DataTypeAttribute
{
    String = 0,
    Decimal = 1,
    Date = 2,
    Time = 4,
    Percentage = 5,
    PercentageInt = 6,
    JustPercentage = 7,
    Url = 8
}

public enum PDFColCategory
{
    None,
    Info,
    Table,
    Group
}

public enum Requirement
{
    HideIfNull,
    Required,
    HideIfZero,
    SettingBased,
    SpecificSettingBased,
    Selected
}

public class Conditional
{
    public int TargetLower { get; set; }
}

[AttributeUsage(AttributeTargets.Property)]
public class TableFieldAttribute : Attribute
{
    private const string CellFormat_Decimal = "#,##0.00";
    private const string CellFormat_Date = "dd-MM-yyyy";
    private const string CellFormat_Time = "HH:mm";
    private const string CellFormat_Percent = "0.00%";
    private const string CellFormat_JustPercent = "0.00\\%";
    private const string CellFormat_PercentInt = "0%";

    public TableFieldAttribute(string ColumnName)
    {
        this.ColumnName = ColumnName;
    }

    public string ColumnName { get; }
    public Requirement ColumnRequirement { get; set; }
    public DataTypeAttribute ColumnDataType { get; set; }
    public bool NomenclatureRequirement { get; set; }
    public bool UseAdditionalNomenclature { get; set; }
    public string NomenclatureUpdated { get; set; }
    public string CellFormat => GetCellFormat();
    public string ConditionalMatchColumn { get; set; }
    public string ConditionalFormat { get; set; }
    public string ConditionalSeparators { get; set; }
        public ColGroupType ColGroupType { get; set; }
    public string HyperLinkText { get; set; }
    public PDFColCategory PDFColCategory { get; set; }
    public CompanySetting[] CompanySettingsToCheck { get; set; }
    public bool IsSettingEnabled { get; set; }
    public bool ShowImage { get; set; }

    public string GetCellFormat()
    {
        switch (ColumnDataType)
        {
            case DataTypeAttribute.Decimal:
                return CellFormat_Decimal;
            case DataTypeAttribute.Date:
                return CellFormat_Date;
            case DataTypeAttribute.Time:
                return CellFormat_Time;
            case DataTypeAttribute.Percentage:
                return CellFormat_Percent;
            case DataTypeAttribute.PercentageInt:
                return CellFormat_PercentInt;
            case DataTypeAttribute.JustPercentage:
                return CellFormat_JustPercent;
            case DataTypeAttribute.String:
            default:
                return null;
        }
    }
}
