﻿using System.Collections.Generic;

namespace Libraries.CommonModels;

public class FilterConstraintModel
{
    public GeographyConstraintModel GeographyConstraints { get; set; }
    public OutletCohortConstraints OutletCohortConstraints { get; set; }
    public DistributorConstraints DistributorConstraints { get; set; }
    public DistributorModel Distributor { get; set; }
}

public class GeographyConstraintModel
{
    public List<long> RegionIds { get; set; } = new();
    public List<long> ZoneIds { get; set; } = new();
}

public class OutletCohortConstraints
{
    public List<string> Channels { get; set; } = new();
    public List<long> ShopTypes { get; set; } = new();
    public List<string> Segmentations { get; set; } = new();
    public string IsFocused { get; set; }  
    public List<string> CustomTag { get; set; } = new();
    public string AttributeText1 { get; set; }
    public double? AttributeNumber1 { get; set; }  
    public bool? AttributeBoolean1 { get; set; }  
}

public class DistributorConstraints
{
    public List<string> Channels { get; set; } = new();
    public List<string> Segmentations { get; set; } = new();
}

public class DistributorModel
{
    public List<long> DistributorIds { get; set; } = new();
}
