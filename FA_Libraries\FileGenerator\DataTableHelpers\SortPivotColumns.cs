﻿using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace FileGenerator.DataTableHelpers;

public class SortPivotColumns
{
    public static DataTable GetSortedPivotTable(DataTable dt, List<Dictionary<string, int>> sortingDictionaryList)
    {
        if (sortingDictionaryList == null || sortingDictionaryList.Count == 0)
        {
            return dt;
        }

        var ordialCount = 0;
        dt.Columns["Id"].SetOrdinal(ordialCount++);
        foreach (var sortingDictionary in sortingDictionaryList)
        {
            foreach (var item in sortingDictionary)
            {
                if (dt.Columns.Contains(item.Key))
                {
                    dt.Columns[item.Key].SetOrdinal(ordialCount++);
                }
            }
        }

        return dt;
    }

    public static DataTable GetLexicographicallySortedPivotTable(DataTable dt)
    {
        var ordial = 0;
        var cols = dt.Columns.Cast<DataColumn>().Where(x => x.ColumnName != "Id").Select(c => c.ColumnName)
            .OrderBy(y => y).ToList();
        dt.Columns["Id"].SetOrdinal(ordial++);
        foreach (var col in cols)
        {
            dt.Columns[col].SetOrdinal(ordial++);
        }

        return dt;
    }
}
