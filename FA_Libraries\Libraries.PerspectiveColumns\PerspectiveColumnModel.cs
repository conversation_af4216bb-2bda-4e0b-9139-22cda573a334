﻿using System.Collections.Generic;
using Libraries.CommonEnums;
using Library.CommonHelpers;

namespace Libraries.PerspectiveColumns;

public class PerspectiveColumnModel
{
    public PerspectiveColumnModel()
    {
        TimeFrames = new List<NLTFrames>();
        LTimeFrames = new List<LTFrames>();
        NameCategory = new List<NameCategory>();
        SubGroup = DisplayName;
    }

    public string Name { get; set; }
    public string DisplayName { get; set; }
    public string Attribute { get; set; }
    public bool IsDimension { get; set; }
    public bool IsMeasure { get; set; }
    public bool IsOtherMeasure { get; set; }
    public bool IsPivot { get; set; }

    public bool NotForReports { get; set; }

    public PerspectiveMeasure PerspectiveMeasure { get; set; }

    public string AggregationType => string.IsNullOrEmpty(PerspectiveMeasure.GetDisplayName()) ? PerspectiveMeasure.ToString() : PerspectiveMeasure.GetDisplayName();

    public List<NLTFrames> TimeFrames { get; set; }
    public List<LTFrames> LTimeFrames { get; set; }
    public List<NameCategory> NameCategory { get; set; }
    public PivotCategory pivotCategory { get; set; }
    public PerspectiveType PerspectiveType { get; set; }
    public RangeType DataType { get; set; }
    public string SubGroup { get; set; }
    public bool IsMandatory { get; set; }
    public int HierarchyOrder { get; set; }

    public bool IsUsesNomenclature { get; set; }
    public string NomenclatureKey { get; set; }
}

public enum RangeType
{
    Double = 0,
    TimeSpan = 1
}
