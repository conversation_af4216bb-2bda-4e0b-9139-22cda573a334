﻿using Libraries.CommonEnums;

namespace FAEngage.Core.Models.MasterDbModels;

public class ManagerAlert
{
    public long Id { get; set; }

    public string AlertTitle { get; set; }

    public string AlertDescription { get; set; }

    public DateTime AlertServerTime { get; set; }

    public DateTime DeviceTime { get; set; }

    public ManagerAlertAction Action { get; set; }

    public long UserId { get; set; }

    public PortalUserRole UserRole { get; set; }

    public long? EventId { get; set; }

    public long CompanyId { get; set; }

    public string CreationContext { get; set; }

    public long? ReportingManager { get; set; }

    public PortalUserRole ReportingManagerUserRole { get; set; }

    public long? ReportingManagerLevel2 { get; set; }

    public PortalUserRole ReportingManagerLevel2UserRole { get; set; }

    public long? ActionTakenBy { get; set; }

    public DateTime? ActionTakenTime { get; set; }

    public DateTime CreatedAt { get; set; }
    public AlertType AlertType { get; set; }
    public long? RequestorPosition { get; set; }
    public long? ReportingManagerPosition { get; set; }
}