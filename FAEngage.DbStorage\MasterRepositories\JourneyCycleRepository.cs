﻿using System.Text.Json;
using FAEngage.Core.MasterRepositories;
using FAEngage.Core.Models;
using FAEngage.Core.Models.MasterDbModels;
using FAEngage.DbStorage.DbContexts;
using Microsoft.EntityFrameworkCore;

namespace FAEngage.DbStorage.MasterRepositories;

public class JourneyCycleRepository(MasterDbContext db) : IJourneyCycleRepository
{
    public async Task<IEnumerable<JourneyCycleMonthYear>> GetJourneyCalendar(long companyId, DateTime date,
        long? zoneId = null)
    {
        var journeyCalendar = await db.JourneyCalendars.Where(jc =>
            !jc.Deleted && !jc.IsDeactive && jc.CompanyId == companyId && jc.StartDate <= date.Date &&
            jc.EndDate >= date.Date).ToListAsync();
        var calendar = zoneId != null
            ? journeyCalendar
                .Where(jc => JsonSerializer.Deserialize<List<long>>(jc.SelectedZones).Contains(zoneId.Value))
                .FirstOrDefault()
            : journeyCalendar.FirstOrDefault();
        if (calendar != null)
            return await db.JourneyCycles.Include(x => x.JourneyWeeks)
                .Where(j => j.JourneyCalendarId == calendar.Id)
                .Select(d => new JourneyCycleMonthYear
                {
                    MonthStartDate = d.MonthStartDate,
                    MonthEndDate = d.MonthEndDate,
                    Month = d.MonthNumber,
                    Year = calendar.Year,
                    MonthName = d.MonthName,
                    Weeks = d.JourneyWeeks.Select(x => new JourneyWeekDataModel
                    {
                        QuarterNumber = x.QuarterNumber,
                        WeekForQuarter = x.WeekForQuarter,
                        WeekForMonth = x.WeekForMonth,
                        WeekForYear = x.WeekForYear,
                        WeekStartDate = x.WeekStartDate,
                        WeekEndDate = x.WeekEndDate
                    }).ToList()
                }).ToListAsync();
        return new List<JourneyCycleMonthYear>();
    }

    public async Task<DateRangeForMonthRange> GetDateRangeForMonthRange(long companyId, int startMonth,
        int endMonth, int year)
    {
        var journeyCalendar = await db.JourneyCalendars
            .Where(jc => !jc.Deleted && !jc.IsDeactive && jc.CompanyId == companyId && jc.Year == year)
            .ToListAsync();
        var calendarId = journeyCalendar.Select(j => j.Id).FirstOrDefault();
        var journeyCyclesActive = GetCycleForCalendar(calendarId).ToList();

        var startDate = journeyCyclesActive.Where(j => j.MonthNumber == startMonth).Select(j => j.MonthStartDate)
            .FirstOrDefault();
        var endDate = journeyCyclesActive.Where(j => j.MonthNumber == endMonth).Select(j => j.MonthEndDate)
            .FirstOrDefault();
        var endMonthStartDate = journeyCyclesActive.Where(j => j.MonthNumber == endMonth)
            .Select(j => j.MonthStartDate).FirstOrDefault();
        return new DateRangeForMonthRange
        {
            StartDate = startDate,
            EndDate = endDate,
            EndMonthStartDate = endMonthStartDate
        };
    }

    private IQueryable<JourneyCycle> GetCycleForCalendar(long id, bool OnlyActive = false)
    {
        return OnlyActive
            ? db.JourneyCycles.Where(s => s.JourneyCalendarId == id && s.MonthEndDate > DateTime.Now)
            : db.JourneyCycles.Where(s => s.JourneyCalendarId == id);
    }
}