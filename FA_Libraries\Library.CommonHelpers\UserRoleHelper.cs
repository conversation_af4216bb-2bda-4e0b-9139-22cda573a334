﻿using Libraries.CommonEnums;

namespace Library.CommonHelpers;

public class UserRoleHelper
{
    public bool IsAdmin { get; set; }
    public bool IsManager { get; set; }
    public bool IsInternal { get; set; }
}

public static class UserRoleExtensions
{
    public static UserRoleHelper CheckUserRole(this PortalUserRole userRole)
    {
        var a = new UserRoleHelper();
        switch (userRole)
        {
            case PortalUserRole.GlobalAdmin:
            case PortalUserRole.AccountManager:
            case PortalUserRole.CompanyAdmin:
            case PortalUserRole.RegionalAdmin:
                a.IsAdmin = true;
                break;

            case PortalUserRole.GlobalSalesManager:
            case PortalUserRole.NationalSalesManager:
            case PortalUserRole.ZonalSalesManager:
            case PortalUserRole.RegionalSalesManager:
            case PortalUserRole.AreaSalesManager:
                a.IsManager = true;
                break;

            default:
                a.IsAdmin = false;
                a.IsManager = false;
                break;
        }

        return a;
    }

    public static UserRoleHelper CheckUserRoleForAudit(this PortalUserRole userRole)
    {
        var a = new UserRoleHelper();
        switch (userRole)
        {
            case PortalUserRole.GlobalAdmin:
            case PortalUserRole.AccountManager:
                a.IsInternal = true;
                break;
            default:
                a.IsInternal = false;
                break;
        }

        return a;
    }
}
