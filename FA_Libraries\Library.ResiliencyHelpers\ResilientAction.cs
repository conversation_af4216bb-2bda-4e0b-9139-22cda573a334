﻿using System;
using System.Threading.Tasks;

namespace Library.ResiliencyHelpers;

public class ResilientAction
{
    private readonly double maxWaitDurationSeconds;
    private readonly int noOfRetries;

    public ResilientAction(int noOfRetries = 3, TimeSpan? maxWaitDuration = null)
    {
        this.noOfRetries = noOfRetries <= 0 ? 1 : noOfRetries;
        maxWaitDurationSeconds = maxWaitDuration?.TotalSeconds ?? 30;
    }

    public TimeSpan CurrentWaitDuration { get; }

    private async Task DelayNextAttempt(int attemptNo)
    {
        await Task.Delay(TimeSpan.FromSeconds((attemptNo + 1) * (maxWaitDurationSeconds / noOfRetries))).ConfigureAwait(false);
    }

    private T RetryResiliently<T>(Delegate action, int retryCount, Exception firstException)
    {
        try
        {
            return (T)action.DynamicInvoke();
        }
        catch (Exception ex)
        {
            if (retryCount < noOfRetries)
            {
                DelayNextAttempt(retryCount).Wait();
                return RetryResiliently<T>(action, ++retryCount, firstException ?? ex);
            }

            throw new Exception($"Error Even after {retryCount} attempts", firstException ?? ex);
        }
    }

    private void RetryResiliently(Action action, int retryCount, Exception firstException)
    {
        try
        {
            action.Invoke();
        }
        catch (Exception ex)
        {
            if (retryCount < noOfRetries)
            {
                DelayNextAttempt(retryCount).Wait();
                RetryResiliently(action, ++retryCount, firstException ?? ex);
            }
            else
            {
                throw new Exception($"Error Even after {retryCount} attempts", firstException ?? ex);
            }
        }
    }

    private async Task RetryResilientlyAsync<T>(Func<T, Task> action, T param1, int retryCount, Exception firstException)
    {
        try
        {
            await action(param1).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            if (retryCount < noOfRetries)
            {
                await DelayNextAttempt(retryCount).ConfigureAwait(false);
                await RetryResilientlyAsync(action, param1, ++retryCount, firstException ?? ex).ConfigureAwait(false);
            }
            else
            {
                throw new Exception($"Error Even after {retryCount} attempts", firstException ?? ex);
            }
        }
    }

    private async Task<TO> RetryResilientlyAsync<T1, T2, TO>(Func<T1, T2, Task<TO>> action, T1 param1, T2 param2, int retryCount, Exception firstException)
    {
        try
        {
            return await action(param1, param2).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            if (retryCount < noOfRetries)
            {
                await DelayNextAttempt(retryCount).ConfigureAwait(false);
                return await RetryResilientlyAsync(action, param1, param2, ++retryCount, firstException ?? ex).ConfigureAwait(false);
            }

            throw new Exception($"Error Even after {retryCount} attempts", firstException ?? ex);
        }
    }

    private async Task<TO> RetryResilientlyAsync<T1, TO>(Func<T1, Task<TO>> action, T1 param1, int retryCount, Exception firstException)
    {
        try
        {
            return await action(param1).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            if (retryCount < noOfRetries)
            {
                await DelayNextAttempt(retryCount).ConfigureAwait(false);
                return await RetryResilientlyAsync(action, param1, ++retryCount, firstException ?? ex).ConfigureAwait(false);
            }

            throw new Exception($"Error Even after {retryCount} attempts", firstException ?? ex);
        }
    }

    private async Task RetryResilientlyAsync<T1, T2>(Func<T1, T2, Task> action, T1 param1, T2 param2, int retryCount, Exception firstException)
    {
        try
        {
            await action(param1, param2).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            if (retryCount < noOfRetries)
            {
                await DelayNextAttempt(retryCount).ConfigureAwait(false);
                await RetryResilientlyAsync(action, param1, param2, ++retryCount, firstException ?? ex).ConfigureAwait(false);
            }
            else
            {
                throw new Exception($"Error Even after {retryCount} attempts", firstException ?? ex);
            }
        }
    }

    private async Task RetryResilientlyAsync<T1, T2, T3>(Func<T1, T2, T3, Task> action, T1 param1, T2 param2, T3 param3, int retryCount, Exception firstException)
    {
        try
        {
            await action(param1, param2, param3).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            if (retryCount < noOfRetries)
            {
                await DelayNextAttempt(retryCount).ConfigureAwait(false);
                await RetryResilientlyAsync(action, param1, param2, param3, ++retryCount, firstException ?? ex).ConfigureAwait(false);
            }
            else
            {
                throw new Exception($"Error Even after {retryCount} attempts", firstException ?? ex);
            }
        }
    }

    private async Task RetryResilientlyAsync(Func<Task> action, int retryCount, Exception firstException)
    {
        try
        {
            await action().ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            if (retryCount < noOfRetries)
            {
                await DelayNextAttempt(retryCount).ConfigureAwait(false);
                await RetryResilientlyAsync(action, ++retryCount, firstException ?? ex).ConfigureAwait(false);
            }
            else
            {
                throw new Exception($"Error Even after {retryCount} attempts", firstException ?? ex);
            }
        }
    }

    public T RetryResiliently<T>(Func<T> action)
    {
        return RetryResiliently<T>(action, 0, null);
    }

    public void RetryResiliently<T>(Action action)
    {
        RetryResiliently(action, 0, null);
    }

    public async Task RetryResilientlyAsync<T>(Func<T, Task> action, T param1)
    {
        await RetryResilientlyAsync(action, param1, 0, null).ConfigureAwait(false);
    }

    public async Task<TO> RetryResilientlyAsync<T1, T2, TO>(Func<T1, T2, Task<TO>> action, T1 param1, T2 param2)
    {
        return await RetryResilientlyAsync(action, param1, param2, 0, null).ConfigureAwait(false);
    }

    public async Task<TO> RetryResilientlyAsync<T1, TO>(Func<T1, Task<TO>> action, T1 param1)
    {
        return await RetryResilientlyAsync(action, param1, 0, null).ConfigureAwait(false);
    }

    public async Task RetryResilientlyAsync<T1, T2>(Func<T1, T2, Task> action, T1 param1, T2 param2)
    {
        await RetryResilientlyAsync(action, param1, param2, 0, null).ConfigureAwait(false);
    }

    public async Task RetryResilientlyAsync<T1, T2, T3>(Func<T1, T2, T3, Task> action, T1 param1, T2 param2, T3 param3)
    {
        await RetryResilientlyAsync(action, param1, param2, param3, 0, null).ConfigureAwait(false);
    }

    public async Task RetryResilientlyAsync(Func<Task> action)
    {
        await RetryResilientlyAsync(action, 0, null).ConfigureAwait(false);
    }
}
