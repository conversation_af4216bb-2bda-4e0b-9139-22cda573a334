# FA Engage

## Overview
FA Engage is a feature designed to enhance the efficiency and effectiveness of sales operations by providing timely and actionable notifications to sales representatives and managers. It helps streamline communication, track performance metrics, and drive improvements in sales activities through targeted notifications.

## Problem Statement
In traditional trade, a single user handles sales at 240 to 600 outlets monthly, and an area-level manager oversees 10-20 subordinates. Managing data and driving insights for performance improvement becomes challenging. Managers cannot micro-manage or always be present with users, making it difficult to provide timely guidance on targets and business KRAs.

## Solution
FA Engage addresses these issues by enabling timely user guidance through notifications. It connects app users with higher management efficiently, allowing for direct communication and action-driven insights.

## Feature Activation
To activate the FA Engage feature:
- **Company Setting:** Enable "Company Uses FA Engage" (ver: 3973).
- **App Version:** Ensure the Play Store build version is greater than 30 versions after 1.2.1.

## Key Components

### 1. Cohort Creation
A cohort is a group of app users with similar properties (e.g., same product category, app platform, employee hierarchy).

#### Steps:
1. **Cohort List Page:**
    - Check if the desired cohort exists.
    - If not, create a new cohort.

2. **Cohort Creation Page:**
    - Fill in the name and description.
    - Select Employee Hierarchy from dropdowns.
    - Choose User Role & Product division.
    - Save the cohort.

### 2. Notification Creation
Notifications include nudges, pop-ups, and push notifications sent to app users on their mobile devices. Notifications are customizable based on variables such as cohort, frequency, time & date, duration, KPI-based triggers, in-app placement, and custom content.

#### Steps:
1. **Notification List Page:**
    - Create a new notification.
    - Select existing cohorts or create a new cohort.
    - Set frequency, time & date, start & end date.
    - Add KRA-based trigger conditions if required.
    - Type in the custom message and optional alternate messages.
    - Save the notification.

2. **Notification Details:**
    - Name and describe the notification.
    - Select notification type (General or KRA-based).
    - Choose cohort from the dropdown.
    - Set notification frequency (Once, Daily, Weekly, Monthly).
    - Define trigger conditions and KPIs.
    - Insert notification title and message.
    - Set on-click action (Web link or App Screen).
    - Preview and save the notification.

## Accessing FA Engage
FA Engage can be accessed through both the New Dashboard and Old Dashboard. Users click on the Cohort option to start the cohort creation flow.

## Technical Notes
- The FA Engage processor runs every 30 minutes (e.g., 10:00 am, 10:30 am, 11:00 am, etc.).
- The product division filter in the cohort is not currently applied.

## Notification Types and Views
- **Push Notifications:** Visible in the mobile notification panel.
- **In-app Pop-ups:** Displayed on the app screen.
- **Notification Feed:** Accessible via the Bell icon on the Beat Dashboard Screen.

## Image Formats Supported
- JPEG
- Portable Network Graphic (PNG) with Truecolour and alpha, various resolutions and dimensions.

By following the setup and user flows provided, managers and users can effectively utilize the FA Engage feature to improve sales performance and streamline communication within their teams.