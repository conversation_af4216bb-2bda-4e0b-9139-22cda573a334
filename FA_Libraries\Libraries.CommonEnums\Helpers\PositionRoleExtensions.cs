﻿namespace Libraries.CommonEnums.Helpers;

public static class PositionRoleExtension
{
    public static string ToDBString(this PositionCodeLevel pcLevel)
    {
        switch (pcLevel)
        {
            case PositionCodeLevel.L8Position:
                return "Level8";

            case PositionCodeLevel.L7Position:
                return "Level7";

            case PositionCodeLevel.L6Position:
                return "Level6";

            case PositionCodeLevel.L5Position:
                return "Level5";

            case PositionCodeLevel.L4Position:
                return "Level4";

            case PositionCodeLevel.L3Position:
                return "Level3";

            case PositionCodeLevel.L2Position:
                return "Level2";

            case PositionCodeLevel.L1Position:
                return "Level1";

            default:
                return "";
        }
    }

    public static string PositionLevelFilter(this PositionCodeLevel pcLevel)
    {
        switch (pcLevel)
        {
            case PositionCodeLevel.L8Position:
                return " and PositionLevel8 in ";

            case PositionCodeLevel.L7Position:
                return " and PositionLevel7 in ";

            case PositionCodeLevel.L6Position:
                return " and PositionLevel6 in ";

            case PositionCodeLevel.L5Position:
                return " and PositionLevel5 in ";

            case PositionCodeLevel.L4Position:
                return " and PositionLevel4 in ";

            case PositionCodeLevel.L3Position:
                return " and PositionLevel3 in ";

            case PositionCodeLevel.L2Position:
                return " and PositionLevel2 in ";

            case PositionCodeLevel.L1Position:
                return " and PositionLevel1 in ";

            default:
                return "";
        }
    }

    public static string PositionCodeLevelFromPositionLevel(this string positionLevel)
    {
        switch (positionLevel)
        {
            case "Level8":
                return "L8Position";

            case "Level7":
                return "L7Position";

            case "Level6":
                return "L6Position";

            case "Level5":
                return "L5Position";

            case "Level4":
                return "L4Position";

            case "Level3":
                return "L3Position";

            case "Level2":
                return "L2Position";

            case "Level1":
                return "L1Position";

            default:
                return "";
        }
    }
}
