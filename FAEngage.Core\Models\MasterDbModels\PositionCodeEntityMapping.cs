﻿namespace FAEngage.Core.Models.MasterDbModels;

public class PositionCodeEntityMapping
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public long PositionCodeId { get; set; }
    public long EntityId { get; set; }
    public bool IsDetached { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public PositionCode PositionCode { get; set; }
    public ClientEmployee Employee { get; set; }
}