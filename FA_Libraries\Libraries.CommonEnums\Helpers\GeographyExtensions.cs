﻿namespace Libraries.CommonEnums.Helpers;

public static class GeographyExtensions
{
    public static string ToDBString(this GeographyHierarchy geoLevel)
    {
        switch (geoLevel)
        {
            case GeographyHierarchy.Beat:
                return "Beat";

            case GeographyHierarchy.Territory:
                return "Territory";

            case GeographyHierarchy.Region:
                return "Region";

            case GeographyHierarchy.Zone:
                return "Zone";

            case GeographyHierarchy.Level5:
                return "Level5";

            case GeographyHierarchy.Level6:
                return "Level6";

            case GeographyHierarchy.Level7:
                return "Level7";

            default:
                return "";
        }
    }
}
