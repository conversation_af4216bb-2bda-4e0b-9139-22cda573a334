﻿using System.Text;
using Microsoft.Extensions.Caching.Distributed;

namespace FAEngage.Core.Interfaces
{
    public interface ICacheProvider
    {
        void Insert<T>(string cacheKey, T result, TimeSpan expiresIn);
        bool TryGet<T>(string cacheKey, out T result);
        Task<T> GetAsJsonAsync<T>(string key, Encoding encoding = null);

        Task SetAsJsonAsync<T>(
            string key,
            T value,
            Encoding encoding = null,
            DistributedCacheEntryOptions options = null);
    }
}