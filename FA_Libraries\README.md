# F2k.Libraries

Official FA Centralized Library

## Background

Centralized repository depicting a library containing all the common resources required to be used in the product's
projects.

- This library is intended to be used as is.
- This library is not intended to run independently. But it can be tested via automated/manual tests.

## Getting Started

### To use this Library

Go to you repository and run the commands:

```shell
# Add this as a submodule
git submodule add https://<EMAIL>/flick2know/F2k.Libraries/_git/F2k.Libraries
```

### Submodules Update

```shell
git submodule update --init --recursive --remote -f
```

### Book keeping

To view changelog

```shell
git --no-pager log --format="- %s (%an)" -15
```

## Master Data Reference Integration

To integrate master data reference into your project, follow the steps outlined below. This integration relies on two
essential projects: `FA.masterDB.Core` and `FA.MasterDB.SQLDatabase`.

### Step 1: Import Projects

In your solution, import the following projects:

- `FA.masterDB.Core`
- `FA.MasterDB.SQLDatabase`

### Step 2: Repository and DTO Organization

#### Core Project (`FA.masterDB.Core`)

All the repositories can be found in the `Interface` folder, while the Data Transfer Objects (DTOs) are located in the
`DTO` folder.

#### SQLDatabase Project (`FA.MasterDB.SQLDatabase`)

In the `SQLDatabase` project, you'll find a `dependency` folder. This folder is designed to facilitate the injection of
necessary dependencies into your project.

## Extras

### Husky pre commit hook

```shell
dotnet new tool-manifest

dotnet tool install Husky

dotnet husky install
```

### Generate Changelog

```shell
git --no-pager log --format="- %s (%an)" -50
```

### Formatting

To reformat code manually:

```shell
dotnet format --exclude ./fa_dotnet_clickhouse_connector ./fa_dotnet_core ./fa_dotnet_logger --verbosity d
```

To only check

```shell
dotnet format --verify-no-changes
```

## Roslynator Fix

```shell
roslynator fix ./AppApis/AppApis.csproj
```

```shell
roslynator fix ./AppApis.Core/*.csproj
```

## Contribution

1. Fork it
2. Create your feature branch (git checkout -b my-new-feature)
3. Commit your changes (git commit -m 'Add some feature')
4. Push to the branch (git push origin my-new-feature)
5. Create new Pull Request