﻿using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums;

public enum OutletChannel
{
    Others = 0,
    GT = 1,
    MT = 2,
    HORECA = 3,

    [Display(Name = "Semi MT")]
    MMS = 4,
    Institutional = 5,
    CSD = 6,

    [Display(Name = "Key Accounts")]
    KeyAccounts = 7,

    [Display(Name = "Direct Dealers")]
    DirectDealers = 8,

    [Display(Name = "Extra Channel")]
    ExtraChannel = 9,

    [Display(Name = "Work Place")]
    WorkPlace = 10,

    [Display(Name = "Cinema Halls")]
    CinemaHalls = 11,
    QSR = 12,
    Pharmacy = 13,

    [Display(Name = "Extra Channel 1")]
    ExtraChannel1 = 14,

    [Display(Name = "Extra Channel 2")]
    ExtraChannel2 = 15,

    [Display(Name = "Extra Channel 3")]
    ExtraChannel3 = 16,

    [Display(Name = "Extra Channel 4")]
    ExtraChannel4 = 17,

    [Display(Name = "Extra Channel 5")]
    ExtraChannel5 = 18,

    [Display(Name = "Extra Channel 6")]
    ExtraChannel6 = 19,

    [Display(Name = "Extra Channel 7")]
    ExtraChannel7 = 20,

    [Display(Name = "Extra Channel 8")]
    ExtraChannel8 = 21,

    [Display(Name = "Extra Channel 9")]
    ExtraChannel9 = 22,

    [Display(Name = "Extra Channel 10")]
    ExtraChannel10 = 23,

    [Display(Name = "Extra Channel 11")]
    ExtraChannel11 = 24,

    [Display(Name = "Extra Channel 12")]
    ExtraChannel12 = 25,

    [Display(Name = "Extra Channel 13")]
    ExtraChannel13 = 26,

    [Display(Name = "Extra Channel 14")]
    ExtraChannel14 = 27,

    [Display(Name = "Extra Channel 15")]
    ExtraChannel15 = 28,

    [Display(Name = "Extra Channel 16")]
    ExtraChannel16 = 29,

    [Display(Name = "Extra Channel 17")]
    ExtraChannel17 = 30,

    [Display(Name = "Extra Channel 18")]
    ExtraChannel18 = 31,

    [Display(Name = "Extra Channel 19")]
    ExtraChannel19 = 32,

    [Display(Name = "Extra Channel 20")]
    ExtraChannel20 = 33,

    [Display(Name = "Extra Channel 21")]
    ExtraChannel21 = 34,

    [Display(Name = "Extra Channel 22")]
    ExtraChannel22 = 35,

    [Display(Name = "Extra Channel 23")]
    ExtraChannel23 = 36,

    [Display(Name = "Extra Channel 24")]
    ExtraChannel24 = 37,

    [Display(Name = "Extra Channel 25")]
    ExtraChannel25 = 38,

    [Display(Name = "Extra Channel 26")]
    ExtraChannel26 = 39,

    [Display(Name = "Extra Channel 27")]
    ExtraChannel27 = 40,

    [Display(Name = "Extra Channel 28")]
    ExtraChannel28 = 41,

    [Display(Name = "Extra Channel 29")]
    ExtraChannel29 = 42,

    [Display(Name = "Extra Channel 30")]
    ExtraChannel30 = 43,

    [Display(Name = "Extra Channel 31")]
    ExtraChannel31 = 44,

    [Display(Name = "Extra Channel 32")]
    ExtraChannel32 = 45,

    [Display(Name = "Extra Channel 33")]
    ExtraChannel33 = 46,

    [Display(Name = "Extra Channel 34")]
    ExtraChannel34 = 47,

    [Display(Name = "Extra Channel 35")]
    ExtraChannel35 = 48,

    [Display(Name = "Extra Channel 36")]
    ExtraChannel36 = 49,

    [Display(Name = "Extra Channel 37")]
    ExtraChannel37 = 50
}
