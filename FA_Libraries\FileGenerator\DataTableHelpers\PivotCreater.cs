﻿using System.Collections.Generic;
using System.Data;
using System.Linq;
using FileGenerator.HelperModels;

namespace FileGenerator.DataTableHelpers;

public class PivotCreater
{
    public static DataTable Pivot(DataTable dt, DataColumn pivotColumn, DataColumn pivotValue)
    {
        // find primary key columns
        //(i.e. everything but pivot column and pivot value)
        var temp = dt.Copy();
        temp.Columns.Remove(pivotColumn.ColumnName);
        temp.Columns.Remove(pivotValue.ColumnName);
        var pkColumnNames = temp.Columns.Cast<DataColumn>()
            .Select(c => c.ColumnName)
            .ToArray();

        // prep results table
        var result = temp.DefaultView.ToTable(true, pkColumnNames).Copy();
        result.PrimaryKey = result.Columns.Cast<DataColumn>().ToArray();
        dt.Rows.Cast<DataRow>()
            .Select(r => r[pivotColumn.ColumnName].ToString())
            .Distinct().ToList()
            .ForEach(c => result.Columns.Add(c, pivotValue.DataType));

        // load it
        foreach (DataRow row in dt.Rows)
        {
            // find row to update
            var aggRow = result.Rows.Find(
                pkColumnNames
                    .Select(c => row[c])
                    .ToArray());
            // the aggregate used here is LATEST
            // adjust the next line if you want (SUM, MAX, etc...)
            aggRow[row[pivotColumn.ColumnName].ToString()] = row[pivotValue.ColumnName];
        }

        result.PrimaryKey = null;
        result.TableName = "Pivot" + pivotColumn;
        return result;
    }

    public static DataTable Pivot(DataTable dt, string primaryKey, PivotColumn pivotColumnSet, string tableName,
        string defaultGroupColumnName = null, List<Dictionary<string, int>> sortingDictionaryList = null,
        string delimiter = "_")
    {
        // find primary key columns
        //(i.e. everything but pivot column and pivot value)
        var temp = dt.Copy();

        var pivotColumn = dt.Columns[pivotColumnSet.ParentColumn];

        foreach (var item in temp.Columns.Cast<DataColumn>()
                     .Select(c => c.ColumnName)
                     .ToArray())
        {
            if (item != primaryKey)
            {
                temp.Columns.Remove(item);
            }
        }

        string[] pkColumnNames = { primaryKey };

        // prep results table
        var result = temp.AsEnumerable().Distinct(DataRowComparer.Default)
            .CopyToDataTable(); //temp.DefaultView.ToTable(true, pkColumnNames).Copy();
        result.PrimaryKey = result.Columns.Cast<DataColumn>().ToArray();
        foreach (var pivotMember in pivotColumnSet.ValueColumns)
        {
            var pivotValue = dt.Columns[pivotMember];
            dt.Rows.Cast<DataRow>()
                .Select(r => r[pivotColumn.ColumnName] + delimiter + pivotValue)
                .Distinct().ToList()
                .ForEach(c => result.Columns.Add(c, pivotValue.DataType));

            // load it
            foreach (DataRow row in dt.Rows)
            {
                // find row to update
                var aggRow = result.Rows.Find(
                    pkColumnNames
                        .Select(c => row[c])
                        .ToArray());
                // the aggregate used here is LATEST
                // adjust the next line if you want (SUM, MAX, etc...)
                aggRow[row[pivotColumn.ColumnName] + delimiter + pivotValue] = row[pivotValue.ColumnName];
            }
        }

        result.PrimaryKey = null;
        result.TableName = tableName;
        var sortPivotColumns = new SortPivotColumns();
        //Date: 16th Aug 2021;
        //Link: https://app.asana.com/0/305436650865282/1200721183030850/f;
        //Reason: Change the concat variable to '+' from '_'.
        foreach (var c in pivotColumnSet.ValueColumns)
        {
            var colmnName = $"{defaultGroupColumnName}{delimiter}{c}";
            if (result.Columns.Contains(colmnName))
            {
                result.Columns.Remove(colmnName);
            }
        }

        if (sortingDictionaryList != null)
        {
            result = SortPivotColumns.GetSortedPivotTable(result, sortingDictionaryList);
        }

        if (pivotColumnSet.addLexicoSorting)
        {
            result = SortPivotColumns.GetLexicographicallySortedPivotTable(result);
        }

        //var pivot = CheckDataTableColumn(result);
        return result;
    }

    public static DataTable DoublePivot(DataTable dt, string primaryKey, PivotColumn pivotColumnSet, string tableName,
        string defaultGroupColumnName = null, List<Dictionary<string, int>> sortingDictionaryList = null,
        string pivotSeparater = "+")
    {
        // find primary key columns
        //(i.e. everything but pivot column and pivot value)
        var temp = dt.Copy();

        var pivotColumn = dt.Columns[pivotColumnSet.ParentColumn];
        var SecondaryPivotColumn = dt.Columns[pivotColumnSet.SecondaryPivotColumn];

        foreach (var item in temp.Columns.Cast<DataColumn>()
                     .Select(c => c.ColumnName)
                     .ToArray())
        {
            if (item != primaryKey)
            {
                temp.Columns.Remove(item);
            }
        }

        string[] pkColumnNames = { primaryKey };

        // prep results table
        var result = temp.AsEnumerable().Distinct(DataRowComparer.Default)
            .CopyToDataTable(); //temp.DefaultView.ToTable(true, pkColumnNames).Copy();
        result.PrimaryKey = result.Columns.Cast<DataColumn>().ToArray();
        foreach (var pivotMember in pivotColumnSet.ValueColumns)
        {
            var pivotValue = dt.Columns[pivotMember];
            dt.Rows.Cast<DataRow>()
                .Select(r =>
                    r[SecondaryPivotColumn.ColumnName] + pivotSeparater + r[pivotColumn.ColumnName] +
                    pivotSeparater + pivotValue)
                .Distinct().ToList()
                .ForEach(c => result.Columns.Add(c, pivotValue.DataType));

            // load it
            foreach (DataRow row in dt.Rows)
            {
                // find row to update
                var aggRow = result.Rows.Find(
                    pkColumnNames
                        .Select(c => row[c])
                        .ToArray());
                // the aggregate used here is LATEST
                // adjust the next line if you want (SUM, MAX, etc...)
                aggRow[
                    row[SecondaryPivotColumn.ColumnName] + pivotSeparater + row[pivotColumn.ColumnName] +
                    pivotSeparater + pivotValue] = row[pivotValue.ColumnName];
            }
        }

        result.PrimaryKey = null;
        result.TableName = tableName;
        var sortPivotColumns = new SortPivotColumns();
        foreach (var c in pivotColumnSet.ValueColumns)
        {
            var colmnName = $"{defaultGroupColumnName}_{c}";
            if (result.Columns.Contains($"{defaultGroupColumnName}_{c}"))
            {
                result.Columns.Remove(colmnName);
            }
        }

        if (sortingDictionaryList != null)
        {
            result = SortPivotColumns.GetSortedPivotTable(result, sortingDictionaryList);
        }

        //var pivot = CheckDataTableColumn(result);
        return result;
    }
}
