﻿using System.IO;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Library.CommonHelpers.HttpClients;
using Newtonsoft.Json;
using Opw.HttpExceptions;

namespace Library.CommonHelpers;

public static class HttpResponseMessageExtensions
{
    public static async Task EnsureSuccessStatusCodeAsync(this HttpResponseMessage response)
    {
        if (response.IsSuccessStatusCode)
        {
            return;
        }

        var content = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

        response.Content?.Dispose();

        throw new HttpException(response.StatusCode, content);
    }

    public static async Task<T> GetParsedJsonAsync<T>(this HttpResponseMessage response)
    {
        await response.EnsureSuccessStatusCodeAsync();
        using var reader = new StreamReader(await response.Content.ReadAsStreamAsync());
        await using var jsonReader = new JsonTextReader(reader);
        var ser = new JsonSerializer();
        return ser.Deserialize<T>(jsonReader);
    }

    public static async Task<T> GetJsonAsync<T>(this FaReportingHttpClient client, string url)
    {
        var response = await client.GetAsync(url);
        await response.EnsureSuccessStatusCodeAsync();
        using var reader = new StreamReader(await response.Content.ReadAsStreamAsync());
        await using var jsonReader = new JsonTextReader(reader);
        var ser = new JsonSerializer();
        return ser.Deserialize<T>(jsonReader);
    }

    public static async Task<T> PostJsonAsync<T>(this FaReportingHttpClient client, string url, string json)
    {
        var buffer = Encoding.UTF8.GetBytes(json);
        var byteContent = new ByteArrayContent(buffer);
        byteContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");
        var response = await client.PostAsync(url, byteContent);
        await response.EnsureSuccessStatusCodeAsync();
        using var reader = new StreamReader(await response.Content.ReadAsStreamAsync());
        await using var jsonReader = new JsonTextReader(reader);
        var ser = new JsonSerializer();
        return ser.Deserialize<T>(jsonReader);
    }
}
