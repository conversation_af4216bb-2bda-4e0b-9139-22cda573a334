﻿using FAEngage.Core.MasterRepositories;
using FAEngage.DbStorage.DbContexts;
using Microsoft.EntityFrameworkCore;

namespace FAEngage.DbStorage.MasterRepositories;

public class ProductDivRepository(MasterDbContext db) : IProductDivRepository
{
    public async Task<long> GetProductDivisionForEmployee(long companyId, long empId)
    {
        return await db.EmployeeProductDivisionMappings
            .Where(e => e.CompanyId == companyId 
                        && e.EmployeeId == empId && !e.Deleted)
            .Select(e => e.ProductDivisionId)
            .FirstOrDefaultAsync();
    }
}