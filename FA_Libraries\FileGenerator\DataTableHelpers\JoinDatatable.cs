﻿using System.Data;
using System.Linq;
using FileGenerator.HelperModels;

namespace FileGenerator.DataTableHelpers;

public class JoinDatatable
{
    public static DataTable JoinDataTables(DataSet ds, PivotColumn pivotColumns, string baseTableName,
        bool forceConvertToNumbers = false)
    {
        DataColumn obj_ParentDepartmentID, obj_ChildDepartmentID;
        obj_ChildDepartmentID = ds.Tables["FlatTable"].Columns["Id"];
        obj_ParentDepartmentID = ds.Tables[pivotColumns.ParentColumn].Columns["Id"];

        var obj_DataRelation = new DataRelation("relation" + pivotColumns.ParentColumn, obj_ParentDepartmentID,
            obj_ChildDepartmentID);
        ds.Relations.Add(obj_DataRelation);

        foreach (DataColumn col1 in ds.Tables[pivotColumns.ParentColumn].Columns)
        {
            if (col1.ColumnName != "Id")
            {
                var dataType = col1.DataType;
                double _ = 0;
                if (forceConvertToNumbers && ds.Tables[pivotColumns.ParentColumn].Rows.Cast<DataRow>()
                        .Any(r => double.TryParse(r[col1.ColumnName].ToString(), out _)))
                {
                    dataType = typeof(double);
                }

                ds.Tables[baseTableName].Columns.Add(col1.ColumnName, dataType);
            }
        }

        foreach (DataRow Parent in ds.Tables[baseTableName].Rows)
        {
            var Child = Parent.GetParentRow(obj_DataRelation);

            foreach (DataColumn col1 in ds.Tables[pivotColumns.ParentColumn].Columns)
            {
                if (col1.ColumnName != "Id")
                {
                    Parent[col1.ColumnName] = Child[col1.ColumnName];
                }
            }
        }

        var dtResult = ds.Tables[baseTableName].AsEnumerable().CopyToDataTable();
        ds.Tables[baseTableName].DefaultView.ToTable(false);

        return dtResult;
    }

    public static DataTable JoinTwoDataTable(DataSet ds, string pivotColumn)
    {
        var pivotTableName = "Pivot" + pivotColumn;
        DataColumn obj_ParentDepartmentID, obj_ChildDepartmentID;
        obj_ParentDepartmentID = ds.Tables[pivotTableName].Columns["Id"];
        obj_ChildDepartmentID = ds.Tables["FlatTable"].Columns["Id"];

        var obj_DataRelation = new DataRelation("relation", obj_ParentDepartmentID, obj_ChildDepartmentID);
        ds.Relations.Add(obj_DataRelation);

        foreach (DataColumn col1 in ds.Tables[pivotTableName].Columns)
        {
            if (col1.ColumnName != "Id")
            {
                ds.Tables["FlatTable"].Columns.Add(col1.ColumnName);
            }
        }

        foreach (DataRow Parent in ds.Tables["FlatTable"].Rows)
        {
            var Child = Parent.GetParentRow(obj_DataRelation);

            foreach (DataColumn col1 in ds.Tables[pivotTableName].Columns)
            {
                if (col1.ColumnName != "Id")
                {
                    Parent[col1.ColumnName] = Child[col1.ColumnName];
                }
            }
        }

        var dtResult =
            ds.Tables["FlatTable"].AsEnumerable()
                .CopyToDataTable(); // ds.Tables["FlatTable"].DefaultView.ToTable(false);

        return dtResult;
    }
}
