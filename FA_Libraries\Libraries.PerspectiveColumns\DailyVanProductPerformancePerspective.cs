﻿using System;
using System.Collections.Generic;
using Libraries.CommonEnums;
using Libraries.PerspectiveColumns.Interface;

namespace Libraries.PerspectiveColumns;

public class DailyVanProductPerformancePerspective : IPerspective
{
    //private linkNames linkNames;

    public DailyVanProductPerformancePerspective(long companyId)
    {
        //linkNames = InMemorySettings.GetLinkNames(companyId);
    }

    public static List<PerspectiveColumnModel> Columns => new()
    {
        new PerspectiveColumnModel { Attribute = "Field User", DisplayName = "GlobalSalesManager  ", Name = "GSM", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Field User", DisplayName = "NationalSalesManager", Name = "NSM", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Field User", DisplayName = "ZonalSalesManager   ", Name = "ZSM", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Field User", DisplayName = "RegionalSalesManager", Name = "RSM", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Field User", DisplayName = "AreaSalesManager    ", Name = "ASM", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Field User", DisplayName = "Reporting Manager", Name = "ReportingManager", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Field User", DisplayName = "FieldUser Rank", Name = "FieldUserRank", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Field User", DisplayName = "FieldUser Name", Name = "FieldUserName", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Field User", DisplayName = "FieldUser HQ", Name = "FieldUserHQ", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Field User", DisplayName = "Field User ERP ID", Name = "FieldUserERPID", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Field User", DisplayName = "User Designation", Name = "UserDesignation", IsDimension = true },

        // Name Property Of New Positions need to be in this Pattern (LXPosition) only or Filtering based on setting won't work
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L8Position", Name = "L8Position", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L8Position" + " Code", Name = "L8Position_Code", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L8Position" + " User", Name = "L8Position_User", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L8Position" + " User ErpId", Name = "L8Position_UserErpId", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L7Position", Name = "L7Position", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L7Position" + " Code", Name = "L7Position_Code", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L7Position" + " User", Name = "L7Position_User", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L7Position" + " User ErpId", Name = "L7Position_UserErpId", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L6Position", Name = "L6Position", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L6Position" + " Code", Name = "L6Position_Code", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L6Position" + " User", Name = "L6Position_User", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L6Position" + " User ErpId", Name = "L6Position_UserErpId", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L5Position", Name = "L5Position", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L5Position" + " Code", Name = "L5Position_Code", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L5Position" + " User", Name = "L5Position_User", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L5Position" + " User ErpId", Name = "L5Position_UserErpId", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L4Position", Name = "L4Position", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L4Position" + " Code", Name = "L4Position_Code", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L4Position" + " User", Name = "L4Position_User", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L4Position" + " User ErpId", Name = "L4Position_UserErpId", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L3Position", Name = "L3Position", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L3Position" + " Code", Name = "L3Position_Code", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L3Position" + " User", Name = "L3Position_User", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L3Position" + " User ErpId", Name = "L3Position_UserErpId", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L2Position", Name = "L2Position", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L2Position" + " Code", Name = "L2Position_Code", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L2Position" + " User", Name = "L2Position_User", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L2Position" + " User ErpId", Name = "L2Position_UserErpId", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L1Position", Name = "L1Position", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L1Position" + " Code", Name = "L1Position_Code", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L1Position" + " User", Name = "L1Position_User", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "L1Position" + " User ErpId", Name = "L1Position_UserErpId", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "Reporting Manager", Name = "L2Position_ReportingUser", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "FieldUser Name", Name = "L1Position_FieldUser", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "Rank", Name = "L1Position_UserRank", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "FieldUser HQ", Name = "L1Position_UserHQ", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "Field User ERP ID", Name = "L1Position_UserERP", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Position", DisplayName = "User Designation", Name = "L1Position_UserDesignation", IsDimension = true },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Employee AttributeText1",
            Name = "EmployeeAttributeText1",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Employee AttributeText2",
            Name = "EmployeeAttributeText2",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Employee AttributeNumber1",
            Name = "EmployeeAttributeNumber1",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Employee AttributeNumber2",
            Name = "EmployeeAttributeNumber2",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Date Of Joining",
            Name = "DateOfJoining",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Email Id",
            Name = "EmailId",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "User Type",
            Name = "UserType",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Contact Number",
            Name = "ContactNumber",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel { Attribute = "Channel Partner", DisplayName = "Distributor" + " Erp Id", Name = "DistributorErpId", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Channel Partner", DisplayName = "Distributor", Name = "Distributor", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Channel Partner", DisplayName = "Distributor" + " Address", Name = "DistributorAddress", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Channel Partner", DisplayName = "Distributor" + " Phone", Name = "DistributorPhone", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Channel Partner", DisplayName = "Stockist Type", Name = "StockistType", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Channel Partner", DisplayName = "SuperStockist", Name = "SuperStockist", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Channel Partner", DisplayName = "SuperStockist" + " Erp Id", Name = "SuperStockistErpId", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Van", DisplayName = "Van ErpId", Name = "VanErpId", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Van", DisplayName = "Van Name", Name = "VanName", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Van", DisplayName = "Van RegistrationNo", Name = "VanRegistrationNo", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Van", DisplayName = "Van Capacity", Name = "VanCapacity", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Van", DisplayName = "Load-in Status", Name = "LoadInStatus", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Sales Territory", DisplayName = "Beat     ", Name = "Beat", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Sales Territory", DisplayName = "Territory", Name = "Territory", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Sales Territory", DisplayName = "Region   ", Name = "Region", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Sales Territory", DisplayName = "Zone     ", Name = "Zone", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Sales Territory", DisplayName = "Level5   ", Name = "Level5", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Sales Territory", DisplayName = "Level6   ", Name = "Level6", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Sales Territory", DisplayName = "Level7   ", Name = "Level7", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Product", DisplayName = "ProductERPID", Name = "ProductERPID", IsDimension = true },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = "Product",
            Name = "ProductName",
            IsDimension = true,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = "SecondaryCategory",
            Name = "SecondaryCategory",
            IsDimension = true,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = "PrimaryCategory  ",
            Name = "PrimaryCategory",
            IsDimension = true,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = "Style",
            Name = "DisplayCategory",
            IsDimension = true,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = "Product Divison",
            Name = "ProductDivision",
            IsDimension = true,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = "Alternate Category",
            Name = "AlternateCategory",
            IsDimension = true,
            IsPivot = true
        },
        new PerspectiveColumnModel { Attribute = "Product", DisplayName = "Unit", Name = "ProductUnit", IsDimension = false },
        new PerspectiveColumnModel { Attribute = "Product", DisplayName = "Conversion Factor", Name = "ProductStandardUnitConversionFactor", IsDimension = false },
        new PerspectiveColumnModel { Attribute = "Product", DisplayName = "Std. Unit", Name = "ProductStandardUnit", IsDimension = false },
        new PerspectiveColumnModel { Attribute = "Product", DisplayName = "PTR", Name = "PTR", IsDimension = false },
        new PerspectiveColumnModel { Attribute = "Product", DisplayName = "Original PTR", Name = "OriginalPTR", IsDimension = false },
        new PerspectiveColumnModel { Attribute = "Product", DisplayName = "Is Focus", Name = "IsFocused", IsDimension = false },
        new PerspectiveColumnModel { Attribute = "Product", DisplayName = "Is Assorted", Name = "IsAssorted", IsDimension = false },
        new PerspectiveColumnModel { Attribute = "Product", DisplayName = "Is Promoted", Name = "IsPromoted", IsDimension = false },
        new PerspectiveColumnModel { Attribute = "Product", DisplayName = "Is Recommended", Name = "IsRecommended", IsDimension = false },
        new PerspectiveColumnModel { Attribute = "Product", DisplayName = "Is Fast Moving", Name = "IsFastMoving", IsDimension = false },
        new PerspectiveColumnModel { Attribute = "Product", DisplayName = "Min Stock Norm", Name = "MinStockNorm", IsDimension = true },
        new PerspectiveColumnModel { Attribute = "Product", DisplayName = "Max Stock Norm", Name = "MaxStockNorm", IsDimension = true },
        new PerspectiveColumnModel
        {
            Attribute = "Time",
            DisplayName = "Order Date",
            Name = "Date",
            IsDimension = true,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Time",
            DisplayName = "Order Week",
            Name = "Week",
            IsDimension = true,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Time",
            DisplayName = "Order Month",
            Name = "Month",
            IsDimension = true,
            IsPivot = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "Day Start Time",
            Name = "DayStartTime",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Min
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "Day End Date",
            Name = "DayEndDate",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Max
        },
        new PerspectiveColumnModel
        {
            Attribute = "Attendance",
            DisplayName = "Day End Time",
            Name = "DayEndTime",
            IsMeasure = true,
            PerspectiveMeasure = PerspectiveMeasure.Max
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Total Load-out Qty ( Unit )",
            Name = "LoadOutStockUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Total Load-out Qty ( StdUnit )",
            Name = "LoadOutStockStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Total Load-out Qty ( SuperUnit )",
            Name = "LoadOutStockSuperUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Total Load-out Value",
            Name = "LoadOutStockValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Fresh Load Qty ( Unit )",
            Name = "FreshLoadOutStockUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Fresh Load Qty ( StdUnit )",
            Name = "FreshLoadOutStockStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Fresh Load Qty ( SuperUnit )",
            Name = "FreshLoadOutStockSuperUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Fresh Load Value",
            Name = "FreshLoadOutStockValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Carry Forward Load Qty ( Unit )",
            Name = "CarryForwardLoadOutStockUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Carry Forward Load Qty ( StdUnit )",
            Name = "CarryForwardLoadOutStockStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Carry Forward Load Qty ( SuperUnit )",
            Name = "CarryForwardLoadOutStockSuperUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Carry Forward Load Value",
            Name = "CarryForwardLoadOutStockValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Calculated Load-In Qty ( Unit )",
            Name = "EstimatedLoadInQtyUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Calculated Load-In Qty ( StdUnit )",
            Name = "EstimatedLoadInQtyStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Calculated Load-In Qty ( SuperUnit )",
            Name = "EstimatedLoadInQtySuperUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Calculated Load-In Value",
            Name = "EstimatedLoadInQtyValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Load-In Qty ( Unit )",
            Name = "LoadInQtyUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Load-In Qty ( StdUnit )",
            Name = "LoadInQtyStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Load-In Qty ( SuperUnit )",
            Name = "LoadInQtySuperUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Load-In Value",
            Name = "LoadInQtyValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Order Qty ( Unit )",
            Name = "OrderInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Order Qty ( StdUnit )",
            Name = "OrderQtyInStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Order Qty ( SuperUnit )",
            Name = "OrderQtyInSupUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Order Value",
            Name = "Value",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PreSales Qty ( Unit )",
            Name = "PreSalesInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PreSales Qty ( StdUnit )",
            Name = "PreSalesQtyInStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PreSales Qty ( SuperUnit )",
            Name = "PreSalesQtyInSupUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PreSales Value",
            Name = "PreSalesValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Total Return Qty ( Unit )",
            Name = "TotalReturnInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Total Return Qty ( StdUnit )",
            Name = "TotalReturnQtyInStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Total Return Qty ( SuperUnit )",
            Name = "TotalReturnQtyInSupUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Total Return Value",
            Name = "TotalReturnValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Damaged Return Qty ( Unit )",
            Name = "DamagedReturnInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Damaged Return Qty ( StdUnit )",
            Name = "DamagedReturnQtyInStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Damaged Return Qty ( SuperUnit )",
            Name = "DamagedReturnQtyInSupUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Damaged Return Value",
            Name = "DamagedReturnValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Expired Return Qty ( Unit )",
            Name = "ExpiredReturnInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Expired Return Qty ( StdUnit )",
            Name = "ExpiredReturnQtyInStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Expired Return Qty ( SuperUnit )",
            Name = "ExpiredReturnQtyInSupUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Expired Return Value",
            Name = "ExpiredReturnValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Saleable Return Qty ( Unit )",
            Name = "SaleableReturnInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Saleable Return Qty ( StdUnit )",
            Name = "SaleableReturnQtyInStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Saleable Return Qty ( SuperUnit )",
            Name = "SaleableReturnQtyInSupUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Saleable Return Value",
            Name = "SaleableReturnValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        }
    };

    List<PerspectiveColumnModel> IPerspective.Columns
    {
        get => Columns;
        set => throw new NotImplementedException();
    }
}
