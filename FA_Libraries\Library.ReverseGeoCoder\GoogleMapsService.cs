using System.Linq;
using System.Threading.Tasks;
using Flurl.Http;
using Library.FaExceptions;
using Newtonsoft.Json;

namespace Library.ReverseGeoCoder;

public class GoogleMapsService
{
    private readonly string baseUrl;

    public GoogleMapsService(string baseUrl)
    {
        this.baseUrl = baseUrl;
    }

    //TODO: Get Distance of Multiple Points Simultaneously https://developers.google.com/maps/documentation/distance-matrix/distance-matrix#DistanceMatrixStatus
    public async Task<double> GetDistanceBetweenTwoLocations(decimal originLatitude = 0, decimal originLongitude = 0, decimal destinationLatitude = 0, decimal destinationLongitude = 0, string apiKey = "")
    {
        var api = $"maps/api/distancematrix/json?destinations={destinationLatitude},{destinationLongitude}&origins={originLatitude},{originLongitude}&key={apiKey}";

        var response = await (baseUrl + api).GetJsonAsync<DistanceMatrixData>().ConfigureAwait(false);

        if (response.Status == "OK")
        {
            return response.Rows.First().Elements.First().Distance?.Value ?? 0;
        }

        throw new HttpFailure(baseUrl, api, response.Status, JsonConvert.SerializeObject(response));
    }
}
