﻿using Libraries.CommonEnums;

namespace FAEngage.Core.Models.MasterDbModels;

public class PositionCode
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public string CodeId { get; set; }
    public string Name { get; set; }
    public PositionCodeLevel Level { get; set; }
    public long? ParentId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public PositionCode Parent { get; set; }
    public bool Deleted { get; set; }
    public virtual List<PositionCodeEntityMapping> PositionCodeEntityMappings { get; set; }
}