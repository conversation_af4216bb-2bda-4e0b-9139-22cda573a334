﻿using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Flurl.Http;
using Library.EmailService.Interface;

namespace Library.EmailService;

public class EmailSparkAPIHandler : IEmailHandler
{
    private const string EmailUsername = "SMTP_Injection";
    private const string EmailPassword = "9a7df52c464d7bd804633d89574ffceed36f50f7";

    public async Task<string> SendEmailAsync(EmailMessage emailMessage, CancellationToken ct = default)
    {
        var request = GetRequestForEmailMessage(emailMessage);
        await "https://api.sparkpost.com/api/v1/".WithBasicAuth(EmailUsername, EmailPassword).PostJsonAsync(request, cancellationToken: ct).ConfigureAwait(false);
        return "200"; // Because above call will throw error for non success status codes
    }

    #region private

    private static object GetRequestForEmailMessage(EmailMessage emailMessage)
    {
        var allTo = emailMessage.To.Split(",;".ToCharArray()).ToList();
        var allBcc = new List<string>();
        var allCc = new List<string>();
        if (!string.IsNullOrWhiteSpace(emailMessage.Bcc))
        {
            allBcc = emailMessage.Bcc.Split(",;".ToCharArray()).ToList();
        }

        if (!string.IsNullOrWhiteSpace(emailMessage.Cc))
        {
            allCc = emailMessage.Cc.Split(",;".ToCharArray()).ToList();
        }

        var requestBody = new
        {
            options = new { sandbox = false, transactional = true },
            content = new { from = new { email = emailMessage.FromEmail, name = emailMessage.FromName }, subject = emailMessage.Subject, html = emailMessage.Message },
            recipients = new List<dynamic>()
        };

        requestBody.recipients.AddRange(allTo.Select(e => new { address = new { email = e } }));

        requestBody.recipients.AddRange(allBcc.Select(e => new { address = new { email = e }, bcc = true }));

        requestBody.recipients.AddRange(allCc.Select(e => new { address = new { email = e }, cc = true }));
        return requestBody;
    }

    #endregion private
}
