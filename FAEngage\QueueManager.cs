﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FAEngage.Configurations;
using FAEngage.Core.Models;
using Libraries.CommonEnums;
using Library.Infrastructure.Interface;
using Library.Infrastructure.Models;
using Library.Infrastructure.QueueService;

namespace FAEngage
{
    public class EnagageNotificationMain
    {
        public long NotificationId { get; set; }
        public long CompanyId { get; set; }
    }

    public class EnagageNotificationEmployee
    {
        public long NotificationId { get; set; }
        public long CompanyId { get; set; }
        public long UserId { get; set; }
        public Guid TransactionId { get; set; }
        public string FATradeNotificationToken { get; set; }
        public string OwnersName { get; set; }
        public Guid? ImageId { get; set; }
        public List<long> PositionCodeIds { get; set; }
        public List<KPICheckModel> KPIs { get; set; }
    }

    public interface IQueueManager
    {
        Task AddToNotificationQueue(EnagageNotificationMain notification);
        Task AddToAggregatedNotificationQueue(EnagageNotificationMain notification);
        Task AddToFieldUserNotification(EnagageNotificationEmployee enagageNotificationEmp);
        Task AddToNotificationMessage(EnagageNotificationEmployee enagageNotificationMesage);
    }

    public class QueueManager : IQueueManager
    {
        private readonly QueueHandlerService handler;
        public QueueManager(QueueHandlerService handler)
        {
            this.handler = handler;
        }
        public async Task AddToNotificationQueue(EnagageNotificationMain notification)
        {
            await handler.AddToQueue(Dependencies.NotificationMainQueue, notification).ConfigureAwait(false);
        }

        public async Task AddToAggregatedNotificationQueue(EnagageNotificationMain notification)
        {
            await handler.AddToQueue(Dependencies.NotificationAggregatedQueue, notification).ConfigureAwait(false);
        }

        public async Task AddToFieldUserNotification(EnagageNotificationEmployee enagageNotificationEmp)
        {
            await handler.AddToQueue(Dependencies.NotificationEmployeeQueue, enagageNotificationEmp).ConfigureAwait(false);
        }

        public async Task AddToNotificationMessage(EnagageNotificationEmployee enagageNotificationMesage)
        {
            await handler.AddToQueue(Dependencies.NotificationMessageQueue, enagageNotificationMesage).ConfigureAwait(false);
        }
    }
}