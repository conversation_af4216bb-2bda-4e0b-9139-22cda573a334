﻿using System;
using System.Collections.Generic;
using Libraries.CommonEnums;
using Libraries.CommonEnums.Helpers;
using Libraries.PerspectiveColumns.Interface;

namespace Libraries.PerspectiveColumns;

public class FlexibleTargetVsAchievementPerspective : IPerspective
{
    private readonly LinkNames linkNames;

    public FlexibleTargetVsAchievementPerspective(Dictionary<string, string> nomenclatureDict)
    {
        linkNames = LinkNames.GetLinkNames(nomenclatureDict);
    }

    List<PerspectiveColumnModel> IPerspective.Columns
    {
        get => Columns;
        set => throw new NotImplementedException();
    }

    public List<PerspectiveColumnModel> Columns => new List<PerspectiveColumnModel>
    {
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L8Position,
            Name = "L8Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L8Position} Code",
            Name = "L8Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L8Position} User",
            Name = "L8Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L8Position} User ErpId",
            Name = "L8Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L7Position,
            Name = "L7Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L7Position} Code",
            Name = "L7Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L7Position} User",
            Name = "L7Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L7Position} User ErpId",
            Name = "L7Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L6Position,
            Name = "L6Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L6Position} Code",
            Name = "L6Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L6Position} User",
            Name = "L6Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L6Position} User ErpId",
            Name = "L6Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L5Position,
            Name = "L5Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L5Position} Code",
            Name = "L5Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L5Position} User",
            Name = "L5Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L5Position} User ErpId",
            Name = "L5Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L4Position,
            Name = "L4Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L4Position} Code",
            Name = "L4Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L4Position} User",
            Name = "L4Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L4Position} User ErpId",
            Name = "L4Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L3Position,
            Name = "L3Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L3Position} Code",
            Name = "L3Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L3Position} User",
            Name = "L3Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L3Position} User ErpId",
            Name = "L3Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L2Position,
            Name = "L2Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L2Position} Code",
            Name = "L2Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L2Position} User",
            Name = "L2Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L2Position} User ErpId",
            Name = "L2Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = linkNames.L1Position,
            Name = "L1Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position,
            IsMandatory = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L1Position} Code",
            Name = "L1Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position,
            IsMandatory = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L1Position} User",
            Name = "L1Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position,
            IsMandatory = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = $"{linkNames.L1Position} User ErpId",
            Name = "L1Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position,
            IsMandatory = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Beat,
            Name = "Beat",
            IsMeasure = false,
            IsDimension = true,
            HierarchyOrder = 4
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Route,
            Name = "Route",
            IsMeasure = false,
            IsDimension = true,
            HierarchyOrder = 4
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Territory,
            Name = "Territory",
            IsMeasure = false,
            IsDimension = true,
            HierarchyOrder = 3
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Region,
            Name = "Region",
            IsMeasure = false,
            IsDimension = true,
            HierarchyOrder = 2
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Zone,
            Name = "Zone",
            IsMeasure = false,
            IsDimension = true,
            HierarchyOrder = 1
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Level5,
            Name = "Level5",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Level6,
            Name = "Level6",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Level7,
            Name = "Level7",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = "Product ErpID",
            Name = "ProductErpId",
            IsDimension = true,
            HierarchyOrder = 4
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = "Product",
            Name = "ProductName",
            IsDimension = true,
            HierarchyOrder = 4
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = linkNames.SecondaryCategory,
            Name = "SecondaryCategory",
            IsMeasure = false,
            IsDimension = true,
            HierarchyOrder = 3
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = linkNames.PrimaryCategory,
            Name = "PrimaryCategory",
            IsMeasure = false,
            IsDimension = true,
            HierarchyOrder = 2
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = "Style",
            Name = "DisplayCategory",
            IsMeasure = false,
            IsDimension = true,
            HierarchyOrder = 4
        },
        new PerspectiveColumnModel
        {
            Attribute = "Product",
            DisplayName = "Product Divison",
            Name = "ProductDivision",
            IsMeasure = false,
            IsDimension = true,
            HierarchyOrder = 1
        },
        new PerspectiveColumnModel { Attribute = "Focussed Product Rule", DisplayName = "Focussed Product Rule", Name = "FocussedProductRule", IsDimension = true },
        new PerspectiveColumnModel
        {
            Attribute = "Must Sell Rule",
            DisplayName = "Must Sell Rule",
            Name = "MustSellRule",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Outlet Attributes",
            DisplayName = linkNames.Outlets,
            Name = "Shop",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Outlet Attributes",
            DisplayName = $"{linkNames.Outlets} ErpId",
            Name = "ShopErpId",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Outlet Attributes",
            DisplayName = $"{linkNames.Outlets} OwnersName",
            Name = "ShopOwnersName",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Outlet Attributes",
            DisplayName = $"{linkNames.Outlets} OwnersNo",
            Name = "ShopOwnersNumber",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Outlet Attributes",
            DisplayName = $"{linkNames.Outlets} Address",
            Name = "ShopAddress",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Outlet Attributes",
            DisplayName = $"{linkNames.Outlets} Market",
            Name = "ShopMarket",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Outlet Attributes",
            DisplayName = $"{linkNames.Outlets} Town",
            Name = "ShopSubCity",
            IsMeasure = true,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Outlet Attributes",
            DisplayName = $"{linkNames.Outlets} City",
            Name = "ShopCity",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Outlet Attributes",
            DisplayName = $"{linkNames.Outlets} State",
            Name = "ShopState",
            IsMeasure = true,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Outlet Attributes",
            DisplayName = $"{linkNames.Outlets} Type",
            Name = "ShopType",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Outlet Attributes",
            DisplayName = $"{linkNames.Outlets} Segmentation",
            Name = "ShopSegmentation",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Outlet Attributes",
            DisplayName = $"Focussed {linkNames.Outlets}",
            Name = "FocussedShop",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Outlet Attributes",
            DisplayName = $"{linkNames.Outlets} Channel",
            Name = "ShopChannel",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Channel Partner",
            DisplayName = linkNames.Distributor,
            Name = "Distributor",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Channel Partner",
            DisplayName = $"{linkNames.Distributor} Erp ID",
            Name = "DistributorErpId",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Channel Partner",
            DisplayName = $"{linkNames.Distributor} Address",
            Name = "DistributorAddress",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Channel Partner",
            DisplayName = "Stockist Type",
            Name = "StockistType",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Channel Partner",
            DisplayName = linkNames.SuperStockist,
            Name = "SuperStockist",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Channel Partner",
            DisplayName = $"{linkNames.SuperStockist} Erp ID",
            Name = "SuperStockistErpID",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Channel Partner",
            DisplayName = $"{linkNames.SuperStockist} Address",
            Name = "SuperstockistAddress",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Performance",
            DisplayName = "Target",
            Name = "Target",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            IsMandatory = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Performance",
            DisplayName = "Achievement",
            Name = "Achievement",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            IsMandatory = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Performance",
            DisplayName = "Achievement%",
            Name = "AchievementPercentage",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            IsMandatory = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Performance",
            DisplayName = "Current Run Rate",
            Name = "CurrentRunRate",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            IsMandatory = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Performance",
            DisplayName = "Required Run Rate",
            Name = "RequiredRunRate",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value,
            IsMandatory = true
        }
    };
}
