using FAEngage.DbStorage.DbContexts;
using jjm.one.Serilog.Sinks.SlackWebHook;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using Serilog.Events;
using ValueFirstMessageProcessor;

var builder = new HostBuilder()
                .ConfigureAppConfiguration((config) =>
                {
                    var keyVaultEndpoint = Environment.GetEnvironmentVariable("KEYVAULT_ENDPOINT");
                    if (!string.IsNullOrEmpty(keyVaultEndpoint))
                    {
                        config.AddAzureKeyVault(keyVaultEndpoint);
                    }

                    config.AddEnvironmentVariables();
                })
                .UseSerilog((hostingContext, loggerConfiguration) =>
                {
                    var slackUri = "*********************************************************************************";
                    var env = hostingContext.Configuration.GetValue<string>("AppSettings:Deployment");

#if DEBUG
                    loggerConfiguration
                        .MinimumLevel.Information();
#else
                    if (env == "dev")
                        loggerConfiguration
                            .MinimumLevel.Information();
                    else
                        loggerConfiguration
                            .MinimumLevel.Warning();
#endif
                    loggerConfiguration
                        .Enrich.FromLogContext()
                        .WriteTo.Console()
                        .WriteTo.Slack(
                        slackWebHookUrl: slackUri,
                        slackChannel: "faengageprocessorv2",
                        slackUsername: $"{env}-Engage Processor Logger",
                        slackEmojiIcon: ":radioactive_sign:",
                        periodicBatchingSinkOptionsBatchSizeLimit: 1,
                        periodicBatchingSinkOptionsPeriod: TimeSpan.FromMilliseconds(1000),
                        periodicBatchingSinkOptionsQueueLimit: 10000,
                        sinkRestrictedToMinimumLevel: LogEventLevel.Error
                                      );
                })
                .ConfigureWebJobs(b =>
                {
                    b.AddTimers();
                })
                .ConfigureServices((context, services) =>
                {
                    // Add dependencies here 
                    services.AddDbContext<TransactionDbContext>(options =>
                        options.UseSqlServer(context.Configuration.GetConnectionString("TransactionDbConnectionString")));

                    services.AddDbContext<WritableTransactionDbContext>(options =>
                        options.UseSqlServer(context.Configuration.GetConnectionString("WritableTransactionDbConnectionString")));

                    // register the processor
                    services.AddScoped<Processor>();

                })
                .UseConsoleLifetime();
var host = builder.Build();
using (host)
{
    await host.Services.GetRequiredService<Processor>().ProcessAsync();
}