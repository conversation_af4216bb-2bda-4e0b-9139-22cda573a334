﻿namespace Library.ResponseHelpers;

/// <summary>
///     Object to Enumerate the details of the Response
/// </summary>
public class ApiResponse
{
    public ApiResponse() : this("Task")
    {
    }

    public ApiResponse(string taskName)
    {
        ResponseList = new List<ApiResponseMessage>();
        Response = ResponseStatus.Failure;
        ResponseStatusCount = new ResponseStatusCount(taskName);
    }

    public bool Success { get; set; }

    /// <summary>
    ///     Message Indicating the Overall Response Summary of the Requested API
    /// </summary>
    public string Message { get; set; }

    /// <summary>
    ///     List of Errors
    /// </summary>
    public List<ApiResponseMessage> ResponseList { get; set; }

    /// <summary>
    ///     Enum Indicating the Response status
    /// </summary>
    public ResponseStatus Response { get; set; }

    /// <summary>
    ///     Summary containing Number of SuccessFull And Failed Tasks
    /// </summary>
    public ResponseStatusCount ResponseStatusCount { get; set; }

    public static ApiResponse GetSuccess(string message = "Success")
    {
        return new ApiResponse { Message = message, Success = true };
    }

    public static ApiResponse GetFailure(string errorMessage, bool success = false)
    {
        return new ApiResponse { Message = errorMessage, Success = success };
    }

    public bool IsSuccess()
    {
        return Response == ResponseStatus.Success || Response == ResponseStatus.Ignored;
    }

    public void CalculateResponse()
    {
        Message = ResponseStatusCount.StatusMessage;
        Response = ResponseStatusCount.ResponseStatus;
        ResponseList = ResponseList.OrderBy(r => r.ResponseStatus).ToList();
        if (Response == ResponseStatus.Success || Response == ResponseStatus.PartialSuccess || Response == ResponseStatus.Ignored)
        {
            Success = true;
        }
    }

    public static ApiResponse operator +(ApiResponse a, ApiResponse b)
    {
        var total = new ApiResponse { ResponseStatusCount = a.ResponseStatusCount + b.ResponseStatusCount, Message = (!string.IsNullOrWhiteSpace(a.Message) ? a.Message + ";" : "") + b.Message };
        total.ResponseList.AddRange(a.ResponseList);
        total.ResponseList.AddRange(b.ResponseList);
        if (a.Response == ResponseStatus.Ignored && b.Response == ResponseStatus.Ignored)
        {
            total.Response = ResponseStatus.Ignored;
        }
        else if (a.IsSuccess() && b.IsSuccess())
        {
            total.Response = ResponseStatus.Success;
        }
        else if ((a.Response == ResponseStatus.Failure && b.Response != ResponseStatus.Success) || (b.Response == ResponseStatus.Failure && a.Response != ResponseStatus.Success))
        {
            total.Response = ResponseStatus.Failure;
        }
        else
        {
            total.Response = ResponseStatus.PartialSuccess;
        }

        return total;
    }

    internal static ApiResponse GetSuccessResponse()
    {
        return new ApiResponse { Response = ResponseStatus.Success };
    }
}

/// <summary>
///     Count Of Response Status Items
/// </summary>
public class ResponseStatusCount
{
    public int Failed;

    public int Ignored;

    public int Updated;

    public ResponseStatusCount()
    {
    }

    public ResponseStatusCount(string name)
    {
        Name = name;
    }

    public string Name { get; set; }

    public int Total => Updated + Failed + Ignored;

    public string StatusMessage =>
        Name + ": Total-" + Total
        + ", Updated-" + Updated
        + ", Failed-" + Failed
        + ", Ignored-" + Ignored;

    public ResponseStatus ResponseStatus
    {
        get
        {
            if (Failed > 0)
            {
                if (Updated > 0)
                {
                    return ResponseStatus.PartialSuccess;
                }

                return ResponseStatus.Failure;
            }

            if (Updated > 0)
            {
                return ResponseStatus.Success;
            }

            return ResponseStatus.Ignored;
        }
    }

    public override string ToString()
    {
        return StatusMessage;
    }

    public static ResponseStatusCount operator +(ResponseStatusCount a, ResponseStatusCount b)
    {
        a.Failed += b.Failed;
        a.Updated += b.Updated;
        a.Ignored += b.Ignored;
        return a;
    }
}

/// <summary>
///     Base for Error and Success Messages
/// </summary>
public class ApiResponseMessage
{
    /// <summary>
    ///     ERPID of the Requested insertion/updation
    /// </summary>

    public string ERPId { get; set; }

    /// <summary>
    ///     Error Message for the indicated ERPID
    /// </summary>
    public string Message { get; set; }

    /// <summary>
    ///     Status of the action requested from Api
    /// </summary>
    public ResponseStatus ResponseStatus { get; set; }

    /// <summary>
    ///     GUID of the database Entry with respect to the ERP Id Provided
    /// </summary>
    public string GUID { get; set; }

    #region NonFAInvoice

    /// <summary>
    ///     ERPID of the Product
    /// </summary>

    public string ProductErpId { get; set; }

    /// <summary>
    ///     Status of the action in true/false format
    /// </summary>

    public bool ResponseStatus1 { get; set; }

    /// <summary>
    ///     ErpId of Distributor
    /// </summary>

    public string DistributorErpId { get; set; }

    /// <summary>
    ///     ErpId of Retailer
    /// </summary>

    public string RetailerErpId { get; set; }

    #endregion
}

public enum ResponseStatus
{
    /// <summary>
    ///     Completely Failed, No Action performed
    /// </summary>
    Failure = 0,

    /// <summary>
    ///     Completely Passed, All jobs completed Successfully
    /// </summary>
    Success = 1,

    /// <summary>
    ///     Partial Success and Few failed
    /// </summary>
    PartialSuccess = 2,

    /// <summary>
    ///     All Inputs Ignored
    /// </summary>
    Ignored = 3
}
