﻿using Libraries.CommonEnums;

namespace FAEngage.Core.Models.MasterDbModels
{
    public class Location
    {
        public long Id { get; set; }
        public bool IsBlocked { get; set; }
        public string ShopName { get; set; }
        public OutletSegmentation Segmentation { get; set; }
        public long CompanyId { get; set; }
        public string ErpId { get; set; }
        public long BeatId { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public long? ShopTypeId { get; set; }
        public OutletChannel OutletChannel { set; get; }
        public string MarketName { set; get; }
        public string City { set; get; }
        public string SubCity { get; set; }
        public string District { get; set; }
        public string Country { get; set; }
        public string State { set; get; }
        public OutletSegmentation CompanySegmentation { set; get; }
        public bool IsFocused { set; get; }
        public string ShopTypeCode { get; set; }
        public string CustomTags { get; set; }
        public string OwnersName { get; set; }
        public string OwnersNo { get; set; }
        public virtual TheShopType TheShopType { get; set; }
        public virtual LocationBeat Beat { get; set; }
    }

    public class TheShopType
    {
        public long Id { get; set; }
        public string ShopTypeName { get; set; }
        public string ErpId { get; set; }
        public long ChannelId { get; set; }
        public long CompanyId { get; set; }
        public bool IsInvalid { get; set; }
        public virtual Channel Channel { get; set; }
    }

    public class Channel
    {
        public long Id { get; set; }
        public long CompanyId { get; set; }
        public OutletChannel Enum { get; set; }
        public string DefaultName { get; set; }
        public string CustomName { get; set; }
        public string ErpId { get; set; }
    }

    public class LocationBeat
    {
        public long Id { get; set; }
        public bool IsDeactive { get; set; }
        public string Name { get; set; }
        public long? TerritoryId { get; set; }
        public virtual Territory Territory { get; set; }
        public ICollection<Location> Location { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public long Company { get; set; }
        public string ErpId { get; set; }
    }

    public class Territory
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public bool IsDeactive { get; set; }
        public long CompanyId { set; get; }
        public long? RegionId { set; get; }
        public virtual Region TheRegion { set; get; }
        public virtual LocationBeat LocationBeats { get; set; }
    }
}
