﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.Net;

namespace Library.PaymentService.Models
{
    public class STKPushResponse
    {
        public HttpStatusCode StatusCode { get; set; }
        public SuccessResponse? SuccessResponse { get; set; }
        public FailureResponse? FailureResponse { get; set; }
    }

    public class SuccessResponse
    {
        public string? MerchantRequestID { get; set; }
        public string? CheckoutRequestID { get; set; }
        public string? ResponseCode { get; set; }
        public string? ResponseDescription { get; set; }
        public string? CustomerMessage { get; set; }
    }

    public class FailureResponse
    {
        public string? requestId { get; set; }
        public string? errorCode { get; set; }
        public string? errorMessage { get; set; }
    }
}
