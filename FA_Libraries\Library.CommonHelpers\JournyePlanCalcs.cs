﻿using System;

namespace Library.CommonHelpers;

public static class JourneyPlanCalcs
{
    public static int GetTodayDayNumberJourneyPlan(DateTime startDate, DateTime today, int? planFrequency = null)
    {
        var daynumber = (today - startDate).Days;
        if (planFrequency.HasValue)
        {
            if (daynumber < 0)
            {
                return 0;
            }

            daynumber %= planFrequency.Value;
        }

        daynumber++;
        return daynumber;
    }
}
