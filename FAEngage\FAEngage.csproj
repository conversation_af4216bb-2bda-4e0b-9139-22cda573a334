﻿<Project Sdk="Microsoft.NET.Sdk;Microsoft.NET.Sdk.Publish">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
	<InvariantGlobalization>false</InvariantGlobalization>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Cronos" Version="0.11.0" />
    <PackageReference Include="jjm.one.Serilog.Sinks.SlackWebHook" Version="2.1.4" />
    <PackageReference Include="Microsoft.Azure.KeyVault.WebKey" Version="3.0.5" />
    <PackageReference Include="Microsoft.Azure.Services.AppAuthentication" Version="1.6.2" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions" Version="5.0.0" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.Storage.Queues" Version="5.3.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.AzureKeyVault" Version="3.1.24" />
      <PackageReference Include="Polly" Version="8.6.2" />
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
  </ItemGroup>
  <ItemGroup>
      <ProjectReference Include="..\FAEngage.Core\FAEngage.Core.csproj" />
      <ProjectReference Include="..\FAEngage.DbStorage\FAEngage.DbStorage.csproj" />
    <ProjectReference Include="..\FA_Libraries\Library.Infrastructure\Library.Infrastructure.csproj" />
    <ProjectReference Include="..\FA_Libraries\Library.ResiliencyHelpers\Library.ResiliencyHelpers.csproj" />
    <ProjectReference Include="..\FA_Libraries\Library.SlackService\Library.SlackService.csproj" />
  </ItemGroup>
</Project>