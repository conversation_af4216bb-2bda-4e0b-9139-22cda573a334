﻿using System;
using System.Collections.Generic;
using Libraries.CommonEnums;
using Libraries.PerspectiveColumns.Interface;

namespace Libraries.PerspectiveColumns;

public class TrendReportPerspective : IPerspective
{
    //private linkNames linkNames;

    public TrendReportPerspective(long companyId)
    {
        // linkNames = InMemorySettings.GetLinkNames(companyId);
    }

    public static List<PerspectiveColumnModel> Columns => new()
    {
        // Name Property Needs to match the key used for Nomenclature For QuickViz
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "GSM",
            Name = "GSM",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false,
            IsPivot = false
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "NationalSalesManager",
            Name = "NSM",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false,
            IsPivot = false
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "ZonalSalesManager",
            Name = "ZSM",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false,
            IsPivot = false
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "RegionalSalesManager",
            Name = "RSM",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false,
            IsPivot = false
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "AreaSalesManager",
            Name = "ASM",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false,
            IsPivot = false
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "Employee",
            Name = "ESM",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "FieldUser HQ",
            Name = "FieldUserHQ",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "FieldUser ErpId",
            Name = "FieldUserERPID",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "FieldUser Designation",
            Name = "FieldUserDesignation",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "Rank",
            Name = "FieldUserRank",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "User Status",
            Name = "UserStatus",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false,
            IsPivot = false
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "Active Days",
            Name = "ActiveDays",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false,
            IsPivot = false
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "FieldUser Region",
            Name = "FieldUserRegion",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false,
            IsPivot = false
        },
        new PerspectiveColumnModel
        {
            Attribute = "Field User",
            DisplayName = "FieldUser Zone",
            Name = "FieldUserZone",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsOtherMeasure = false,
            IsPivot = false
        },
        // Name Property Of New Positions need to be in this Pattern (LXPosition) only or Filtering based on setting won't work
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L8Position",
            Name = "L8Position",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L7Position",
            Name = "L7Position",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L6Position",
            Name = "L6Position",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L5Position",
            Name = "L5Position",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L4Position",
            Name = "L4Position",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L3Position",
            Name = "L3Position",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L2Position",
            Name = "L2Position",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L1Position",
            Name = "L1Position",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L8Position Code",
            Name = "L8Position_Code",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L7Position Code",
            Name = "L7Position_Code",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L6Position Code",
            Name = "L6Position_Code",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L5Position Code",
            Name = "L5Position_Code",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L4Position Code",
            Name = "L4Position_Code",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L3Position Code",
            Name = "L3Position_Code",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L2Position Code",
            Name = "L2Position_Code",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L1Position Code",
            Name = "L1Position_Code",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L8Position User",
            Name = "L8Position_User",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L7Position User",
            Name = "L7Position_User",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L6Position User",
            Name = "L6Position_User",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L5Position User",
            Name = "L5Position_User",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L4Position User",
            Name = "L4Position_User",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L3Position User",
            Name = "L3Position_User",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L2Position User",
            Name = "L2Position_User",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L1Position User",
            Name = "L1Position_User",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L8Position User ErpId",
            Name = "L8Position_UserErpId",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L7Position User ErpId",
            Name = "L7Position_UserErpId",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L6Position User ErpId",
            Name = "L6Position_UserErpId",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L5Position User ErpId",
            Name = "L5Position_UserErpId",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L4Position User ErpId",
            Name = "L4Position_UserErpId",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L3Position User ErpId",
            Name = "L3Position_UserErpId",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L2Position User ErpId",
            Name = "L2Position_UserErpId",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "L1Position User ErpId",
            Name = "L1Position_UserErpId",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "FieldUser HQ",
            Name = "L1Position_UserHQ",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Rank",
            Name = "L1Position_UserRank",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "User Status",
            Name = "L1Position_UserStatus",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "Active Days",
            Name = "L1Position_ActiveDays",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "FieldUser Region",
            Name = "L1Position_UserRegion",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Position",
            DisplayName = "FieldUser Zone",
            Name = "L1Position_UserZone",
            IsMeasure = false,
            IsDimension = true
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = "Level7",
            Name = "Level7",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = false,
            IsOtherMeasure = false
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = "Level6",
            Name = "Level6",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = false,
            IsOtherMeasure = false
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = "Level5",
            Name = "Level5",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = false,
            IsOtherMeasure = false
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = "Zone",
            Name = "Zone",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = false,
            IsOtherMeasure = false
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = "Region",
            Name = "Region",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsPivot = false,
            IsOtherMeasure = false
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Territory",
            DisplayName = "Territory",
            Name = "Territory",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTDTC",
            Name = "LMTDTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTDTC",
            Name = "MTDTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvgTC",
            Name = "L3MAvgTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTC",
            Name = "LMTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3MTC",
            Name = "PerL3MTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTDPC",
            Name = "LMTDPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTDPC",
            Name = "MTDPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvgPC",
            Name = "L3MAvgPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMPC",
            Name = "LMPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3MPC",
            Name = "PerL3MPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Working Days",
            Name = "LMTDWorkingDays",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Working Days",
            Name = "MTDWorkingDays",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg Working Days",
            Name = "L3MAvgWorkingDays",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Working Days",
            Name = "LMWorkingDays",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M Working Days",
            Name = "PerL3MWorkingDays",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Avg. TC (per day)",
            Name = "AvgTCPerday",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Avg. PC (per day)",
            Name = "AvgPCPerday",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "UTC",
            Name = "UTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "UPC",
            Name = "UPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "OVT",
            Name = "OVT",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "OVC",
            Name = "OVC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Avg Retailing Time",
            Name = "AvgRetailingTime",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Scheme Value",
            Name = "SchemeValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Number of telephonic orders",
            Name = "Numberoftelephonicorders",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Order Qty ( Unit )",
            Name = "OrderInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Value",
            Name = "Value",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "NetValue",
            Name = "NetValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Order Qty ( StdUnit )",
            Name = "SalesInStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Order Qty ( SuperUnit )",
            Name = "SalesInSupUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Net Value (Dispatch)",
            Name = "DispatchInRevenue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Dispatch Qty ( StdUnit )",
            Name = "DispatchInStdUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Dispatch Qty ( SuperUnit )",
            Name = "DispatchInSupUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Dispatch Qty ( Unit )",
            Name = "DispatchInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "AvgValuePerCall",
            Name = "DropSize",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Total Lines",
            Name = "TotalLines",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LPC",
            Name = "LPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Ach",
            Name = "Achievement",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Ach(Dispatch)",
            Name = "DispatchAchievement",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Days Retailing",
            Name = "DaysRetailing",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "SC",
            Name = "SC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "USC",
            Name = "USC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "New Outlets",
            Name = "NewOutlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "New Outlets Value",
            Name = "NewOutletsValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Avg Value Per New Outlet",
            Name = "ValuePerNewOutlet",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "FO Qty ( Unit )",
            Name = "FOQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "FO Qty ( StdUnit )",
            Name = "FOStdQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "FO Qty ( SuperUnit )",
            Name = "FOSupQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "FO Value",
            Name = "FOValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "FocusedOutlet UTC",
            Name = "FocusedOutletsUTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "FocusedOutlet UPC",
            Name = "FocusedOutletsUPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        //Master measures to be shown only if position code setting is off for now (08/07/2021)
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "Employee Overall Target",
            Name = "Targets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "Total Outlets",
            Name = "Outlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "Total Beats",
            Name = "Beats",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "Total Routes",
            Name = "Routes",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "Total Distributors",
            Name = "Distributors",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "FocusedOutlet Count",
            Name = "TotalFocusedOutlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Focus Product Order Qty ( Unit )",
            Name = "FocProdOrderQtyUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Focus Product Order Qty ( StdUnit )",
            Name = "FocProdOrderQtyStd",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Focus Product Order Qty ( SuperUnit )",
            Name = "FocProdOrderQtySup",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Avg. TC (per day)",
            Name = "LMTDAvgTCPerday",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Avg. PC (per day)",
            Name = "LMTDAvgPCPerday",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD UTC",
            Name = "LMTDUTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD UPC",
            Name = "LMTDUPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD OVT",
            Name = "LMTDOVT",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD OVC",
            Name = "LMTDOVC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Avg Retailing Time",
            Name = "LMTDAvgRetailingTime",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Scheme Value",
            Name = "LMTDSchemeValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Number of telephonic orders",
            Name = "LMTDNumberoftelephonicorders",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Order Qty ( Unit )",
            Name = "LMTDOrderInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Value",
            Name = "LMTDValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD NetValue",
            Name = "LMTDNetValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Order Qty ( StdUnit )",
            Name = "LMTDSalesInStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Order Qty ( SuperUnit )",
            Name = "LMTDSalesInSupUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Net Value (Dispatch)",
            Name = "LMTDDispatchInRevenue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Dispatch Qty ( StdUnit )",
            Name = "LMTDDispatchInStdUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Dispatch Qty ( SuperUnit )",
            Name = "LMTDDispatchInSupUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Dispatch Qty ( Unit )",
            Name = "LMTDDispatchInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD AvgValuePerCall",
            Name = "LMTDDropSize",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Total Lines",
            Name = "LMTDTotalLines",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD LPC",
            Name = "LMTDLPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Ach",
            Name = "LMTDAchievement",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Ach(Dispatch)",
            Name = "LMTDDispatchAchievement",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Days Retailing",
            Name = "LMTDDaysRetailing",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD SC",
            Name = "LMTDSC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD USC",
            Name = "LMTDUSC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD New Outlets",
            Name = "LMTDNewOutlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD New Outlets Value",
            Name = "LMTDNewOutletsValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Avg Value Per New Outlet",
            Name = "LMTDValuePerNewOutlet",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD FO Qty ( Unit )",
            Name = "LMTDFOQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD FO Qty ( StdUnit )",
            Name = "LMTDFOStdQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD FO Qty ( SuperUnit )",
            Name = "LMTDFOSupQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD FO Value",
            Name = "LMTDFOValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD FocusedOutlet UTC",
            Name = "LMTDFocusedOutletsUTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD FocusedOutlet UPC",
            Name = "LMTDFocusedOutletsUPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Focus Product Order Qty ( Unit )",
            Name = "LMTDFocProdOrderQtyUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Focus Product Order Qty ( StdUnit )",
            Name = "LMTDFocProdOrderQtyStd",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Focus Product Order Qty ( SuperUnit )",
            Name = "LMTDFocProdOrderQtySup",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },

        //Master measures to be shown only if position code setting is off for now (08/07/2021)
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "LMTD Employee Overall Target",
            Name = "LMTDTargets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "LMTD Total Outlets",
            Name = "LMTDOutlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "LMTD Total Beats",
            Name = "LMTDBeats",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "LMTD Total Routes",
            Name = "LMTDRoutes",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "LMTD Total Distributors",
            Name = "LMTDDistributors",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "LMTD FocusedOutlet Count",
            Name = "LMTDTotalFocusedOutlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Avg. TC (per day)",
            Name = "MTDAvgTCPerday",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Avg. PC (per day)",
            Name = "MTDAvgPCPerday",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD UTC",
            Name = "MTDUTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD UPC",
            Name = "MTDUPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD OVT",
            Name = "MTDOVT",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD OVC",
            Name = "MTDOVC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Avg Retailing Time",
            Name = "MTDAvgRetailingTime",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Scheme Value",
            Name = "MTDSchemeValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Number of telephonic orders",
            Name = "MTDNumberoftelephonicorders",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Order Qty ( Unit )",
            Name = "MTDOrderInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Value",
            Name = "MTDValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD NetValue",
            Name = "MTDNetValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Order Qty ( StdUnit )",
            Name = "MTDSalesInStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Order Qty ( SuperUnit )",
            Name = "MTDSalesInSupUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Net Value (Dispatch)",
            Name = "MTDDispatchInRevenue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Dispatch Qty ( StdUnit )",
            Name = "MTDDispatchInStdUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Dispatch Qty ( SuperUnit )",
            Name = "MTDDispatchInSupUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Dispatch Qty ( Unit )",
            Name = "MTDDispatchInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD AvgValuePerCall",
            Name = "MTDDropSize",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Total Lines",
            Name = "MTDTotalLines",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD LPC",
            Name = "MTDLPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Ach",
            Name = "MTDAchievement",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Ach(Dispatch)",
            Name = "MTDDispatchAchievement",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Days Retailing",
            Name = "MTDDaysRetailing",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD SC",
            Name = "MTDSC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD USC",
            Name = "MTDUSC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD New Outlets",
            Name = "MTDNewOutlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD New Outlets Value",
            Name = "MTDNewOutletsValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Avg Value Per New Outlet",
            Name = "MTDValuePerNewOutlet",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD FO Qty ( Unit )",
            Name = "MTDFOQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD FO Qty ( StdUnit )",
            Name = "MTDFOStdQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD FO Qty ( SuperUnit )",
            Name = "MTDFOSupQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD FO Value",
            Name = "MTDFOValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD FocusedOutlet UTC",
            Name = "MTDFocusedOutletsUTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD FocusedOutlet UPC",
            Name = "MTDFocusedOutletsUPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Focus Product Order Qty ( Unit )",
            Name = "MTDFocProdOrderQtyUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Focus Product Order Qty ( StdUnit )",
            Name = "MTDFocProdOrderQtyStd",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Focus Product Order Qty ( SuperUnit )",
            Name = "MTDFocProdOrderQtySup",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },

        //Master measures to be shown only if position code setting is off for now (08/07/2021)
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "MTD Employee Overall Target",
            Name = "MTDTargets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "MTD Total Outlets",
            Name = "MTDOutlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "MTD Total Beats",
            Name = "MTDBeats",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "MTD Total Routes",
            Name = "MTDRoutes",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "MTD Total Distributors",
            Name = "MTDDistributors",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "MTD FocusedOutlet Count",
            Name = "MTDTotalFocusedOutlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg Avg. TC (per day)",
            Name = "L3MAvgAvgTCPerday",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg Avg. PC (per day)",
            Name = "L3MAvgAvgPCPerday",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg UTC",
            Name = "L3MAvgUTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg UPC",
            Name = "L3MAvgUPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg OVT",
            Name = "L3MAvgOVT",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg OVC",
            Name = "L3MAvgOVC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg Avg Retailing Time",
            Name = "L3MAvgAvgRetailingTime",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg Scheme Value",
            Name = "L3MAvgSchemeValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg Number of telephonic orders",
            Name = "L3MAvgNumberoftelephonicorders",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg Order Qty ( Unit )",
            Name = "L3MAvgOrderInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg Value",
            Name = "L3MAvgValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg NetValue",
            Name = "L3MAvgNetValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg Order Qty ( StdUnit )",
            Name = "L3MAvgSalesInStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg Order Qty ( SuperUnit )",
            Name = "L3MAvgSalesInSupUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg Net Value (Dispatch)",
            Name = "L3MAvgDispatchInRevenue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg Dispatch Qty ( StdUnit )",
            Name = "L3MAvgDispatchInStdUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg Dispatch Qty ( SuperUnit )",
            Name = "L3MAvgDispatchInSupUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg Dispatch Qty ( Unit )",
            Name = "L3MAvgDispatchInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg AvgValuePerCall",
            Name = "L3MAvgDropSize",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg Total Lines",
            Name = "L3MAvgTotalLines",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg LPC",
            Name = "L3MAvgLPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg Ach",
            Name = "L3MAvgAchievement",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg Ach(Dispatch)",
            Name = "L3MAvgDispatchAchievement",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg Days Retailing",
            Name = "L3MAvgDaysRetailing",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg SC",
            Name = "L3MAvgSC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg USC",
            Name = "L3MAvgUSC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg New Outlets",
            Name = "L3MAvgNewOutlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg New Outlets Value",
            Name = "L3MAvgNewOutletsValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg Avg Value Per New Outlet",
            Name = "L3MAvgValuePerNewOutlet",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg FO Qty ( Unit )",
            Name = "L3MAvgFOQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg FO Qty ( StdUnit )",
            Name = "L3MAvgFOStdQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg FO Qty ( SuperUnit )",
            Name = "L3MAvgFOSupQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg FO Value",
            Name = "L3MAvgFOValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg FocusedOutlet UTC",
            Name = "L3MAvgFocusedOutletsUTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg FocusedOutlet UPC",
            Name = "L3MAvgFocusedOutletsUPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg Focus Product Order Qty ( Unit )",
            Name = "L3MAvgFocProdOrderQtyUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg Focus Product Order Qty ( StdUnit )",
            Name = "L3MAvgFocProdOrderQtyStd",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3MAvg Focus Product Order Qty ( SuperUnit )",
            Name = "L3MAvgFocProdOrderQtySup",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },

        //Master measures to be shown only if position code setting is off for now (08/07/2021)
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "L3MAvg Employee Overall Target",
            Name = "L3MAvgTargets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "L3MAvg Total Outlets",
            Name = "L3MAvgOutlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "L3MAvg Total Beats",
            Name = "L3MAvgBeats",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "L3MAvg Total Routes",
            Name = "L3MAvgRoutes",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "L3MAvg Total Distributors",
            Name = "L3MAvgDistributors",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "L3MAvg FocusedOutlet Count",
            Name = "L3MAvgTotalFocusedOutlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Avg. TC (per day)",
            Name = "LMAvgTCPerday",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Avg. PC (per day)",
            Name = "LMAvgPCPerday",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM UTC",
            Name = "LMUTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM UPC",
            Name = "LMUPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM OVT",
            Name = "LMOVT",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM OVC",
            Name = "LMOVC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Avg Retailing Time",
            Name = "LMAvgRetailingTime",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Scheme Value",
            Name = "LMSchemeValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Number of telephonic orders",
            Name = "LMNumberoftelephonicorders",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Order Qty ( Unit )",
            Name = "LMOrderInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Value",
            Name = "LMValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM NetValue",
            Name = "LMNetValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Order Qty ( StdUnit )",
            Name = "LMSalesInStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Order Qty ( SuperUnit )",
            Name = "LMSalesInSupUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Net Value (Dispatch)",
            Name = "LMDispatchInRevenue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Dispatch Qty ( StdUnit )",
            Name = "LMDispatchInStdUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Dispatch Qty ( SuperUnit )",
            Name = "LMDispatchInSupUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Dispatch Qty ( Unit )",
            Name = "LMDispatchInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM AvgValuePerCall",
            Name = "LMDropSize",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Total Lines",
            Name = "LMTotalLines",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM LPC",
            Name = "LMLPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Ach",
            Name = "LMAchievement",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Ach(Dispatch)",
            Name = "LMDispatchAchievement",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Days Retailing",
            Name = "LMDaysRetailing",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM SC",
            Name = "LMSC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM USC",
            Name = "LMUSC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM New Outlets",
            Name = "LMNewOutlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM New Outlets Value",
            Name = "LMNewOutletsValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Avg Value Per New Outlet",
            Name = "LMValuePerNewOutlet",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM FO Qty ( Unit )",
            Name = "LMFOQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM FO Qty ( StdUnit )",
            Name = "LMFOStdQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM FO Qty ( SuperUnit )",
            Name = "LMFOSupQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM FO Value",
            Name = "LMFOValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM FocusedOutlet UTC",
            Name = "LMFocusedOutletsUTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM FocusedOutlet UPC",
            Name = "LMFocusedOutletsUPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Focus Product Order Qty ( Unit )",
            Name = "LMFocProdOrderQtyUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Focus Product Order Qty ( StdUnit )",
            Name = "LMFocProdOrderQtyStd",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Focus Product Order Qty ( SuperUnit )",
            Name = "LMFocProdOrderQtySup",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },

        //Master measures to be shown only if position code setting is off for now (08/07/2021)
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "LM Employee Overall Target",
            Name = "LMTargets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "LM Total Outlets",
            Name = "LMOutlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "LM Total Beats",
            Name = "LMBeats",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "LM Total Routes",
            Name = "LMRoutes",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "LM Total Distributors",
            Name = "LMDistributors",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "LM FocusedOutlet Count",
            Name = "LMTotalFocusedOutlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M Avg. TC (per day)",
            Name = "PerL3AvgTCPerday",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M Avg. PC (per day)",
            Name = "PerL3AvgPCPerday",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M UTC",
            Name = "PerL3MUTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M UPC",
            Name = "PerL3MUPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M OVT",
            Name = "PerL3MOVT",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M OVC",
            Name = "PerL3MOVC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M Avg Retailing Time",
            Name = "PerL3MAvgRetailingTime",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M Scheme Value",
            Name = "PerL3MSchemeValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M Number of telephonic orders",
            Name = "PerL3MNumberoftelephonicorders",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M Order Qty ( Unit )",
            Name = "PerL3MOrderInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M Value",
            Name = "PerL3MValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M NetValue",
            Name = "PerL3MNetValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M Order Qty ( StdUnit )",
            Name = "PerL3MSalesInStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M Order Qty ( SuperUnit )",
            Name = "PerL3MSalesInSupUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M Net Value (Dispatch)",
            Name = "PerL3MDispatchInRevenue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M Dispatch Qty ( StdUnit )",
            Name = "PerL3MDispatchInStdUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M Dispatch Qty ( SuperUnit )",
            Name = "PerL3MDispatchInSupUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M Dispatch Qty ( Unit )",
            Name = "PerL3MDispatchInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M AvgValuePerCall",
            Name = "PerL3MDropSize",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M Total Lines",
            Name = "PerL3MTotalLines",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M LPC",
            Name = "PerL3MLPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M Ach",
            Name = "PerL3MAchievement",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M Ach(Dispatch)",
            Name = "PerL3MDispatchAchievement",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M Days Retailing",
            Name = "PerL3MDaysRetailing",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M SC",
            Name = "PerL3MSC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M USC",
            Name = "PerL3MUSC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M New Outlets",
            Name = "PerL3MNewOutlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M New Outlets Value",
            Name = "PerL3MNewOutletsValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M Avg Value Per New Outlet",
            Name = "PerL3MValuePerNewOutlet",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M FO Qty ( Unit )",
            Name = "PerL3MFOQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M FO Qty ( StdUnit )",
            Name = "PerL3MFOStdQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M FO Qty ( SuperUnit )",
            Name = "PerL3MFOSupQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M FO Value",
            Name = "PerL3MFOValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M FocusedOutlet UTC",
            Name = "PerL3MFocusedOutletsUTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M FocusedOutlet UPC",
            Name = "PerL3MFocusedOutletsUPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M Focus Product Order Qty ( Unit )",
            Name = "PerL3MFocProdOrderQtyUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M Focus Product Order Qty ( StdUnit )",
            Name = "PerL3MFocProdOrderQtyStd",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "PerL3M Focus Product Order Qty ( SuperUnit )",
            Name = "PerL3MFocProdOrderQtySup",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },

        //Master measures to be shown only if position code setting is off for now (08/07/2021)
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "PerL3M Employee Overall Target",
            Name = "PerL3MTargets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "PerL3M Total Outlets",
            Name = "PerL3MOutlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "PerL3M Total Beats",
            Name = "PerL3MBeats",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "PerL3M Total Routes",
            Name = "PerL3MRoutes",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "PerL3M Total Distributors",
            Name = "PerL3MDistributors",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Master Measure",
            DisplayName = "PerL3M FocusedOutlet Count",
            Name = "PerL3MTotalFocusedOutlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Days Started Late",
            Name = "MTDDaysStartedLate",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3M Avg Days Started Late",
            Name = "L3MAvgDaysStartedLate",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Days Started Late",
            Name = "LMTDDaysStartedLate",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Days Started Late",
            Name = "LMDaysStartedLate",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Per L3M Days Started Late",
            Name = "PerL3MDaysStartedLate",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Weekly Days Started Late",
            Name = "WeeklyDaysStartedLate",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Monthly Days Started Late",
            Name = "MonthlyDaysStartedLate",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Quarterly Days Started Late",
            Name = "QuarterlyDaysStartedLate",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "MTD Days First Call OVC",
            Name = "MTDDaysFrstCallOVC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "L3M Avg Days First Call OVC",
            Name = "L3MAvgDaysFrstCallOVC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LMTD Days First Call OVC",
            Name = "LMTDDaysFrstCallOVC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LM Days First Call OVC",
            Name = "LMDaysFrstCallOVC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Per L3M Days First Call OVC",
            Name = "PerL3MDaysFrstCallOVC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Weekly Days First Call OVC",
            Name = "WeeklyDaysFrstCallOVC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Monthly Days First Call OVC",
            Name = "MonthlyDaysFrstCallOVC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "Quarterly Days First Call OVC",
            Name = "QuarterlyDaysFrstCallOVC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD Avg. TC (per day)",
            Name = "LYMTDAvgTCPerday",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD Avg. PC (per day)",
            Name = "LYMTDAvgPCPerday",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD UTC",
            Name = "LYMTDUTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD UPC",
            Name = "LYMTDUPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD OVT",
            Name = "LYMTDOVT",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD OVC",
            Name = "LYMTDOVC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD Avg Retailing Time",
            Name = "LYMTDAvgRetailingTime",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD Scheme Value",
            Name = "LYMTDSchemeValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD Number of telephonic orders",
            Name = "LYMTDNumberoftelephonicorders",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD Order Qty ( Unit )",
            Name = "LYMTDOrderInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD Value",
            Name = "LYMTDValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD NetValue",
            Name = "LYMTDNetValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD Order Qty ( StdUnit )",
            Name = "LYMTDSalesInStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD Order Qty ( SuperUnit )",
            Name = "LYMTDSalesInSupUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD Net Value (Dispatch)",
            Name = "LYMTDDispatchInRevenue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD Dispatch Qty ( StdUnit )",
            Name = "LYMTDDispatchInStdUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD Dispatch Qty ( SuperUnit )",
            Name = "LYMTDDispatchInSupUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD Dispatch Qty ( Unit )",
            Name = "LYMTDDispatchInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD AvgValuePerCall",
            Name = "LYMTDDropSize",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD Total Lines",
            Name = "LYMTDTotalLines",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD LPC",
            Name = "LYMTDLPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD Ach",
            Name = "LYMTDAchievement",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD Ach(Dispatch)",
            Name = "LYMTDDispatchAchievement",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD Days Retailing",
            Name = "LYMTDDaysRetailing",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD SC",
            Name = "LYMTDSC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD USC",
            Name = "LYMTDUSC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD New Outlets",
            Name = "LYMTDNewOutlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD New Outlets Value",
            Name = "LYMTDNewOutletsValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD Avg Value Per New Outlet",
            Name = "LYMTDValuePerNewOutlet",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD FO Qty ( Unit )",
            Name = "LYMTDFOQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD FO Qty ( StdUnit )",
            Name = "LYMTDFOStdQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD FO Qty ( SuperUnit )",
            Name = "LYMTDFOSupQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD FO Value",
            Name = "LYMTDFOValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD FocusedOutlet UTC",
            Name = "LYMTDFocusedOutletsUTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD FocusedOutlet UPC",
            Name = "LYMTDFocusedOutletsUPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD Focus Product Order Qty ( Unit )",
            Name = "LYMTDFocProdOrderQtyUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD Focus Product Order Qty ( StdUnit )",
            Name = "LYMTDFocProdOrderQtyStd",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYMTD Focus Product Order Qty ( SuperUnit )",
            Name = "LYMTDFocProdOrderQtySup",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM Avg. TC (per day)",
            Name = "LYMAvgTCPerday",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM Avg. PC (per day)",
            Name = "LYMAvgPCPerday",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM UTC",
            Name = "LYMUTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM UPC",
            Name = "LYMUPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM OVT",
            Name = "LYMOVT",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM OVC",
            Name = "LYMOVC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM Avg Retailing Time",
            Name = "LYMAvgRetailingTime",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM Scheme Value",
            Name = "LYMSchemeValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM Number of telephonic orders",
            Name = "LYMNumberoftelephonicorders",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM Order Qty ( Unit )",
            Name = "LYMOrderInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM Value",
            Name = "LYMValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM NetValue",
            Name = "LYMNetValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM Order Qty ( StdUnit )",
            Name = "LYMSalesInStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM Order Qty ( SuperUnit )",
            Name = "LYMSalesInSupUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM Net Value (Dispatch)",
            Name = "LYMDispatchInRevenue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM Dispatch Qty ( StdUnit )",
            Name = "LYMDispatchInStdUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM Dispatch Qty ( SuperUnit )",
            Name = "LYMDispatchInSupUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM Dispatch Qty ( Unit )",
            Name = "LYMDispatchInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM AvgValuePerCall",
            Name = "LYMDropSize",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM Total Lines",
            Name = "LYMTotalLines",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM LPC",
            Name = "LYMLPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM Ach",
            Name = "LYMAchievement",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM Ach(Dispatch)",
            Name = "LYMTispatchAchievement",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM Days Retailing",
            Name = "LYMDaysRetailing",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM SC",
            Name = "LYMSC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM USC",
            Name = "LYMUSC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM New Outlets",
            Name = "LYMNewOutlets",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM New Outlets Value",
            Name = "LYMNewOutletsValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM Avg Value Per New Outlet",
            Name = "LYMValuePerNewOutlet",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM FO Qty ( Unit )",
            Name = "LYMFOQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM FO Qty ( StdUnit )",
            Name = "lymFOStdQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM FO Qty ( SuperUnit )",
            Name = "LYMFOSupQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM FO Value",
            Name = "LYMFOValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM FocusedOutlet UTC",
            Name = "LYMFocusedOutletsUTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM FocusedOutlet UPC",
            Name = "LYMFocusedOutletsUPC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM Focus Product Order Qty ( Unit )",
            Name = "LYMFocProdOrderQtyUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM Focus Product Order Qty ( StdUnit )",
            Name = "LYMFocProdOrderQtyStd",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        },
        new PerspectiveColumnModel
        {
            Attribute = "Sales Measure",
            DisplayName = "LYM Focus Product Order Qty ( SuperUnit )",
            Name = "LYMFocProdOrderQtySup",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Value
        }
    };

    List<PerspectiveColumnModel> IPerspective.Columns
    {
        get => Columns;
        set => throw new NotImplementedException();
    }
}
