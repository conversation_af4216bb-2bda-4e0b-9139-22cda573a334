﻿using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums;

public enum ProductLevelHierarchy
{
    SKU = 0,
    SecondaryCategory = 1,
    PrimaryCategory = 2,
    ProductDivision = 3,
    DisplayCategory = 4,
    ProductGroup = 5
}

public enum CalculationMeasure
{
    QuantityInUnits = 0,
    QuantityInStdUnits = 1,
    OrderValue = 2,
    LinesCut = 3
}

public enum UIElementType
{
    ProgressBar = 0,
    TargetAndAchievement = 1,
    TargetOnly = 2,
    AchievementOnly = 3
}

public enum SequencingType
{
    Absolute = 0,
    Relative = 1
}

public enum TaskManagementFocusAreaType
{
    NumberBased = 0,
    TargetAchievementBased = 1,
    SurveyBased = 2,
    IRBased = 3,
    ProductTag = 4,
    ProductRecommendationBased = 5,
    AssetManagementBased = 6, // Asset Audit
    PerfectCallBased = 7,
    FocusArea = 8,
    Qualifier = 9,
}

public enum TaskManagementQueryRelation
{
    F2KLocationsV3 = 0,
    Transactions = 1,
    Reports = 2,
    Unify = 3,
    FA_Masters = 4
}

public enum ProcessingType
{
    Manual = 0,
    Automatic = 1
}

public enum ParentType
{
    PerfectStore = 1
}

public enum TasksProductHierarchy
{
    SKU = 0,
    SecondaryCategory = 1,
    PrimaryCategory = 2,
    ProductDivision = 3,
    DisplayCategory = 4
}

public enum TaskEntityType
{
    Position = 1,
    User = 2,
    Beat = 3,
    Outlet = 4,
    Territory = 5,
    Region = 6,
    Zone = 7,
    Product = 8,
    PrimaryCategory = 9,
    SecondaryCategory = 10,
    ProductDivision = 11,
    DisplayCategory = 12,
    Distributor = 13,
    OutletChannel = 14,
    OutletSegmentation = 15,
    ShopTypes = 16,
    DistributorChannel = 17,
    DistributorSegmentation = 18,
    FocusedProductRule = 19,
    MustSellRule = 20,
    Level5Geography = 21,
    Level6Geography = 22,
    Level7Geography = 23
}

public enum TaskCalculationType
{
    Quantity_Units = 0,
    Quantity_StdUnits = 1,
    Order_Value = 2,
    Lines_Cut = 3
}

public enum TaskCalculationCriteria
{
    [Display(Name = "Segmentation Prescribed Visits Basic Automation")]
    BasicAutomation = 0,

    [Display(Name = "Monthly Revised Targets")]
    MonthlyRevisedTargets = 1,

    [Display(Name = "Segmentation Prescribed Visits + Historical Visit-wise Trends")]
    SegmentationWithTrends = 2,

    [Display(Name = "Historical Week-wise Trends")]
    WeeklyTrends = 3
}
