﻿using System.Text.Json;
using FAEngage.Core.Helpers;
using FAEngage.Core.Interfaces;
using FAEngage.Core.MasterRepositories;
using FAEngage.Core.Models;
using FAEngage.Core.Models.MasterDbModels;
using FAEngage.Core.Models.TransactionDbModels;
using FAEngage.Core.Services.MasterServices;
using FAEngage.Core.TransactionRepositories;
using Libraries.CommonEnums;
using Libraries.CommonModels;

namespace FAEngage.Core.Services
{
    public class FAEngageService(
        IFAEngageRepository fAEngageRepository,
        IKpiRepository kpiRepository,
        ICompanySettingsRepository companySettingsRepository,
        MTDService mtdService,
        IFAEngageReportService engageReportService,
        ITransactionKPIAchievementRepository transactionKpiAchievementRepository,
        IMasterKpiAchievementRepository masterKpiAchievementRepository,
        IFaEngageTransactionRepository faEngageTransactionRepository)
    {
        public async Task<List<EngageNotification>> GetActiveUserAppNotifications(DateTime date)
        {
            return await fAEngageRepository.GetActiveUSerAppNotificationsForDate(date);
        }

        public async Task<List<long>> GetUsersForAggregatedEangage(long companyId, long userId, PortalUserRole userRole, Guid transactionId)
        {
            var engageLogs = await faEngageTransactionRepository.GetLogsForTransactionId(companyId, transactionId);

            return engageLogs.SelectMany(s => s.AggregationHierarchy.Select(d => new
            {
                s,
                d.UserId,
                d.UserRole
            })).Where(f => f.UserId == userId && f.UserRole == userRole).Select(f => f.s.UserId).ToList();
        }

        public async Task<EngageNotification> GetNotification(long id, long companyId)
        {
            return await fAEngageRepository.GetNotification(id, companyId);
        }

        public async Task<Cohort> GetCohort(long cohort, long companyId)
        {
            return await fAEngageRepository.GetCohort(cohort, companyId);
        }

        public async Task<List<KPITriggers>> GetKPITriggers(long notificationId, long companyId)
        {
            return await fAEngageRepository.GetKpiTriggers(notificationId, companyId);
        }

        public async Task<KPICheckModel> CheckKPIPassed(long notificationId, long kpiId, long companyId, long userId, PortalUserRole? userRole,
            string matchValue, ComparisonOperator comparisonOperator)
        {
            var kpi = await kpiRepository.GetKpiById(kpiId, companyId);
            if (kpi != null)
            {
                var companySettingsDict = await companySettingsRepository.GetSettings(companyId);
                var companySettings = new CompanySettings(companySettingsDict);

                var yearStartMonth = companySettings.YearStartMonth;
                var todayDate = DateTime.UtcNow.Add(companySettings.TimeZoneOffset).Date;
                var mtdLmtd = await mtdService.GetMTD_LMTD(todayDate, companyId, true, yearStartMonth: yearStartMonth);
                var monthNumber = mtdLmtd.MTD.MonthNumber;

                var startDate = mtdLmtd.MTD.StartDate;
                var enddate = mtdLmtd.MTD.EndDate;

                switch (kpi.Frequency)
                {
                    case KpiFrequency.Daily:
                        startDate = todayDate.Date;
                        enddate = todayDate.AddDays(1).AddMilliseconds(-1);
                        break;

                    case KpiFrequency.Weekly:
                        int daysUntilkpiWeekDay = ((int)(kpi.WeekOfDay ?? DayOfWeek.Monday) - (int)todayDate.DayOfWeek + 7) % 7;
                        enddate = todayDate.AddDays(daysUntilkpiWeekDay);
                        startDate = enddate.AddDays(-7);
                        break;
                }

                var res = new KPICheckModel()
                {
                    AchValue = string.Empty,
                    KPIId = kpiId,
                    Passed = false
                };

                var reportAchValue = string.Empty;
                var transactionAchValue = string.Empty;
                var masterAchvalue = string.Empty;
                if (!string.IsNullOrEmpty(kpi.SQLQuery))
                {
                    reportAchValue = await engageReportService.GetKPIReportAchievement(companyId, userId, userRole, kpi.Id, startDate,
                        enddate);
                    if (reportAchValue == null || (reportAchValue != null && string.IsNullOrWhiteSpace(reportAchValue)))
                    {
                        return res;
                    }
                }

                if (!string.IsNullOrEmpty(kpi.TransactionSQLQuery))
                {
                    transactionAchValue = await transactionKpiAchievementRepository.GetKPIAchievedTarget(kpi.TransactionSQLQuery,
                        companyId, userId, startDate, enddate, monthNumber);

                    if (transactionAchValue == null || (transactionAchValue != null && string.IsNullOrWhiteSpace(transactionAchValue)))
                    {
                        return res;
                    }
                }

                if (!string.IsNullOrEmpty(kpi.MasterSQLQuery))
                {
                    masterAchvalue = await masterKpiAchievementRepository.GetKPIAchievedTarget(kpi.MasterSQLQuery,
                        companyId, userId, startDate, enddate, monthNumber);

                    if (masterAchvalue == null || (masterAchvalue != null && string.IsNullOrWhiteSpace(masterAchvalue)))
                    {
                        return res;
                    }
                }

                switch (kpi.Measure)
                {
                    case KpiMeasure.Currency:
                    case KpiMeasure.Decimal:
                    case KpiMeasure.PercentageNumber:
                    case KpiMeasure.Percentage:
                    case KpiMeasure.Number:
                        double.TryParse(matchValue, out double matchNumValue);
                        double achievedValue = 0;
                        switch (kpi.Relation)
                        {
                            case Relation.UseDirectReportDb:
                                achievedValue = double.TryParse(reportAchValue, out var reportAchValueDouble) ? Math.Round(reportAchValueDouble, 2) : 0;
                                break;

                            case Relation.UseDirectMasterDb:
                                achievedValue = double.TryParse(masterAchvalue, out var masterAchValueDouble) ? Math.Round(masterAchValueDouble, 2) : 0;
                                break;

                            case Relation.UseDirectTransactionDb:
                                achievedValue = double.TryParse(transactionAchValue, out var transactionAchValueDouble) ? Math.Round(transactionAchValueDouble, 2) : 0;
                                break;

                            case Relation.ReportDbDividedByMasterDb:
                                if (double.TryParse(reportAchValue, out reportAchValueDouble) && double.TryParse(masterAchvalue, out masterAchValueDouble) && !masterAchValueDouble.Equals(0))
                                {
                                    achievedValue = Math.Round(reportAchValueDouble / masterAchValueDouble, 2);
                                }

                                break;

                            case Relation.MasterDbDividedByReportDb:
                                if (double.TryParse(masterAchvalue, out masterAchValueDouble) && double.TryParse(reportAchValue, out reportAchValueDouble) && !reportAchValueDouble.Equals(0))
                                {
                                    achievedValue = Math.Round(masterAchValueDouble / reportAchValueDouble, 2);
                                }

                                break;

                            case Relation.MasterDbDividedByTransactionDb:
                                if (double.TryParse(masterAchvalue, out masterAchValueDouble) && double.TryParse(transactionAchValue, out transactionAchValueDouble) && !transactionAchValueDouble.Equals(0))
                                {
                                    achievedValue = Math.Round(masterAchValueDouble / transactionAchValueDouble, 2);
                                }

                                break;

                            case Relation.TransactionDbDividedByMasterDb:
                                if (double.TryParse(transactionAchValue, out transactionAchValueDouble) && double.TryParse(masterAchvalue, out masterAchValueDouble) && !masterAchValueDouble.Equals(0))
                                {
                                    achievedValue = Math.Round(transactionAchValueDouble / masterAchValueDouble, 2);
                                }

                                break;
                        }

                        switch (comparisonOperator)
                        {
                            case ComparisonOperator.EqualTo:
                                res.Passed = achievedValue == matchNumValue;
                                res.KPIId = kpiId;
                                res.AchValue = achievedValue.ToString();
                                break;

                            case ComparisonOperator.LessThan:
                                res.Passed = achievedValue < matchNumValue;
                                res.KPIId = kpiId;
                                res.AchValue = achievedValue.ToString();
                                break;

                            case ComparisonOperator.LessThanOrEqualTo:
                                res.Passed = achievedValue <= matchNumValue;
                                res.KPIId = kpiId;
                                res.AchValue = achievedValue.ToString();
                                break;

                            case ComparisonOperator.GreaterThanOrEqualTo:
                                res.Passed = achievedValue >= matchNumValue;
                                res.KPIId = kpiId;
                                res.AchValue = achievedValue.ToString();
                                break;

                            case ComparisonOperator.GreaterThan:
                                res.Passed = achievedValue > matchNumValue;
                                res.KPIId = kpiId;
                                res.AchValue = achievedValue.ToString();
                                break;

                            case ComparisonOperator.NotEqualTo:
                                res.Passed = achievedValue != matchNumValue;
                                res.KPIId = kpiId;
                                res.AchValue = achievedValue.ToString();
                                break;
                        }

                        break;

                    case KpiMeasure.Time:
                        DateTime.TryParse(matchValue, out DateTime matchTimeValue);
                        DateTime achievedValueTime = new DateTime();
                        switch (kpi.Relation)
                        {
                            case Relation.UseDirectReportDb:
                                achievedValueTime = DateTime.Parse(reportAchValue);
                                break;

                            case Relation.UseDirectMasterDb:
                                achievedValueTime = DateTime.Parse(masterAchvalue);
                                break;

                            case Relation.UseDirectTransactionDb:
                                achievedValueTime = DateTime.Parse(transactionAchValue);
                                break;
                        }

                        switch (comparisonOperator)
                        {
                            case ComparisonOperator.EqualTo:
                                res.Passed = achievedValueTime == matchTimeValue;
                                res.KPIId = kpiId;
                                res.AchValue = achievedValueTime.ToString("HH:mm");
                                break;

                            case ComparisonOperator.LessThan:
                                res.Passed = achievedValueTime < matchTimeValue;
                                res.KPIId = kpiId;
                                res.AchValue = achievedValueTime.ToString("HH:mm");
                                break;

                            case ComparisonOperator.LessThanOrEqualTo:
                                res.Passed = achievedValueTime <= matchTimeValue;
                                res.KPIId = kpiId;
                                res.AchValue = achievedValueTime.ToString("HH:mm");
                                break;

                            case ComparisonOperator.GreaterThanOrEqualTo:
                                res.Passed = achievedValueTime >= matchTimeValue;
                                res.KPIId = kpiId;
                                res.AchValue = achievedValueTime.ToString("HH:mm");
                                break;

                            case ComparisonOperator.GreaterThan:
                                res.Passed = achievedValueTime > matchTimeValue;
                                res.KPIId = kpiId;
                                res.AchValue = achievedValueTime.ToString("HH:mm");
                                break;

                            case ComparisonOperator.NotEqualTo:
                                res.Passed = achievedValueTime != matchTimeValue;
                                res.KPIId = kpiId;
                                res.AchValue = achievedValueTime.ToString("HH:mm");
                                break;
                        }

                        break;

                    case KpiMeasure.YesNo:
                        bool achievedValueBool = false;
                        Boolean.TryParse(matchValue, out var matchValueBool);
                        switch (kpi.Relation)
                        {
                            case Relation.UseDirectReportDb:
                                achievedValueBool = Convert.ToBoolean(reportAchValue);
                                break;

                            case Relation.UseDirectMasterDb:
                                achievedValueBool = Convert.ToBoolean(masterAchvalue);
                                break;

                            case Relation.UseDirectTransactionDb:
                                achievedValueBool = Convert.ToBoolean(transactionAchValue);
                                break;
                        }

                        switch (comparisonOperator)
                        {
                            case ComparisonOperator.EqualTo:
                                res.Passed = achievedValueBool == matchValueBool;
                                res.KPIId = kpiId;
                                res.AchValue = achievedValueBool.ToString();
                                break;

                            case ComparisonOperator.NotEqualTo:
                                res.Passed = achievedValueBool != matchValueBool;
                                res.KPIId = kpiId;
                                res.AchValue = achievedValueBool.ToString();
                                break;
                        }

                        break;

                    case KpiMeasure.TimeDuration:
                        TimeSpan.TryParse(matchValue, out TimeSpan matchTimeSpanValue);
                        TimeSpan achievedValueTimeSpan = new TimeSpan(0, 0, 0);
                        switch (kpi.Relation)
                        {
                            case Relation.UseDirectReportDb:
                                achievedValueTimeSpan = TimeSpan.Parse(reportAchValue);
                                break;

                            case Relation.UseDirectMasterDb:
                                achievedValueTimeSpan = TimeSpan.Parse(masterAchvalue);
                                break;

                            case Relation.UseDirectTransactionDb:
                                achievedValueTimeSpan = TimeSpan.Parse(transactionAchValue);
                                break;
                        }

                        switch (comparisonOperator)
                        {
                            case ComparisonOperator.EqualTo:
                                res.Passed = achievedValueTimeSpan == matchTimeSpanValue;
                                res.KPIId = kpiId;
                                res.AchValue = achievedValueTimeSpan.ToString(@"HH\:mm");
                                break;

                            case ComparisonOperator.LessThan:
                                res.Passed = achievedValueTimeSpan < matchTimeSpanValue;
                                res.KPIId = kpiId;
                                res.AchValue = achievedValueTimeSpan.ToString(@"HH\:mm");
                                break;

                            case ComparisonOperator.LessThanOrEqualTo:
                                res.Passed = achievedValueTimeSpan <= matchTimeSpanValue;
                                res.KPIId = kpiId;
                                res.AchValue = achievedValueTimeSpan.ToString(@"HH\:mm");
                                break;

                            case ComparisonOperator.GreaterThanOrEqualTo:
                                res.Passed = achievedValueTimeSpan >= matchTimeSpanValue;
                                res.KPIId = kpiId;
                                res.AchValue = achievedValueTimeSpan.ToString(@"HH\:mm");
                                break;

                            case ComparisonOperator.GreaterThan:
                                res.Passed = achievedValueTimeSpan > matchTimeSpanValue;
                                res.KPIId = kpiId;
                                res.AchValue = achievedValueTimeSpan.ToString(@"HH\:mm");
                                break;

                            case ComparisonOperator.NotEqualTo:
                                res.Passed = achievedValueTimeSpan != matchTimeSpanValue;
                                res.KPIId = kpiId;
                                res.AchValue = achievedValueTimeSpan.ToString(@"HH\:mm");
                                break;
                        }

                        break;

                    case KpiMeasure.ValueAPI:
                        string achievedValueString = string.Empty;
                        switch (kpi.Relation)
                        {
                            case Relation.UseDirectReportDb:
                                achievedValueString = reportAchValue;
                                break;

                            case Relation.UseDirectMasterDb:
                                achievedValueString = masterAchvalue;
                                break;

                            case Relation.UseDirectTransactionDb:
                                achievedValueString = transactionAchValue;
                                break;
                        }

                        switch (comparisonOperator)
                        {
                            case ComparisonOperator.EqualTo:
                                res.Passed = achievedValueString == matchValue;
                                res.KPIId = kpiId;
                                res.AchValue = achievedValueString.ToString();
                                break;

                            case ComparisonOperator.NotEqualTo:
                                res.Passed = achievedValueString != matchValue;
                                res.KPIId = kpiId;
                                res.AchValue = achievedValueString.ToString();
                                break;
                        }

                        break;
                }

                return res;
            }

            return new KPICheckModel();
        }

        public async Task<List<EntityMin>> GetKPIsForNotification(long notificationId, long companyId)
        {
            var kpiIds = (await GetKPITriggers(notificationId, companyId)).Select(s => s.KpiId).ToList();

            return (await kpiRepository.GetKpiListByIds(kpiIds, companyId)).Select(s => new EntityMin
            {
                Id = s.Id,
                Name = s.Name
            }).ToList();
        }

        public async Task<List<FAEngageLog>> GetLastLogs(long notificationId, long companyId)
        {
            var companySettingsDict = await companySettingsRepository.GetSettings(companyId);
            var companySettings = new CompanySettings(companySettingsDict);

            var aggregationDelay = ((await GetNotification(notificationId, companyId)).AggregationDelay ?? 30) + 15;
            var startTime = DateTimeOffset.UtcNow.ToOffset(companySettings.TimeZoneOffset).AddMinutes(-aggregationDelay);
            var endTime = DateTimeOffset.UtcNow.ToOffset(companySettings.TimeZoneOffset);

            var lastTransactionId = await faEngageTransactionRepository.GetLastTransaction(notificationId, companyId, startTime, endTime);
            if (lastTransactionId == Guid.Empty)
            {
                return new List<FAEngageLog>();
            }

            return await faEngageTransactionRepository.GetLogsForTransactionId(companyId, lastTransactionId);
        }

        public async Task<NotificationMessage> GetNotificationMessage(long notificationId, long companyId)
        {
            return await fAEngageRepository.GetNotificationMessage(notificationId, companyId);
        }

        public async Task<NotificationMessage> GetNotificationAggregationMessage(long notificationId, long companyId)
        {
            return await fAEngageRepository.GetAggregatedNotificationMessage(notificationId, companyId);
        }

        public async Task AddLogToDB(long id, long companyId, long userId, Guid transactionId,
            string aggregationHierarchyJson, bool isnotificationSentfrombackend, string notificationSentMessage, Guid sendId)
        {
            var companySettingsDict = await companySettingsRepository.GetSettings(companyId);
            var companySettings = new CompanySettings(companySettingsDict);
            var log = new FAEngageLog()
            {
                CompanyId = companyId,
                CreatedAt = DateTime.UtcNow,
                IsReadByUser = false,
                NotificationId = id,
                NotificationTransactionId = transactionId,
                SendTime = new DateTimeOffset(DateTime.UtcNow).ToOffset(companySettings.TimeZoneOffset),
                UserId = userId,
                AggregationHierarchyJson = aggregationHierarchyJson,
                IsSentFromBackend = isnotificationSentfrombackend,
                SentMessage = notificationSentMessage,
                SentId = sendId
            };
            await faEngageTransactionRepository.AddEngageLogToDb(log);
        }

        public async Task AddWhatsAppLogToDB(long id, long companyId, long userId, Guid transactionId,
            string aggregationHierarchyJson, bool isnotificationSentfrombackend, string notificationSentMessage, Guid sendId)
        {
            var companySettingsDict = await companySettingsRepository.GetSettings(companyId);
            var companySettings = new CompanySettings(companySettingsDict);
            var log = new FAEngageLogWhatsapp()
            {
                CompanyId = companyId,
                CreatedAt = DateTime.UtcNow,
                IsReadByUser = false,
                NotificationId = id,
                NotificationTransactionId = transactionId,
                SendTime = new DateTimeOffset(DateTime.UtcNow).ToOffset(companySettings.TimeZoneOffset),
                UserId = userId,
                IsSentFromBackend = isnotificationSentfrombackend,
                SentMessage = notificationSentMessage,
                SentId = sendId,
                AggregationHierarchyJson = aggregationHierarchyJson,
            };
            await faEngageTransactionRepository.AddWhatsAppEngageLogToDb(log);
        }

        public async Task AddAnalyticsLogToDB(long id, long companyId, long userId,
            bool isnotificationSentfrombackend, string notificationSentMessage, Guid sendId, Guid transactionId, long? alertId, List<long?> positionCodeIds, bool isNotAggregated = false)
        {
            var companySettingsDict = await companySettingsRepository.GetSettings(companyId);
            var companySettings = new CompanySettings(companySettingsDict);
            var log = new FAManagerEngageLog()
            {
                CompanyId = companyId,
                CreatedAt = DateTime.UtcNow,
                IsReadByUser = false,
                NotificationId = id,
                NotificationTransactionId = transactionId,
                SendTime = new DateTimeOffset(DateTime.UtcNow).ToOffset(companySettings.TimeZoneOffset),
                UserId = userId,
                IsSentFromBackend = isnotificationSentfrombackend,
                SentMessage = notificationSentMessage,
                SentId = sendId,
                AlertId = alertId,
                PositionCodeIds = JsonSerializer.Serialize(positionCodeIds),
                IsNotAggregated = isNotAggregated
            };
            await faEngageTransactionRepository.AddAnalyticsEngageLogToDb(log);
        }

        public async Task UpdateEngageLogsMessage(Guid sendId, Guid notificationTransactionId, string message, int statusCode)
        {
            await faEngageTransactionRepository.UpdateSendMessageForEngageLogs(sendId, notificationTransactionId, message, statusCode);
        }
    }
}