﻿using FAEngage.Core.Interfaces;
using FAEngage.Core.MasterRepositories;
using FAEngage.Core.ReportRepositories;
using Libraries.CommonEnums;

namespace FAEngage.Core.Services;

public class FAEngageReportService(
    IKpiRepository kpiRepository,
    IKpiAchievementRepository kpiAchievementRepository)
    : IFAEngageReportService
{
    public async Task<string> GetKPIReportAchievement(long companyId, long esmId, PortalUserRole? userRole, long kpiId, 
        DateTime startDate, DateTime endDate)
    {
        var kpi = await kpiRepository.GetKpiById(kpiId, companyId);

        return await kpiAchievementRepository.GetKPIAchievedTarget(kpi.SQLQuery, companyId, esmId, userRole, startDate,
            endDate);
    }
}