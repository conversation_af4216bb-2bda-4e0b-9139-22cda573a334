﻿using System.Net.Http;
using System.Threading.Tasks;

namespace Library.CommonHelpers.HttpClients;

public abstract class BaseHttpClient
{
    protected readonly HttpClient Client;

    protected BaseHttpClient(HttpClient httpClient)
    {
        Client = httpClient;
    }

    public virtual Task<HttpResponseMessage> GetAsync(string endpoint)
    {
        return Client.GetAsync(endpoint);
    }

    public virtual Task<HttpResponseMessage> PostAsync(string endpoint, ByteArrayContent byteContent)
    {
        return Client.PostAsync(endpoint, byteContent);
    }
}
