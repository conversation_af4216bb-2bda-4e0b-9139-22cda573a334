﻿using System.Data.SqlClient;
using System.Text.RegularExpressions;
using FAEngage.Core.ReportRepositories;
using FAEngage.DbStorage.DbContexts;
using Libraries.CommonEnums;

namespace FAEngage.DbStorage.ReportRepositories;

public class KpiAchievementRepository(ReportDbSqlDataReader db) : IKpiAchievementRepository
{
    public async Task<string> GetKPIAchievedTarget(string query, long companyId, long esmId, PortalUserRole? userRole, DateTime startDate,
        DateTime endDate)
    {
        if (string.IsNullOrWhiteSpace(query)) return null;

        var parameters = new List<SqlParameter>();
        var paramsInQuery = Regex.Matches(query, @"@\b\S+?\b");

        foreach (Match p in paramsInQuery)
        {
            string value = null;
            switch (p.Value.ToLower())
            {
                case "@companyid":
                    value = companyId.ToString();
                    break;
                case "@esmid":
                    value = esmId.ToString();
                    break;
                case "@startdatekey":
                    value = startDate.ToString("yyyyMMdd");
                    break;
                case "@enddatekey":
                    value = endDate.ToString("yyyyMMdd");
                    break;
                case "@managerid":
                    if (userRole != null)
                    {
                        if (userRole == PortalUserRole.ClientEmployee)
                        {
                            query = query.Replace("[[managerid]]", "EsmId");
                        }
                        else if (userRole == PortalUserRole.AreaSalesManager)
                        {
                            query = query.Replace("[[managerid]]", "ASMUserId");
                        }
                        else if (userRole == PortalUserRole.RegionalSalesManager)
                        {
                            query = query.Replace("[[managerid]]", "RSMUserId");
                        }
                        else if (userRole == PortalUserRole.ZonalSalesManager)
                        {
                            query = query.Replace("[[managerid]]", "ZSMUserId");
                        }
                        else if (userRole == PortalUserRole.NationalSalesManager)
                        {
                            query = query.Replace("[[managerid]]", "NSMUserId");
                        }
                        else if (userRole == PortalUserRole.GlobalSalesManager)
                        {
                            query = query.Replace("[[managerid]]", "GSMUserId");
                        }
                        value = esmId.ToString();
                    }
                    break;

            }

            if (value != null) parameters.Add(new SqlParameter(p.Value.ToLower(), value));
        }

        return await db.GetSingleResultOfQuery(query, parameters);
    }
}