﻿using Library.SqlHelper;

namespace FAEngage.DbStorage.DbContexts;

public class ReportDbSqlDataReader : SqlDataReader
{
    public ReportDbSqlDataReader(string connectionString) : base(connectionString)
    {
    }

    public ReportDbSqlDataReader(string connectionString, int timeout) : base(connectionString, timeout)
    {
    }
}

public class QVReportDbSqlDataReader : SqlDataReader
{
    public QVReportDbSqlDataReader(string connectionString) : base(connectionString)
    {
    }

    public QVReportDbSqlDataReader(string connectionString, int timeout) : base(connectionString, timeout)
    {
    }
}

public class L3MQVReportDbSqlDataReader : SqlDataReader
{
    public L3MQVReportDbSqlDataReader(string connectionString) : base(connectionString)
    {
    }

    public L3MQVReportDbSqlDataReader(string connectionString, int timeout) : base(connectionString, timeout)
    {
    }
}