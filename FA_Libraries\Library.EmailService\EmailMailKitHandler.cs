﻿using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Library.EmailService.Interface;
using Library.EmailService.Model;
using Microsoft.Extensions.Options;
using MimeKit;
using SmtpClient = MailKit.Net.Smtp.SmtpClient;

namespace Library.EmailService;

public class EmailMailKitHandler : IEmailHandler
{
    private readonly SmtpOptions _options;

    public EmailMailKitHandler(IOptions<SmtpOptions> options)
    {
        _options = options.Value;
    }

    public async Task<string> SendEmailAsync(EmailMessage emailMessage, CancellationToken ct = default)
    {
        using var combined = CancellationTokenSource.CreateLinkedTokenSource(ct);
        using var smtpClient = new SmtpClient();
        // Enforce a hard timeout from the configuration.
        combined.CancelAfter(_options.Timeout);

        await EnsureConnectedAsync(smtpClient, combined.Token).ConfigureAwait(false);

        var smtpMessage = new MimeMessage
        {
            From = { new MailboxAddress(emailMessage.FromName, emailMessage.FromEmail) },
            Subject = emailMessage.Subject,
            Body = new BodyBuilder { HtmlBody = emailMessage.Message }.ToMessageBody(),
            Headers = { { CustomHeaderKey, CustomHeaderStringValue } }
        };
        AddAddresses(emailMessage.To, smtpMessage.To);
        AddAddresses(emailMessage.Cc, smtpMessage.Cc);
        AddAddresses(emailMessage.Bcc, smtpMessage.Bcc);
        var response = await smtpClient.SendAsync(smtpMessage, ct).ConfigureAwait(false);
        //await smtpClient.DisconnectAsync(quit: true, ct);
        return response;
    }

    #region private members

    //find full details of this header here: https://developers.sparkpost.com/api/smtp/
    private const string CustomHeaderKey = "X-MSYS-API";

    private static readonly object CustomHeaderValue = new { options = new { sandbox = false, transactional = true } };

    private static string CustomHeaderStringValue => JsonSerializer.Serialize(CustomHeaderValue);

    private async Task EnsureConnectedAsync(SmtpClient smtpClient,
        CancellationToken ct)
    {
        if (!smtpClient.IsConnected)
        {
            smtpClient.AuthenticationMechanisms.Remove("XOAUTH2");
            await smtpClient.ConnectAsync(_options.Server, _options.Port, _options.EnableSsl, ct).ConfigureAwait(false);
        }

        if (!smtpClient.IsAuthenticated)
        {
            await smtpClient.AuthenticateAsync(_options.Username, _options.Password, ct).ConfigureAwait(false);
        }
    }

    private static void AddAddresses(string addresses, InternetAddressList list)
    {
        list.AddRange(addresses.Split(",;").Select(s => new MailboxAddress("", s)));
    }

    #endregion private members
}
