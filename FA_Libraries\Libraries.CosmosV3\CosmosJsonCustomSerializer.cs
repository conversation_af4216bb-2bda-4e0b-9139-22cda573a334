﻿using System.IO;
using System.Text;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;

namespace Libraries.CosmosV3;

public class CosmosJsonCustomSerializer : CosmosSerializer
{
    private static readonly Encoding DefaultEncoding = new UTF8Encoding(false, true);
    private readonly JsonSerializer Serializer;
    private readonly JsonSerializerSettings serializerSettings;

    public CosmosJsonCustomSerializer()
        : this(new JsonSerializerSettings())
    {
    }

    public CosmosJsonCustomSerializer(
        JsonSerializerSettings serializerSettings
    )
    {
        this.serializerSettings = serializerSettings;
        Serializer = JsonSerializer.Create(this.serializerSettings);
    }

    public override T FromStream<T>(Stream stream)
    {
        using (stream)
        {
            if (typeof(Stream).IsAssignableFrom(typeof(T)))
            {
                return (T)(object)stream;
            }

            using (var sr = new StreamReader(stream))
            {
                using (var jsonTextReader = new JsonTextReader(sr))
                {
                    return Serializer.Deserialize<T>(jsonTextReader);
                }
            }
        }
    }

    public override Stream ToStream<T>(T input)
    {
        var streamPayload = new MemoryStream();
        using (var streamWriter = new StreamWriter(streamPayload, DefaultEncoding, 1024, true))
        {
            using (JsonWriter writer = new JsonTextWriter(streamWriter))
            {
                writer.Formatting = Formatting.None;
                Serializer.Serialize(writer, input);
                writer.Flush();
                streamWriter.Flush();
            }
        }

        streamPayload.Position = 0;
        return streamPayload;
    }
}
