﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Flurl" Version="4.0.0" />
        <PackageReference Include="Flurl.Http" Version="4.0.2" />
        <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="9.0.7" />
    </ItemGroup>
    <ItemGroup>
      <ProjectReference Include="..\FA_Libraries\Libraries.CommonEnums\Libraries.CommonEnums.csproj" />
      <ProjectReference Include="..\FA_Libraries\Libraries.CommonModels\Libraries.CommonModels.csproj" />
      <ProjectReference Include="..\FA_Libraries\Library.DateTimeHelpers\Library.DateTimeHelpers.csproj" />
      <ProjectReference Include="..\FA_Libraries\Library.NumberSystem\Library.NumberSystem.csproj" />
      <ProjectReference Include="..\FA_Libraries\Library.SlackService\Library.SlackService.csproj" />
    </ItemGroup>
    <ItemGroup>
      <Folder Include="Models\ReportDbModels\" />
      <Folder Include="Services\ReportServices\" />
    </ItemGroup>
</Project>
