﻿using System;
using System.Collections.Generic;
using System.Text.Json;
using Libraries.CommonEnums;

namespace Library.Infrastructure.Interface;

public class IPushNotificationMessage : IQueueMessage
{
}

public class PushNotificationMessage : IPushNotificationMessage
{
    public List<string> GcmIds { get; set; }
    public UserMessages data { get; set; }
    public ApiMessage Message { get; set; }
    public bool RemoveNotificationParameter { get; set; }
    public AlertSource AlertSource { get; set; }
}

public class ApiMessage
{
    public ApiMessage()
    {
    }

    public ApiMessage(UserMessages userMessage)
    {
        message = userMessage.message;
        text = userMessage.message;
        title = userMessage.title;
        fullMessage = JsonSerializer.Serialize(userMessage);
    }

    public string message { set; get; }
    public string text { set; get; }
    public string title { set; get; }
    public string fullMessage { get; set; }
    public string metaData { get; set; }
    public string orderId { get; set; }
    public string imageUrl { get; set; }
    public string ctaReturnModel { get; set; }
    public string transactionId { get; set; }
    public NotificationType NotificationType { get; set; } = NotificationType.Default;
    public Guid Id { get; set; }
    public Guid TransactionId { get; set; }
    public long? AlertId { get; set; }
    public int Placement { get; set; }
    public int? InAppScreen { get; set; }
    public string ActionLink { get; set; }
    public int? ActionScreen { get; set; }
    public string Emoji { get; set; }
}

public class EngageMetaData
{
    public Guid id { get; set; }
    public Guid transactionId { get; set; }
    public long? alertId { get; set; }
    public string title { get; set; }
    public string message { get; set; }
    public AppNotificationType placement { get; set; }
    public int? inAppScreen { get; set; }
    public string actionLink { get; set; }
    public int? actionScreen { get; set; }
    public string emoji { get; set; }
}

public class UserMessages
{
    public long chatThreadId { set; get; }
    public string chatThreadName { set; get; }
    public string chatThreadDescription { set; get; }
    public string fromName { set; get; }
    public string fromRole { set; get; }
    public long fromId { set; get; }
    public string participantName { set; get; }
    public string participantRole { set; get; }
    public NotificationCategory notification_type { get; set; }
    public long participantId { set; get; }
    public long dateTime { set; get; }
    public string message { set; get; }
    public string title { set; get; }
    public string messageUniqueId { set; get; }
    public bool isApproved { get; set; }
    public long entityId { get; set; }
    public long outletId { get; set; }
}


public class CTAReturnModel
{
    public string ctaName { get; set; }

    public CtaType ctaType { get; set; }

    public string ctaAction { get; set; }
}

public enum NotificationType
{
    Default,
    OTP,
    EndDay,
    SuggestedOrder,
    Engage = 4,
    EFRIS = 5
}

public enum NotificationCategory
{
    Default,
    StockTransfer,
    CampaignCompletion,
    POAction,
    POActionRejected
}
