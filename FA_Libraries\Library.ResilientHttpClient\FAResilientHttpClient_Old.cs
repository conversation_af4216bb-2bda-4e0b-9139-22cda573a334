﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;

namespace Library.ResilientHttpClient;

public class FAResilientHttpClient_Old
{
    private static readonly IEnumerable<HttpStatusCode> defaultStatusCodesToRetry =
        new List<HttpStatusCode> { HttpStatusCode.GatewayTimeout, HttpStatusCode.InternalServerError, HttpStatusCode.RequestTimeout };

    private readonly HttpClient httpClient;
    private readonly IEnumerable<HttpStatusCode> httpCodesToRetry;
    private readonly double maxWaitDurationSeconds = 30;
    private readonly int noOfRetries = 3;

    public FAResilientHttpClient_Old(int noOfRetries = 3, TimeSpan? maxWaitDuration = null)
        : this(defaultStatusCodesToRetry, noOfRetries, maxWaitDuration)
    {
    }

    public FAResilientHttpClient_Old(IEnumerable<HttpStatusCode> httpCodesToRetry, int noOfRetries = 3,
        TimeSpan? maxWaitDuration = null)
    {
        httpClient = new HttpClient();
        this.httpCodesToRetry = httpCodesToRetry;
        this.noOfRetries = noOfRetries <= 0 ? 1 : noOfRetries;
        maxWaitDurationSeconds = maxWaitDuration?.TotalSeconds ?? 30;
    }

    public HttpRequestHeaders DefaultRequestHeaders => httpClient.DefaultRequestHeaders;

    private async Task DelayNextAttempt(int attemptNo)
    {
        await Task.Delay(TimeSpan.FromSeconds((attemptNo + 1) * (maxWaitDurationSeconds / noOfRetries))).ConfigureAwait(false);
    }

    private async Task<HttpResponseMessage> GetAsync(string requestUri, int retryCount)
    {
        HttpResponseMessage response;
        try
        {
            response = await httpClient.GetAsync(requestUri).ConfigureAwait(false);
            if (httpCodesToRetry.Contains(response.StatusCode))
            {
                if (retryCount < noOfRetries)
                {
                    await DelayNextAttempt(retryCount).ConfigureAwait(false);
                    return await GetAsync(requestUri, ++retryCount).ConfigureAwait(false);
                }
            }
        }
        catch (Exception ex)
        {
            if (retryCount < noOfRetries)
            {
                await DelayNextAttempt(retryCount).ConfigureAwait(false);
                return await GetAsync(requestUri, ++retryCount).ConfigureAwait(false);
            }

            throw new Exception($"Failed While Accessing: {requestUri}", ex);
        }

        return response;
    }

    private async Task<HttpResponseMessage> PostAsync(string requestUri, HttpContent httpContent, int retryCount)
    {
        HttpResponseMessage response;
        try
        {
            response = await httpClient.PostAsync(requestUri, httpContent).ConfigureAwait(false);
            if (httpCodesToRetry.Contains(response.StatusCode))
            {
                if (retryCount < noOfRetries)
                {
                    await DelayNextAttempt(retryCount).ConfigureAwait(false);
                    return await PostAsync(requestUri, httpContent, ++retryCount).ConfigureAwait(false);
                }
            }
        }
        catch (Exception)
        {
            if (retryCount < noOfRetries)
            {
                await DelayNextAttempt(retryCount).ConfigureAwait(false);
                return await PostAsync(requestUri, httpContent, ++retryCount).ConfigureAwait(false);
            }

            throw;
        }

        return response;
    }

    public async Task<HttpResponseMessage> GetAsync(string requestUri)
    {
        return await GetAsync(requestUri, 0).ConfigureAwait(false);
    }

    public async Task<HttpResponseMessage> PostAsync(string requestUri, HttpContent httpContent)
    {
        return await PostAsync(requestUri, httpContent, 0).ConfigureAwait(false);
    }

    public async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request)
    {
        return await httpClient.SendAsync(request).ConfigureAwait(false);
    }

    public async Task<HttpResponseMessage> GetAsyncOCR(string requestUri)
    {
        HttpResponseMessage response = null;
        ;
        try
        {
            httpClient.DefaultRequestHeaders.Accept.Clear();
            httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            httpClient.DefaultRequestHeaders.Add("Ocp-Apim-Subscription-Key", "3ca927792f2449939abca19642f14c56");
            response = await httpClient.GetAsync(requestUri).ConfigureAwait(false);
        }
        catch (Exception)
        {
        }

        return response;
    }
}
