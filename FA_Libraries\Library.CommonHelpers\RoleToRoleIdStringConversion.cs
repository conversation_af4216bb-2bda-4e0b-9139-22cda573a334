﻿using Libraries.CommonEnums;

namespace Library.CommonHelpers;

public class RoleToRoleIdStringConversion
{
    public static string GetNewIdStringFromRole(PortalUserRole role)
    {
        switch (role)
        {
            case PortalUserRole.ClientEmployee:
                return "ESMId";

            case PortalUserRole.AreaSalesManager:
                return "ASMUserId";

            case PortalUserRole.RegionalSalesManager:
                return "RSMUserId";

            case PortalUserRole.ZonalSalesManager:
                return "ZSMUserId";

            case PortalUserRole.NationalSalesManager:
                return "NSMUserId";

            case PortalUserRole.GlobalSalesManager:
                return "GSMUserId";

            default:
                return "ESMId";
        }
    }

    public static string GetOldIdStringFromRole(PortalUserRole role)
    {
        switch (role)
        {
            case PortalUserRole.ClientEmployee:
                return "ESMId";

            case PortalUserRole.AreaSalesManager:
                return "ASMId";

            case PortalUserRole.RegionalSalesManager:
                return "RSMId";

            case PortalUserRole.ZonalSalesManager:
                return "ZSMId";

            case PortalUserRole.NationalSalesManager:
                return "NSMId";

            case PortalUserRole.GlobalSalesManager:
                return "GSMId";

            default:
                return "ESMId";
        }
    }

    public static string GetIdStringFromPositionLevel(PositionCodeLevel role)
    {
        switch (role)
        {
            case PositionCodeLevel.L1Position:
                return "PositionLevel1UserId";
            case PositionCodeLevel.L2Position:
                return "PositionLevel2UserId";
            case PositionCodeLevel.L3Position:
                return "PositionLevel3UserId";
            case PositionCodeLevel.L4Position:
                return "PositionLevel4UserId";
            case PositionCodeLevel.L5Position:
                return "PositionLevel5UserId";
            case PositionCodeLevel.L6Position:
                return "PositionLevel6UserId";
            case PositionCodeLevel.L7Position:
                return "PositionLevel7UserId";
            case PositionCodeLevel.L8Position:
                return "PositionLevel8UserId";
            default:
                return "ESMId";
        }
    }

    public static string GetPosIdStringFromPositionLevel(PositionCodeLevel positionCodeLevel)
    {
        switch (positionCodeLevel)
        {
            case PositionCodeLevel.L1Position:
                return "PositionLevel1";
            case PositionCodeLevel.L2Position:
                return "PositionLevel2";
            case PositionCodeLevel.L3Position:
                return "PositionLevel3";
            case PositionCodeLevel.L4Position:
                return "PositionLevel4";
            case PositionCodeLevel.L5Position:
                return "PositionLevel5";
            case PositionCodeLevel.L6Position:
                return "PositionLevel6";
            case PositionCodeLevel.L7Position:
                return "PositionLevel7";
            case PositionCodeLevel.L8Position:
                return "PositionLevel8";
            default:
                return "PositionLevel1";
        }
    }

    public static string GetUnifyPosIdStringFromPositionLevel(PositionCodeLevel positionCodeLevel)
    {
        switch (positionCodeLevel)
        {
            case PositionCodeLevel.L1Position:
                return "Level1PositionId";
            case PositionCodeLevel.L2Position:
                return "Level2PositionId";
            case PositionCodeLevel.L3Position:
                return "Level3PositionId";
            case PositionCodeLevel.L4Position:
                return "Level4PositionId";
            case PositionCodeLevel.L5Position:
                return "Level5PositionId";
            case PositionCodeLevel.L6Position:
                return "Level6PositionId";
            case PositionCodeLevel.L7Position:
                return "Level7PositionId";
            case PositionCodeLevel.L8Position:
                return "Level8PositionId";
            default:
                return "Level1PositionId";
        }
    }
}
