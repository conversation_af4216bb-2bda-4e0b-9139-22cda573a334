﻿using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Library.Infrastructure.Models;

namespace Library.Infrastructure.QueueService;

public class QueueHandler<T>
{
    private readonly string _connectionString;
    private readonly QueueSubscription _queueSubscription;

    public QueueHandler(QueueType subscription, string connectionString)
    {
        _queueSubscription = new QueueSubscription(subscription);
        _connectionString = connectionString;
    }

    public async Task AddToGridQueue(string id, T data, string eventType = "Generic",
        bool forceResyncData = false, string queueSource = "Unknown")
    {
        var queueAction = new QueueActions(_queueSubscription, _connectionString);

        var queueEvent = new GridEvent<T>(data, id: id,
            eventTime: DateTime.UtcNow,
            eventType: eventType,
            subject: _queueSubscription.Queuestr, source: queueSource, forceResyncData: forceResyncData);

        // Serialize the queueEvent to a string
        var serializedData = JsonSerializer.Serialize(queueEvent);
        // Call the AddToQueue Generic method
        await queueAction.AddToQueue(serializedData).ConfigureAwait(false);
    }

    public async Task AddToQueue(T data, TimeSpan? visibilityTimeout = null, TimeSpan? timeToLive = null, CancellationToken cancellationToken = default)
    {
        var queueAction = new QueueActions(_queueSubscription, _connectionString);
        // Serialize the queueEvent to a string
        var serializedData = JsonSerializer.Serialize(data);
        // Call the AddToQueue Generic method
        await queueAction.AddToQueue(serializedData, visibilityTimeout, timeToLive, cancellationToken).ConfigureAwait(false);
    }
}
