﻿using FAEngage.Core.MasterRepositories;
using FAEngage.Core.Models;
using FAEngage.Core.Models.MasterDbModels;
using FAEngage.DbStorage.DbContexts;
using Microsoft.EntityFrameworkCore;

namespace FAEngage.DbStorage.MasterRepositories
{
    public class LocationRepository(MasterDbContext masterDbContext) : ILocationRepository
    {
        public async Task<List<LocationDto>> GetLocationsForCompany(long companyId)
        {
            var data = await masterDbContext.Locations
                .Where(s => s.CompanyId == companyId && !s.IsBlocked)
                .Select(l => new LocationDto
                {
                    BeatId = l.BeatId,
                    City = l.City,
                    CompanyId = l.CompanyId,
                    CompanySegmentation = l.CompanySegmentation,
                    Country = l.Country,
                    CustomTags = l.CustomTags,
                    District = l.District,
                    ErpId = l.ErpId,
                    Id = l.Id,
                    IsBlocked = l.IsBlocked,
                    IsFocused = l.IsFocused,
                    Latitude = l.Latitude,
                    Longitude = l.Longitude,
                    MarketName = l.MarketName,
                    ShopName = l.ShopName,
                    Segmentation = l.Segmentation,
                    ShopTypeCode = l.ShopTypeCode,
                    ShopTypeId = l.ShopTypeId,
                    State = l.State,
                    SubCity = l.SubCity,
                    OutletChannel = l.OutletChannel,
                    TerritoryId = l.Beat.TerritoryId,
                    RegionId = l.Beat.Territory.RegionId,
                    ZoneId = l.Beat.Territory.TheRegion.ZoneId,
                    OwnersName = l.OwnersName,
                    OwnersNo = l.OwnersNo
                })
                .ToListAsync();

            return data;
        }

        public async Task<List<TheShopType>> GetShopTypes(long companyId)
        {
            return await masterDbContext.ShopTypes
                .Where(s => s.CompanyId == companyId && s.IsInvalid)
                .ToListAsync();
        }

        public async Task<List<FATradeUser>> GetFATradeUserForOwnersNumber(List<string> ownerNos)
        {
            return await masterDbContext.FATradeUsers
                .Where(s => ownerNos.Contains(s.PhoneNo))
                .ToListAsync();
        }

        public async Task<List<FATradeToken>> GetFATradeTokens(List<long> userIds, long companyId)
        {
            return await masterDbContext.FATradeTokens
                .Where(s => s.CompanyId == companyId && userIds.Contains(s.UserId))
                .ToListAsync();
        }
    }
}