﻿using System.Collections.Generic;
using Libraries.CommonEnums;

namespace Library.CommonHelpers;

public static class DayStartTypeHelper
{
    public static bool IsDayStartOfTypeOther(this DayStartType type)
    {
        switch (type)
        {
            case DayStartType.Other:
            case DayStartType.OfficialWork:
            case DayStartType.NonRetailingManagerWork:
            case DayStartType.ManagerWorking:
                return true;

            case DayStartType.Regular:
            case DayStartType.ManagerJointWorking:
            case DayStartType.Leave:
            case DayStartType.Holiday:
            case DayStartType.WeeklyOff:
            case DayStartType.None:
            default:
                return false;
        }
    }

    public static bool IsDayStartOfTypeLeave(this DayStartType type)
    {
        switch (type)
        {
            case DayStartType.Leave:
            case DayStartType.Holiday:
            case DayStartType.WeeklyOff:
                return true;

            case DayStartType.Regular:
            case DayStartType.ManagerJointWorking:
            case DayStartType.Other:
            case DayStartType.ManagerWorking:
            case DayStartType.OfficialWork:
            case DayStartType.NonRetailingManagerWork:
            case DayStartType.None:
            default:
                return false;
        }
    }

    public static bool IsDayStartOfTypeRegular(this DayStartType type)
    {
        switch (type)
        {
            case DayStartType.Regular:
            case DayStartType.ManagerJointWorking:
                return true;

            default:
                return false;
        }
    }

    public static bool IsDayStartOfTypeWorking(this DayStartType type)
    {
        switch (type)
        {
            case DayStartType.Leave:
            case DayStartType.Holiday:
            case DayStartType.WeeklyOff:
            case DayStartType.None:
                return false;

            case DayStartType.Regular:
            case DayStartType.ManagerJointWorking:
            case DayStartType.Other:
            case DayStartType.OfficialWork:
            case DayStartType.NonRetailingManagerWork:
            case DayStartType.ManagerWorking:
            default:
                return true;
        }
    }

    public static List<DayStartType> GetDayStartTypeList(this DayStartTypeCategory type)
    {
        var dstypeList = new List<DayStartType>();
        switch (type)
        {
            case DayStartTypeCategory.Retailing:
                dstypeList.Add(DayStartType.Regular);
                break;

            case DayStartTypeCategory.ManagerJW:
                dstypeList.Add(DayStartType.ManagerJointWorking);
                break;

            case DayStartTypeCategory.OfficialWork:
                dstypeList.Add(DayStartType.Other);
                dstypeList.Add(DayStartType.OfficialWork);
                dstypeList.Add(DayStartType.NonRetailingManagerWork);
                break;

            case DayStartTypeCategory.Leave:
                dstypeList.Add(DayStartType.Leave);
                break;

            case DayStartTypeCategory.Holiday:
                dstypeList.Add(DayStartType.Holiday);
                break;

            case DayStartTypeCategory.WeeklyOff:
                dstypeList.Add(DayStartType.WeeklyOff);
                break;

            case DayStartTypeCategory.Absent:
                dstypeList.Add(DayStartType.None);
                break;

            case DayStartTypeCategory.ManagerWorking:
                dstypeList.Add(DayStartType.ManagerWorking);
                break;
        }

        return dstypeList;
    }

    public static class DayStartReasonCategory
    {
        public static DayStartType GetDayStartType(string Type)
        {
            return Type == "Retailing" ? DayStartType.Regular :
                Type == "Leave" ? DayStartType.Leave :
                Type == "Holiday" ? DayStartType.Holiday :
                Type == "WeeklyOff" ? DayStartType.WeeklyOff :
                Type == "Absent" ? DayStartType.None :
                Type == "Absent" ? DayStartType.None :
                Type == "ManagerJointWorking" ? DayStartType.ManagerJointWorking :
                Type == "NonRetailingManagerWork" ? DayStartType.NonRetailingManagerWork :
                Type == "Other" ? DayStartType.Other :
                Type == "Weekly Off" ? DayStartType.WeeklyOff :
                Type == "Manager Working" ? DayStartType.ManagerWorking :
                DayStartType.OfficialWork;
        }
    }
}
