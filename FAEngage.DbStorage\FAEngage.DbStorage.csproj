﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <RootNamespace>FAEngage.DbStorage</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\FAEngage.Core\FAEngage.Core.csproj" />
      <ProjectReference Include="..\FA_Libraries\Libraries.CommonModels\Libraries.CommonModels.csproj" />
      <ProjectReference Include="..\FA_Libraries\Library.SqlHelper\Library.SqlHelper.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.7" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.7" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.7">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
    </ItemGroup>

</Project>
