﻿using FAEngage.Core.MasterRepositories;
using FAEngage.Core.Models.MasterDbModels;
using FAEngage.DbStorage.DbContexts;
using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore;

namespace FAEngage.DbStorage.MasterRepositories;

public class FAEngageRepository(MasterDbContext masterDbContext) : IFAEngageRepository
{
    public async Task<List<EngageNotification>> GetActiveUSerAppNotificationsForDate(DateTime date)
    {
        return await masterDbContext.FaEngageNotificationMasters
            .Where(n => !n.Deleted
                        && (n.UserPlatform == UserPlatform.UserApp || n.UserPlatform == UserPlatform.FATradeApp || n.UserPlatform == UserPlatform.AnalyticApp)
                        && n.StartDate <= date && n.EndDate >= date)
            .ToListAsync();
    }
    
    public async Task<Cohort> GetCohort(long cohort, long companyId)
    {
        return await masterDbContext.Cohorts
            .Where(c => c.Id == cohort && c.CompanyId == companyId)
            .FirstAsync();
    }

    public async Task<List<KPITriggers>> GetKpiTriggers(long notificationId, long companyId)
    {
        return await masterDbContext.FaEngageKPITriggers
            .Where(n => n.NotificationId == notificationId
                        && n.CompanyId == companyId)
            .ToListAsync();
    }

    public async Task<EngageNotification> GetNotification(long id, long companyId)
    {
        return await masterDbContext.FaEngageNotificationMasters
            .Where(n => n.CompanyId == companyId && n.Id == id)
            .FirstAsync();
    }

    public async Task<NotificationMessage> GetNotificationMessage(long notificationId, long companyId)
    {
        return await masterDbContext.FaEngageNotificationMessages
            .Where(n => n.NotificationId == notificationId 
                        && n.CompanyId == companyId && !n.IsAggregated)
            .FirstAsync();
    }

    public async Task<NotificationMessage> GetAggregatedNotificationMessage(long notificationId, long companyId)
    {
        return await masterDbContext.FaEngageNotificationMessages
            .Where(n => n.NotificationId == notificationId 
                        && n.CompanyId == companyId && n.IsAggregated)
            .FirstAsync();
    }
}