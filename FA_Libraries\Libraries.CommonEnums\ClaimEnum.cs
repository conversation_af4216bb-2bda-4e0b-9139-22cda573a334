﻿using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums;

public enum ClaimType
{
    [Display(Name = "custom")]
    custom,

    [Display(Name = "return_claim")]
    return_claim,

    [Display(Name = "scheme_claim")]
    scheme_claim,

    [Display(Name = "margin_difference_claim")]
    margin_difference_claim,

    [Display(Name = "master_claim")]
    master_claim
}

public enum EditingType
{
    [Display(Name = "Retailer Level")]
    retailer_level = 0,

    [Display(Name = "Invoice Level")]
    invoice_level = 1
}
