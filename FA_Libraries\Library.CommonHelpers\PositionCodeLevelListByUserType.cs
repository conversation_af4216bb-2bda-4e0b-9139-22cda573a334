﻿using System.Collections.Generic;
using Libraries.CommonEnums;

namespace Library.CommonHelpers;

public static class PositionCodeLevelListByUserType
{
    public static List<int> GetPositionCodeLevelInt(FieldUserType fieldUserType)
    {
        var result = new List<int>();
        switch (fieldUserType)
        {
            case FieldUserType.All:
                result.Add((int)PositionCodeLevel.L1Position);
                result.Add((int)PositionCodeLevel.L2Position);
                result.Add((int)PositionCodeLevel.L3Position);
                result.Add((int)PositionCodeLevel.L4Position);
                result.Add((int)PositionCodeLevel.L5Position);
                result.Add((int)PositionCodeLevel.L6Position);
                result.Add((int)PositionCodeLevel.L7Position);
                result.Add((int)PositionCodeLevel.L8Position);
                break;
            case FieldUserType.ESM:
                result.Add((int)PositionCodeLevel.L1Position);
                break;
            case FieldUserType.Managers:
                result.Add((int)PositionCodeLevel.L2Position);
                result.Add((int)PositionCodeLevel.L3Position);
                result.Add((int)PositionCodeLevel.L4Position);
                result.Add((int)PositionCodeLevel.L5Position);
                result.Add((int)PositionCodeLevel.L6Position);
                result.Add((int)PositionCodeLevel.L7Position);
                result.Add((int)PositionCodeLevel.L8Position);
                break;
        }

        return result;
    }
}
