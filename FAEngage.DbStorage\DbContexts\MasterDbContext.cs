﻿using FAEngage.Core;
using FAEngage.Core.Models.MasterDbModels;
using Microsoft.EntityFrameworkCore;

namespace FAEngage.DbStorage.DbContexts;

public class MasterDbContext(DbContextOptions<MasterDbContext> options) : DbContext(options)
{
    public DbSet<ClientEmployee> Employee { get; set; }
    public DbSet<Kpi> KPIs { get; set; }
    public DbSet<ManagerToken> FAManagersTokens { get; set; }
    public DbSet<Devices> Devices { get; set; }
    public DbSet<JourneyCalendar> JourneyCalendars { get; set; }
    public DbSet<JourneyCycle> JourneyCycles { get; set; }
    public DbSet<JourneyWeek> JourneyWeeks { get; set; }
    public DbSet<PositionCode> PositionCodes { get; set; }
    public DbSet<EngageNotification> FaEngageNotificationMasters { get; set; }
    public DbSet<Cohort> Cohorts { get; set; }
    public DbSet<NotificationMessage> FaEngageNotificationMessages { get; set; }
    public DbSet<KPITriggers> FaEngageKPITriggers { get; set; }
    public DbSet<PositionCodeEntityMapping> PositionCodeEntityMappings { get; set; }
    public DbSet<EmployeeProductDivMapping> EmployeeProductDivisionMappings { get; set; }
    public DbSet<CompanySetting> CompanySettings { get; set; }
    public DbSet<CompanySettingValue> CompanySettingValues { get; set; }
    public DbSet<CountryDetails> CountryDetails { get; set; }
    public DbSet<Company> Companies { get; set; }
    public DbSet<CompanyKPI> CompanyKPIs { get; set; }
    public DbSet<Location> Locations { get; set; }
    public DbSet<TheShopType> ShopTypes { get; set; }
    public DbSet<Channel> Channels { get; set; }
    public DbSet<FATradeUser> FATradeUsers { get; set; }
    public DbSet<FATradeToken> FATradeTokens { get; set; }
    public DbSet<PositionCodeHierarchy> PositionCodeHierarchies { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        #region Fluent Api Configuration

        modelBuilder.Entity<ClientEmployee>(entity =>
        {
            entity.ToTable("ClientEmployees");
            entity.Property(e => e.IsDeactive).HasColumnName("Deleted");
            entity.Property(e => e.Deleted).HasColumnName("IsDeleted");
            entity.Property(e => e.EmployeeType).HasColumnName("UserType");
        });

        modelBuilder.Entity<Devices>(entity => entity.HasKey(s => s.DeviceID));
        modelBuilder.Entity<Zone>(entity => entity.ToTable("FACompanyZones"));
        modelBuilder.Entity<Region>(entity => entity.ToTable("Regions"));
        modelBuilder.Entity<LocationBeat>(entity => entity.ToTable("LocationBeats"));
        modelBuilder.Entity<Territory>(entity => entity.ToTable("Territories"));
        modelBuilder.Entity<LocationBeat>().HasOne(s => s.Territory).WithOne(t => t.LocationBeats).HasForeignKey<LocationBeat>(b => b.TerritoryId);
        modelBuilder.Entity<Territory>().HasOne(t => t.TheRegion).WithMany().HasForeignKey(t => t.RegionId);

        // Location Configuration
        modelBuilder.Entity<Location>(entity =>
        {
            entity.ToTable("F2KLocations");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ShopName).HasMaxLength(200);
            entity.Property(e => e.CompanyId).HasColumnName("Company");
            entity.Property(e => e.MarketName).HasMaxLength(100);
            entity.Property(e => e.City).HasMaxLength(50);
            entity.Property(e => e.SubCity).HasMaxLength(200);
            entity.Property(e => e.District).HasMaxLength(200);
            entity.Property(e => e.Country).HasMaxLength(100);
            entity.Property(e => e.State).HasMaxLength(50);
            entity.HasOne(e => e.TheShopType).WithMany().HasForeignKey(e => e.ShopTypeId);
            entity.HasOne(e => e.Beat).WithMany().HasForeignKey(e => e.BeatId);
        });

        // TheShopType Configuration
        modelBuilder.Entity<TheShopType>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ShopTypeName).IsRequired().HasMaxLength(200);
            entity.Property(e => e.ErpId).IsRequired().HasMaxLength(50);
            entity.HasOne(e => e.Channel).WithMany().HasForeignKey(e => e.ChannelId);
        });

        // Channel Configuration
        modelBuilder.Entity<Channel>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.DefaultName).IsRequired().HasMaxLength(200);
            entity.Property(e => e.CustomName).HasMaxLength(200);
            entity.Property(e => e.ErpId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Enum).IsRequired();
        });

        modelBuilder.Entity<FATradeUser>(entity =>
        {
            entity.ToTable("FATradeUsers");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ShopName).HasMaxLength(500);
            entity.Property(e => e.City).HasMaxLength(50);
            entity.Property(e => e.State).HasMaxLength(50);
            entity.Property(e => e.OwnersName).HasMaxLength(50);
            entity.Property(e => e.PhoneNo).HasMaxLength(15);
            entity.Property(e => e.Email).HasMaxLength(500);
            entity.Property(e => e.MarketName).HasMaxLength(100);
            entity.Property(e => e.PAN).HasMaxLength(10);
            entity.Property(e => e.Aadhar).HasMaxLength(12);
            entity.Property(e => e.ShopType).HasMaxLength(50);
            entity.Property(e => e.PinCode).HasMaxLength(6);
            entity.Property(e => e.Landmark).HasMaxLength(1000);
            entity.Property(e => e.LandlineNumber).HasMaxLength(50);
            entity.Property(e => e.ModeOfDataCollection).HasMaxLength(500);
            entity.Property(e => e.BankAccountNumber).HasMaxLength(18);
            entity.Property(e => e.IFSCCode).HasMaxLength(11);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
        });

        modelBuilder.Entity<FATradeToken>(entity =>
        {
            entity.ToTable("FATradeTokens");
            entity.Property(t => t.Token).IsRequired().HasMaxLength(100);
        });

        #endregion Fluent Api Configuration
    }
}