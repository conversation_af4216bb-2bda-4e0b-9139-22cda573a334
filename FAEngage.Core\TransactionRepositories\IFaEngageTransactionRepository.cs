﻿using FAEngage.Core.Models.TransactionDbModels;

namespace FAEngage.Core.TransactionRepositories;

public interface IFaEngageTransactionRepository
{
    Task AddEngageLogToDb(FAEngageLog fAEngageLog);

    Task AddWhatsAppEngageLogToDb(FAEngageLogWhatsapp fAEngageLog);

    Task AddAnalyticsEngageLogToDb(FAManagerEngageLog fAEngageLog);

    Task<Guid> GetLastTransaction(long notificationId, long companyId, DateTimeOffset startTime, DateTimeOffset endTime);

    Task<List<FAEngageLog>> GetLogsForTransactionId(long companyId, Guid lastTransactionId);

    Task UpdateSendMessageForEngageLogs(Guid sendId, Guid transactionNotificationId, string message, int statusCode);
}