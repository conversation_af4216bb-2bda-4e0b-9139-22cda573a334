﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <OutputType>Library</OutputType>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Storage.Common" Version="12.24.0" />
    <PackageReference Include="Azure.Storage.Queues" Version="12.23.0" />
    <PackageReference Include="MongoDB.Driver.Core" Version="2.30.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="WindowsAzure.Storage" Version="9.3.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Libraries.CommonEnums\Libraries.CommonEnums.csproj" />
  </ItemGroup>

</Project>
