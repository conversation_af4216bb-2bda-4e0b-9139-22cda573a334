﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using ClickHouse.Client.ADO;
using ClickHouse.Client.Copy;
using Library.CommonHelpers;

namespace Library.SqlHelper;

public class QueryParameter
{
    public string Name { get; set; }
    public object Value { get; set; }
    public DbType DataType { get; set; }

    public QueryParameter(string name, object value, DbType dataType)
    {
        Name = name;
        Value = value;
        DataType = dataType;
    }
}

/// <summary>
/// Helper to execute Raw sql Queries in ClickHouse.
/// Usage: Inherit this helper in database specific readers.
/// Important: How to setup IHttpClientFactory for this:
/// services.AddHttpClient(ClickhouseDb.ClickhouseHttpClientFactoryName).ConfigurePrimaryHttpMessageHandler(_ => new HttpClientHandler
/// {
///     AutomaticDecompression = (DecompressionMethods.GZip | DecompressionMethods.Deflate)
/// });
/// </summary>
public abstract class ClickHouseSqldataReader
{
    public const string SuggestedHttpClientName = "clickHouseHttpClient";
    private readonly string _connectionString;
    private readonly string _httpClientName;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly int _commandTimeout;
    protected readonly string _dbName;

    protected ClickHouseSqldataReader(string connectionString, IHttpClientFactory httpClientFactory, string httpClientName
        , int commandTimeout, string dbName)
    {
        _connectionString = connectionString;
        _httpClientName = httpClientName;
        _httpClientFactory = httpClientFactory;
        _commandTimeout = commandTimeout;
        _dbName = dbName;
    }

    public ClickHouseConnection GetClickHouseConnection()
    {
        return new ClickHouseConnection(_connectionString,
            _httpClientFactory, _httpClientName);
    }

    private async Task OpenConnection(ClickHouseConnection connection)
    {
        await connection.OpenAsync();
        if (!string.IsNullOrWhiteSpace(_dbName))
        {
            connection.ChangeDatabase(_dbName);
        }
    }

    public async Task<DataTable> GetDatatableForQuery(string queryString, IEnumerable<QueryParameter> parameters = null,
        int? commandTimeout = null, bool isSp = false)
    {
        var resultTable = new DataTable();

        using var connection = GetClickHouseConnection();
        try
        {
            await OpenConnection(connection);

            using var command = new ClickHouseCommand(connection);
            try
            {
                command.CommandText = queryString;
                command.CommandTimeout = commandTimeout ?? _commandTimeout;
                if (isSp)
                {
                    command.CommandType = CommandType.StoredProcedure;
                }

                if (parameters != null)
                {
                    foreach (var parameter in parameters)
                    {
                        var param = command.CreateParameter();
                        param.ParameterName = parameter.Name;
                        param.Value = parameter.Value;
                        param.DbType = parameter.DataType;
                        command.Parameters.Add(param);
                    }
                }

                using var reader = await command.ExecuteReaderAsync();

                await resultTable.LoadManuallyFromDbDataReader(reader);
            }
            finally
            {
                command.Parameters.Clear();
            }

            return resultTable;
        }
        finally
        {
            await connection.CloseAsync();
        }
    }

    public async Task<long> BulkCopyData<T>(IEnumerable<T> data, string tableName)
    {
        var dataTable = DataTableHelpers.GetDataTable(data);
        using var connection = GetClickHouseConnection();
        try
        {
            await OpenConnection(connection);
            using var bulkCopy = new ClickHouseBulkCopy(connection) { DestinationTableName = tableName };
            await bulkCopy.InitAsync();

            await bulkCopy
                .WriteToServerAsync(dataTable, CancellationToken.None);

            return bulkCopy.RowsWritten;
        }
        finally
        {
            await connection.CloseAsync();
        }
    }

    public async Task<long> InsertWithSettingsAsync<T>(
    IEnumerable<T> data,
    string tableName,

    Dictionary<string, object> clickhouseSettings)
    {
        ArgumentNullException.ThrowIfNull(data);

        if (string.IsNullOrWhiteSpace(tableName))
        {
            throw new ArgumentException("Table name is required.");
        }

        if (string.IsNullOrWhiteSpace(_connectionString))
        {
            throw new ArgumentException("Connection string is required.");
        }

        var dataTable = DataTableHelpers.GetDataTable(data);
        if (dataTable.Rows.Count == 0)
        {
            return 0;
        }

        try
        {
            await using var connection = new ClickHouseConnection(_connectionString);
            await connection.OpenAsync();

            var columns = dataTable.Columns.Cast<DataColumn>().Select(c => EscapeColumnName(c.ColumnName)).ToArray();
            var columnList = string.Join(", ", columns);

            var settingsClause = clickhouseSettings != null && clickhouseSettings.Any()
                ? " SETTINGS " + string.Join(", ", clickhouseSettings.Select(kv => $"{kv.Key}={FormatClickhouseValue(kv.Value)}"))
                : "";

            var insertPrefix = $"INSERT INTO {EscapeTableName(tableName)} ({columnList}){settingsClause} VALUES ";

            var sb = new StringBuilder();
            sb.Append(insertPrefix);

            for (var i = 0; i < dataTable.Rows.Count; i++)
            {
                var row = dataTable.Rows[i];
                var values = row.ItemArray.Select(FormatClickhouseValue);
                sb.Append($"({string.Join(", ", values)})");

                if (i < dataTable.Rows.Count - 1)
                {
                    sb.Append(", ");
                }
            }

            await using var cmd = connection.CreateCommand();
            cmd.CommandText = sb.ToString();
            await cmd.ExecuteNonQueryAsync();

            await connection.CloseAsync();
            return dataTable.Rows.Count;
        }
        catch (Exception ex)
        {
            // Log the exception or handle it as needed
            Console.WriteLine($"Error during bulk insert: {ex.Message}");
            throw;
        }
    }

    private static string EscapeColumnName(string columnName)
    {
        return $"`{columnName.Replace("`", "``")}`";
    }

    private static string EscapeTableName(string tableName)
    {
        return $"`{tableName.Replace("`", "``")}`";
    }

    private static string FormatClickhouseValue(object value)
    {
        return value switch
        {
            string s => $"'{s.Replace("'", "''")}'",
            bool b => b ? "1" : "0",
            DateTime dt => $"'{dt:yyyy-MM-dd HH:mm:ss}'",
            _ => Convert.ToString(value, System.Globalization.CultureInfo.InvariantCulture)
        };
    }
}
