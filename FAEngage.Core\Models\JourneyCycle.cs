﻿using Library.DateTimeHelpers;

namespace FAEngage.Core.Models;

public class JourneyCycleMonthYear
{
    public int Month { get; set; }
    public int Year { get; set; }
    public DateTime MonthStartDate { get; set; }
    public DateTime MonthEndDate { get; set; }
    public string MonthName { get; set; }
    public ICollection<JourneyWeekDataModel> Weeks { get; set; }
}

public class DateRangeForMonthRange
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public DateTime EndMonthStartDate { get; set; }
    public int StartMonth { get; set; }
    public int EndMonth { get; set; }
    public int StartYear { get; set; }
    public int EndYear { get; set; }
}

public class DateRangeForMonthsWithMTDLMTDList
{
    public DateRangeForMonthRange Ranges { get; set; }
    public List<FA_MTD_LMTD> FA_MTD_LMTDs { get; set; }
}

public class JourneyWeekDataModel
{
    public int WeekForMonth { get; set; }
    public int QuarterNumber { get; set; }
    public int WeekForQuarter { get; set; }
    public int WeekForYear { get; set; }
    public DateTime WeekStartDate { get; set; }
    public DateTime WeekEndDate { get; set; }
}