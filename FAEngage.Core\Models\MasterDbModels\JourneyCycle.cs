﻿namespace FAEngage.Core.Models.MasterDbModels;

public class JourneyCycle
{
    public long Id { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    public string CreationContext { get; set; }


    public int MonthNumber { get; set; }


    public string MonthName { get; set; }


    public DateTime MonthStartDate { get; set; }


    public DateTime MonthEndDate { get; set; }

    public long JourneyCalendarId { get; set; }

    public long CompanyId { get; set; }
    public virtual JourneyCalendar JourneyCalendar { get; set; }
    public ICollection<JourneyWeek> JourneyWeeks { get; set; }

    public static ICollection<JourneyCycle> GetCycles(DateTime startDate)
    {
        var cycles = new List<JourneyCycle>();
        for (var j = 0; j < 12; j++)
        {
            var monthStartDate = startDate.AddMonths(j);
            var monthEndDate = monthStartDate.AddMonths(1).AddDays(-1);
            cycles.Add(new JourneyCycle
            {
                MonthStartDate = monthStartDate,
                MonthEndDate = monthEndDate,
                MonthNumber = j + 1
            });
        }

        return cycles;
    }
}