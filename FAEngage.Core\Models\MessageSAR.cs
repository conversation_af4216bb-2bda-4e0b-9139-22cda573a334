﻿using System.Text.Json.Serialization;

namespace FAEngage.Core.Models;

public class MessageSAR
{
    [JsonPropertyName("@VER")] public string @VER { get; set; }
    public User USER { get; set; }
    public List<SMS> SMS { get; set; }
}

public class User
{
    [Json<PERSON>ropertyName("@CH_TYPE")] public string @CH_TYPE { get; set; }
    [JsonPropertyName("@UNIXTIMESTAMP")] public string @UNIXTIMESTAMP { get; set; }
}

public class SMS
{
    [JsonPropertyName("@UDH")] public string @UDH { get; set; }
    [JsonPropertyName("@CODING")] public string @CODING { get; set; }
    [JsonPropertyName("@TEXT")] public string @TEXT { get; set; }
    [JsonPropertyName("@TEMPLATEINFO")] public string @TEMPLATEINFO { get; set; }
    [JsonPropertyName("@PROPERTY")] public string @PROPERTY { get; set; }
    [JsonPropertyName("@MSGTYPE")] public string @MSGTYPE { get; set; }
    [JsonPropertyName("@ID")] public string @ID { get; set; }
    public List<ADDRESS> ADDRESS { get; set; }
}

public class ADDRESS
{
    [JsonPropertyName("@FROM")] public string @FROM { get; set; }
    [JsonPropertyName("@TO")] public string @TO { get; set; }
    [JsonPropertyName("@SEQ")] public string @SEQ { get; set; }
    [JsonPropertyName("@TAG")] public string @TAG { get; set; }
}

public class MessageSignify
{
    public string apiKey { get; set; }
    public string campaignName { get; set; }
    public string destination { get; set; }
    public string userName { get; set; }
    public MEDIA media { get; set; }
    public List<string> templateParams { get; set; }
}

public class MEDIA
{
    public string url { get; set; }
    public string fileName { get; set; }
}