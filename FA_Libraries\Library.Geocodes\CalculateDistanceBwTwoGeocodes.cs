﻿using System;

namespace Library.Geocodes;

public class CalculateDistanceBwTwoGeocodes
{
    /// <summary>
    ///     get the distance betwee two geo codes in METRES.
    /// </summary>
    /// <param name="Lat1">latitude of first geo code</param>
    /// <param name="Long1">longitude of first geo code</param>
    /// <param name="Lat2">latiude of second geo code</param>
    /// <param name="Long2">longitude of second geocode</param>
    /// <returns>double value of distance in metres</returns>
    public static double? CalculateDistance(
        double? Lat1,
        double? Long1,
        double? Lat2,
        double? Long2)
    {
        /*
            The Haversine formula according to Dr. Math.
            http://mathforum.org/library/drmath/view/51879.html

            dlon = lon2 - lon1
            dlat = lat2 - lat1
            a = (sin(dlat/2))^2 + cos(lat1) * cos(lat2) * (sin(dlon/2))^2
            c = 2 * atan2(sqrt(a), sqrt(1-a))
            d = R * c

            Where
                * dlon is the change in longitude
                * dlat is the change in latitude
                * c is the great circle distance in Radians.
                * R is the radius of a spherical Earth.
                * The locations of the two points in
                    spherical coordinates (longitude and
                    latitude) are lon1,lat1 and lon2, lat2.
        */
        try
        {
            if (!Lat1.HasValue
                || !Lat2.HasValue
                || !Long1.HasValue
                || !Long2.HasValue)
            {
                return default;
            }

            if (Lat1 == 0
                || Lat2 == 0
                || Long1 == 0
                || Long2 == 0)
            {
                return default;
            }

            var dDistance = double.MinValue;
            var dLat1InRad = Lat1.Value * (Math.PI / 180.0);
            var dLong1InRad = Long1.Value * (Math.PI / 180.0);
            var dLat2InRad = Lat2.Value * (Math.PI / 180.0);
            var dLong2InRad = Long2.Value * (Math.PI / 180.0);

            var dLongitude = dLong2InRad - dLong1InRad;
            var dLatitude = dLat2InRad - dLat1InRad;

            // Intermediate result a.
            var a = Math.Pow(Math.Sin(dLatitude / 2.0), 2.0) +
                    Math.Cos(dLat1InRad) * Math.Cos(dLat2InRad) *
                    Math.Pow(Math.Sin(dLongitude / 2.0), 2.0);

            // Intermediate result c (great circle distance in Radians).
            var c = 2.0 * Math.Asin(Math.Sqrt(a));

            // Distance.
            // const Double kEarthRadiusMiles = 3956.0;
            const double kEarthRadiusKms = 6376.5;
            dDistance = kEarthRadiusKms * c;

            // distance in metres

            var distanceInMetres = dDistance * 1000;

            return distanceInMetres;
        }
        catch (Exception)
        {
            return default;
        }
    }
}
