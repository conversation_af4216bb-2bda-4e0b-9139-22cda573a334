﻿using System;
using System.Runtime.InteropServices;
using Newtonsoft.Json;

namespace Library.JsonHelper;

[StructLayout(LayoutKind.Explicit)]
internal struct DecimalScale
{
    public DecimalScale(decimal value)
    {
        this = default;
        d = value;
    }

    [FieldOffset(0)]
    private readonly decimal d;

    [FieldOffset(0)]
    private readonly int flags;

    public int Scale => (flags >> 16) & 0x000000FF;
}

/// <summary>
///     Custom Decimal Json Converter
/// </summary>
public class CustomDecimalJsonConverter : JsonConverter
{
    public override bool CanRead => false;

    private static bool IsWholeValue(object value)
    {
        if (value is decimal decimalValue)
        {
            var scale = new DecimalScale(decimalValue).Scale; //Scale is the number of digits after the decimal point
            return scale == 0;
        }

        if (value is float || value is double)
        {
            var doubleValue = (double)value;
            return doubleValue == Math.Truncate(doubleValue);
        }

        return false;
    }

    public override bool CanConvert(Type objType)
    {
        return objType == typeof(decimal) || objType == typeof(float) || objType == typeof(double);
    }

    public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
    {
        throw new NotImplementedException("Error reading Json value.");
    }

    public override void WriteJson(JsonWriter jWriter, object value, JsonSerializer jSerializer)
    {
        if (IsWholeValue(value))
        {
            jWriter.WriteRawValue(JsonConvert.ToString(Convert.ToInt64(value)));
        }
        else if (value is decimal || value is float || value is double)
        {
            //Date: 04-10-2021; Asana: https://app.asana.com/0/305436650865282/1200941749067687/f
            //change: Remove rounding to 2 decimal places. The converter is only referenced in Flexible report code and tests
            jWriter.WriteRawValue(JsonConvert.ToString(Convert.ToDecimal(value)));
        }
    }
}
