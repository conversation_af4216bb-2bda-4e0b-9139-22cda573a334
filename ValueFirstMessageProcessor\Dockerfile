# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

# This stage is used when running from VS in fast mode (Default for Debug configuration)
FROM mcr.microsoft.com/dotnet/aspnet:9.0-alpine AS base
WORKDIR /app
ENV DOTNET_RUNNING_IN_CONTAINER=true
RUN apk add --no-cache icu-libs
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false


# This stage is used to build the service project
FROM mcr.microsoft.com/dotnet/sdk:9.0-alpine AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["ValueFirstMessageProcessor/ValueFirstMessageProcessor.csproj", "ValueFirstMessageProcessor/"]
COPY ["FAEngage.DbStorage/FAEngage.DbStorage.csproj", "FAEngage.DbStorage/"]
COPY ["FAEngage.Core/FAEngage.Core.csproj", "FAEngage.Core/"]
COPY ["FA_Libraries/Libraries.CommonEnums/Libraries.CommonEnums.csproj", "FA_Libraries/Libraries.CommonEnums/"]
COPY ["FA_Libraries/Libraries.CommonModels/Libraries.CommonModels.csproj", "FA_Libraries/Libraries.CommonModels/"]
COPY ["FA_Libraries/Library.DateTimeHelpers/Library.DateTimeHelpers.csproj", "FA_Libraries/Library.DateTimeHelpers/"]
COPY ["FA_Libraries/Library.NumberSystem/Library.NumberSystem.csproj", "FA_Libraries/Library.NumberSystem/"]
COPY ["FA_Libraries/Library.SlackService/Library.SlackService.csproj", "FA_Libraries/Library.SlackService/"]
COPY ["FA_Libraries/Library.Infrastructure/Library.Infrastructure.csproj", "FA_Libraries/Library.Infrastructure/"]
COPY ["FA_Libraries/Library.StorageWriter/Library.StorageWriter.csproj", "FA_Libraries/Library.StorageWriter/"]
COPY ["FA_Libraries/Libraries.Cryptography/Libraries.Cryptography.csproj", "FA_Libraries/Libraries.Cryptography/"]
COPY ["FA_Libraries/Library.SqlHelper/Library.SqlHelper.csproj", "FA_Libraries/Library.SqlHelper/"]
COPY ["FA_Libraries/Library.CommonHelpers/Library.CommonHelpers.csproj", "FA_Libraries/Library.CommonHelpers/"]
RUN dotnet restore "./ValueFirstMessageProcessor/ValueFirstMessageProcessor.csproj"
COPY . .
WORKDIR "/src/ValueFirstMessageProcessor"
RUN dotnet build "./ValueFirstMessageProcessor.csproj" -c $BUILD_CONFIGURATION -o /app/build

# This stage is used to publish the service project to be copied to the final stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./ValueFirstMessageProcessor.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# This stage is used in production or when running from VS in regular mode (Default when not using the Debug configuration)
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "ValueFirstMessageProcessor.dll"]