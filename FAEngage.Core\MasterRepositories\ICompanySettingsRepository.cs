﻿using Libraries.CommonEnums;

namespace FAEngage.Core.MasterRepositories
{
    public interface ICompanySettingsRepository
    {
        Task<Dictionary<string, object>> GetSettings(long companyId);
        Task<bool> UsesUserDistributorMapping(long companyId);
        Task<bool> UsesBeatDistributorMapping(long companyId);
        Task<bool> UsesPositionCodes(long companyId);
        Task<bool> UsesRoutePlan(long companyId);
        Task<bool> UsesNetValueForEmployeeSales(long companyId);
        Task<bool> UsesBeatPlan(long companyId);
        Task<bool> UseManualTertiaryOffTake(long companyId);
        Task<bool> E11Company(long companyId);
        Task<bool> UsesSalesInAmount(long companyId);
        Task<bool> UsesScheduledCallProductivity(long companyId);
        Task<TimeSpan> GetCompanyTimeZoneOffset(long companyId);
        Task<bool> UsesInternationalNumberSystem(long companyId);
        List<long> GetCompaniesForInvoiceReport(string settingValue);
        Task<string> GetCurrencySymbol(long companyId);
        Task<bool> UsesAutomaticErpIdWithRegionCode(long companyId);
        Task<TargetValueType> TargetValueType(long companyId);
        Task<string> CalculateAchFrom(long companyId);
        Task<string> PrimaryTargetsAchievementCalculation(long companyId);
        Task<bool> UseChannelFinancing(long companyId);
        Task<TargetOn> GetTargetOn(long companyId);
        Task<bool> UsesEngageWhatsappIntegration(long companyId);
    }
}