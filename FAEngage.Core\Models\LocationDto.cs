﻿using Libraries.CommonEnums;

namespace FAEngage.Core.Models
{
    public class LocationDto
    {
        public long Id { get; set; }
        public bool IsBlocked { get; set; }
        public string ShopName { get; set; }
        public OutletSegmentation Segmentation { get; set; }
        public long CompanyId { get; set; }
        public string ErpId { get; set; }
        public long? BeatId { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public string ShopType { set; get; }
        public long? ShopTypeId { get; set; }
        public OutletChannel OutletChannel { set; get; }
        public string MarketName { set; get; }
        public string City { set; get; }
        public string SubCity { get; set; }
        public string District { get; set; }
        public string Country { get; set; }
        public string State { set; get; }
        public OutletSegmentation CompanySegmentation { set; get; }
        public bool IsFocused { set; get; }
        public string ShopTypeCode { get; set; }
        public string CustomTags { get; set; }
        public long? RegionId { get; set; }
        public long? ZoneId { get; set; }
        public long? TerritoryId { get; set; }
        public string OwnersName { get; set; }
        public string OwnersNo { get; set; }
    }

    public class NotificationQueueData
    {
        public string OwnersName { get; set; }
        public Guid? ImageId { get; set; }
    }
}
