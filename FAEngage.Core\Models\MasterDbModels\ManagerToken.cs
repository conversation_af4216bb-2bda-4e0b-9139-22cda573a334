﻿using Libraries.CommonEnums;

namespace FAEngage.Core.Models.MasterDbModels;

public class ManagerToken
{
    public long Id { get; set; }
    public string Token { get; set; }
    public ManagerAppLoginIdentifier LoginIdentifier { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { set; get; }
    public PortalUserRole UserRole { set; get; }
    public long UserId { set; get; }
    public long CompanyId { get; set; }
    public string CreationContext { get; set; }
    public string FCMToken { get; set; }
    public AppPlatform? Platform { get; set; }
}