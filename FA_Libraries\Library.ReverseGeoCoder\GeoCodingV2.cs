﻿using Library.ReverseGeoCoder.Interface;
using Library.SlackService;
using Library.StringHelpers;

namespace Library.ReverseGeoCoder;

public class GeoCodingV2 : IGeoCoding
{
    public enum Source
    {
        GoogleApi = 0,
        GoogleApi_FO1 = 1,
        GoogleApi_FO2 = 2,
        GoogleApi_FO3 = 3,
        GoogleApi_FO4 = 4,
        GoogleApi_FO5 = 5,
        GoogleApi_FO6 = 6,
        GoogleApi_FO7 = 7
    }

    private readonly ErrorMessenger _errorMessanger;
    private readonly Source source;

    public GeoCodingV2(ErrorMessenger errorMessanger, Source source = Source.GoogleApi)
    {
        _errorMessanger = errorMessanger;
        this.source = source;
    }

    public GeoCodedReturnData GetDataFromLatLongFromGoogle(decimal lat, decimal lng, bool isoldAPI = false)
    {
        if (!CheckForZeroLatLng(lat, lng))
        {
            return new GeoCodedReturnData { Latitude = lat, Longitude = lng };
        }

        GeoCodedReturnData data = null;
        var geocoderGoogle = new GeoCodingGoogle(_errorMessanger, source);
        if (isoldAPI)
        {
            var geoCoderLocationIQ = new GeoCodingLocationIQ(_errorMessanger);
            data = geoCoderLocationIQ.GetDataFromLatLng(lat, lng, true);
        }

        if (!IsGeoDataValid(data))
        {
            data = geocoderGoogle.GetDataFromLatLng(lat, lng);
        }

        if (string.IsNullOrEmpty(data?.Address) || string.IsNullOrWhiteSpace(data?.Address))
        {
            geocoderGoogle = new GeoCodingGoogle(_errorMessanger, Source.GoogleApi_FO1);
            data = geocoderGoogle.GetDataFromLatLng(lat, lng);
        }

        if (string.IsNullOrEmpty(data?.Address) || string.IsNullOrWhiteSpace(data?.Address))
        {
            geocoderGoogle = new GeoCodingGoogle(_errorMessanger, Source.GoogleApi_FO2);
            data = geocoderGoogle.GetDataFromLatLng(lat, lng);
        }

        if (string.IsNullOrEmpty(data?.Address) || string.IsNullOrWhiteSpace(data?.Address))
        {
            geocoderGoogle = new GeoCodingGoogle(_errorMessanger, Source.GoogleApi_FO3);
            data = geocoderGoogle.GetDataFromLatLng(lat, lng);
        }

        if (string.IsNullOrEmpty(data?.Address) || string.IsNullOrWhiteSpace(data?.Address))
        {
            geocoderGoogle = new GeoCodingGoogle(_errorMessanger, Source.GoogleApi_FO4);
            data = geocoderGoogle.GetDataFromLatLng(lat, lng);
        }

        if (string.IsNullOrEmpty(data?.Address) || string.IsNullOrWhiteSpace(data?.Address))
        {
            geocoderGoogle = new GeoCodingGoogle(_errorMessanger, Source.GoogleApi_FO5);
            data = geocoderGoogle.GetDataFromLatLng(lat, lng);
        }

        if (string.IsNullOrEmpty(data?.Address) || string.IsNullOrWhiteSpace(data?.Address))
        {
            geocoderGoogle = new GeoCodingGoogle(_errorMessanger, Source.GoogleApi_FO6);
            data = geocoderGoogle.GetDataFromLatLng(lat, lng);
        }

        if (string.IsNullOrEmpty(data?.Address) || string.IsNullOrWhiteSpace(data?.Address))
        {
            geocoderGoogle = new GeoCodingGoogle(_errorMessanger, Source.GoogleApi_FO7);
            data = geocoderGoogle.GetDataFromLatLng(lat, lng);
        }

        data?.Locality.TrimToLength(200); //Database Limits it to 200
        return data;
    }

    public GeoCodedReturnData GetDataFromLatLngFromMapMyIndia(decimal lat, decimal lng)
    {
        var geocoderMapMyIndia = new GeoCodingMapMyIndia(_errorMessanger);
        var data = geocoderMapMyIndia.GetDataFromLatLng(lat, lng);
        data.Locality.TrimToLength(200); //Database Limits it to 200
        return data;
    }

    public GeoCodedReturnData GetDataFromLatLngFromLocationIQ(decimal lat, decimal lng)
    {
        if (!CheckForZeroLatLng(lat, lng))
        {
            return new GeoCodedReturnData { Latitude = lat, Longitude = lng };
        }

        var geocoderLocationIQ = new GeoCodingLocationIQ(_errorMessanger);
        var data = geocoderLocationIQ.GetDataFromLatLng(lat, lng, true, true);
        data.Locality.TrimToLength(200); //Database Limits it to 200
        return data;
    }

    //Date: Dec 31,2020 ; Asana:https://app.asana.com/0/282003586492747/1198702698567327/f ; Change: Added a new method to get reverse geo coded data using a specific Google API
    public GeoCodedReturnData GetDataFromLatLongFromGoogleAPI(decimal lat, decimal lng)
    {
        if (!CheckForZeroLatLng(lat, lng))
        {
            return new GeoCodedReturnData { Latitude = lat, Longitude = lng };
        }

        var geocoderGoogle = new GeoCodingGoogle(_errorMessanger, source);
        var data = geocoderGoogle.GetDataFromLatLng(lat, lng);
        return data;
    }

    private static bool IsGeoDataValid(GeoCodedReturnData data)
    {
        if (data == null)
        {
            return false;
        }

        return !string.IsNullOrWhiteSpace(data.Address)
               && !string.IsNullOrWhiteSpace(data.State)
               && !string.IsNullOrWhiteSpace(data.City) && !string.IsNullOrWhiteSpace(data.PinCode);
    }

    private static bool CheckForZeroLatLng(decimal lat, decimal lng)
    {
        return lat != decimal.Zero || lng != decimal.Zero;
    }
}
