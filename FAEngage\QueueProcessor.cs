﻿using System;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Azure.WebJobs;
using Microsoft.Extensions.DependencyInjection;
using FAEngage.Configurations;
using FAEngage.Core.Models;
using FAEngage.Core.Services;
using Library.ResiliencyHelpers;
using Polly;
using Serilog;

namespace FAEngage
{
    public class QueueProcessor
    {
        private readonly AsyncPolicy retryPolicy;
        private readonly ResilientAction resilientAction;
        private readonly FAEngageService engageService;
        private readonly IServiceProvider serviceProvider;

        public QueueProcessor(ResilientAction resilientAction, FAEngageService engageService, IServiceProvider serviceProvider)
        {
            retryPolicy = Policy.Handle<Exception>().WaitAndRetryAsync(5, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));
            this.serviceProvider = serviceProvider;
            this.resilientAction = resilientAction;
            this.engageService = engageService;
        }

        public async Task ProcessNotification([TimerTrigger("*/30 * * * *", RunOnStartup = true)] TimerInfo myTimer)
        {
            using (var scope = serviceProvider.CreateScope())
            {
                try
                {
                    var startTime = DateTime.UtcNow;
                    var processor = scope.ServiceProvider.GetRequiredService<INotificationProcessor>();
                    await resilientAction.RetryResilientlyAsync(processor.ProcessUserApp, startTime);
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "Notification processor failed");
                    throw;
                }
            }
        }

        public async Task ProcessNotificationMain([QueueTrigger(Dependencies.NotificationMainQueue, Connection = "StorageConnectionString")] EnagageNotificationMain queueRequest)
        {
            using (var scope = serviceProvider.CreateScope())
            {
                try
                {
                    var processor = scope.ServiceProvider.GetRequiredService<INotificationProcessor>();
                    await resilientAction.RetryResilientlyAsync(processor.ProcessNotification, queueRequest.NotificationId, queueRequest.CompanyId);
                }
                catch (Exception ex)
                {
                    Log.Error(ex, $"Notification Failed for NotificationId: {queueRequest.NotificationId} for CompanyId: {queueRequest.CompanyId}");
                    throw;
                }
            }
        }

        public async Task ProcessEmployeeNotification([QueueTrigger(Dependencies.NotificationEmployeeQueue, Connection = "StorageConnectionString")] EnagageNotificationEmployee queueRequest)
        {
            using (var scope = serviceProvider.CreateScope())
            {
                try
                {
                    var processor = scope.ServiceProvider.GetRequiredService<INotificationProcessor>();
                    await processor.ProcessEmployeeNotification(queueRequest.NotificationId, queueRequest.CompanyId, queueRequest.UserId, queueRequest.TransactionId, queueRequest.PositionCodeIds);
                }
                catch (Exception ex)
                {
                    Log.Error(ex, $"Notification Employee failed for NotificationId: {queueRequest.NotificationId} and UserId: {queueRequest.UserId} and TransactionId: {queueRequest.TransactionId}");
                    throw;
                }
            }
        }

        public async Task ProcessMessageNotification([QueueTrigger(Dependencies.NotificationMessageQueue, Connection = "StorageConnectionString")] EnagageNotificationEmployee queueRequest)
        {
            using (var scope = serviceProvider.CreateScope())
            {
                try
                {
                    var processor = scope.ServiceProvider.GetRequiredService<INotificationProcessor>();
                    await processor.ProcessMessageNotification(queueRequest.NotificationId, queueRequest.CompanyId, queueRequest.UserId, queueRequest.TransactionId,
                        queueRequest.PositionCodeIds, queueRequest.KPIs, queueRequest.FATradeNotificationToken, queueRequest.OwnersName, queueRequest.ImageId);
                }
                catch (Exception ex)
                {
                    Log.Error(ex, $"Notification Message failed for NotificationId: {queueRequest.NotificationId} and UserId: {queueRequest.UserId} and TransactionId: {queueRequest.TransactionId}");
                    throw;
                }
            }
        }

        public async Task ProcessNotificationAggregated([QueueTrigger(Dependencies.NotificationAggregatedQueue, Connection = "StorageConnectionString")] EnagageNotificationMain queueRequest)
        {
            using (var scope = serviceProvider.CreateScope())
            {
                try
                {
                    var processor = scope.ServiceProvider.GetRequiredService<INotificationProcessor>();
                    await resilientAction.RetryResilientlyAsync(processor.ProcessNotificationAggregated, queueRequest.NotificationId, queueRequest.CompanyId);
                }
                catch (Exception ex)
                {
                    Log.Error(ex, $"Notification Aggregation failed for NotificationId: {queueRequest.NotificationId} and CompanyId: {queueRequest.CompanyId}");
                    throw;
                }
            }
        }

        public async Task ProcessNotificationToUpdateEngageLogsWithCorrectFcmApiResponse([QueueTrigger(Dependencies.EngageLogQueue, Connection = "StorageConnectionString")] EngageLogQueueData data)
        {
            try
            {
                await retryPolicy.ExecuteAsync(async () => await engageService.UpdateEngageLogsMessage(
                    data.SendId, data.NotificationTransactionId, data.Message, data.StatusCode));
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error occurred while updating engage log with correct fcm api response data {0}", JsonSerializer.Serialize(data));
                throw;
            }
        }
    }
}