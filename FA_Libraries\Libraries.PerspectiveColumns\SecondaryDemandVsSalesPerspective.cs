﻿using System;
using System.Collections.Generic;
using Libraries.CommonEnums;
using Libraries.CommonEnums.Helpers;
using Libraries.PerspectiveColumns.Interface;

namespace Libraries.PerspectiveColumns;

public class SecondaryDemandVsSalesPerspective : IPerspective
{
    private readonly LinkNames linkNames;

    public SecondaryDemandVsSalesPerspective(Dictionary<string, string> nomenclatureDict)
    {
        linkNames = LinkNames.GetLinkNames(nomenclatureDict);
    }

    List<PerspectiveColumnModel> IPerspective.Columns
    {
        get => Columns;
        set => throw new NotImplementedException();
    }

    public List<PerspectiveColumnModel> Columns => new()
    {
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L8Position,
            Name = "L8Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L8Position + " Code",
            Name = "L8Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L8Position + " User ErpId",
            Name = "L8Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L8Position + " User",
            Name = "L8Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L8Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L7Position,
            Name = "L7Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L7Position + " Code",
            Name = "L7Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L7Position + " User ErpId",
            Name = "L7Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L7Position + " User",
            Name = "L7Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L7Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L6Position,
            Name = "L6Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L6Position + " Code",
            Name = "L6Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L6Position + " User ErpId",
            Name = "L6Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L6Position + " User",
            Name = "L6Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L6Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L5Position,
            Name = "L5Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L5Position + " Code",
            Name = "L5Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L5Position + " User ErpId",
            Name = "L5Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L5Position + " User",
            Name = "L5Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L5Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L4Position,
            Name = "L4Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L4Position + " Code",
            Name = "L4Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L4Position + " User ErpId",
            Name = "L4Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L4Position + " User",
            Name = "L4Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L4Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L3Position,
            Name = "L3Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L3Position + " Code",
            Name = "L3Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L3Position + " User ErpId",
            Name = "L3Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L3Position + " User",
            Name = "L3Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L3Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L2Position,
            Name = "L2Position",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L2Position + " Code",
            Name = "L2Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L2Position + " User ErpId",
            Name = "L2Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L2Position + " User",
            Name = "L2Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L2Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L1Position,
            Name = "L1Position",
            IsMeasure = true,
            IsDimension = true,
            SubGroup = linkNames.L1Position,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L1Position + " Code",
            Name = "L1Position_Code",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L1Position + " User ErpId",
            Name = "L1Position_UserErpId",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = linkNames.L1Position + " User",
            Name = "L1Position_User",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Field User Name",
            Name = "L1Position_FieldUser",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Reporting Manager",
            Name = "L2Position_ReportingUser",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Field User HQ",
            Name = "L1Position_UserHQ",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Field User ERP ID",
            Name = "L1Position_UserERP",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Employee " + linkNames.AttributeText1,
            Name = "EmployeeAttributeText1",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Employee " + linkNames.AttributeText2,
            Name = "EmployeeAttributeText2",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Employee " + linkNames.AttributeNumber1,
            Name = "EmployeeAttributeNumber1",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Employee " + linkNames.AttributeNumber2,
            Name = "EmployeeAttributeNumber2",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Date Of Joining",
            Name = "DateOfJoining",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Email Id",
            Name = "EmailId",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "User Type",
            Name = "UserType",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "Contact Number",
            Name = "ContactNumber",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Position",
            DisplayName = "User Designation",
            Name = "L1Position_UserDesignation",
            IsMeasure = false,
            IsDimension = true,
            SubGroup = linkNames.L1Position
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.GlobalSalesManager + " User ErpId",
            Name = "GSMErpId",
            IsMeasure = false,
            IsDimension = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.GlobalSalesManager,
            Name = "GSM",
            IsMeasure = false,
            IsDimension = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.NationalSalesManager + " User ErpId",
            Name = "NSMErpId",
            IsMeasure = false,
            IsDimension = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.NationalSalesManager,
            Name = "NSM",
            IsMeasure = false,
            IsDimension = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.ZonalSalesManager + " User ErpId",
            Name = "ZSMErpId",
            IsMeasure = false,
            IsDimension = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.ZonalSalesManager,
            Name = "ZSM",
            IsMeasure = false,
            IsDimension = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.RegionalSalesManager + " User ErpId",
            Name = "RSMErpId",
            IsMeasure = false,
            IsDimension = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.RegionalSalesManager,
            Name = "RSM",
            IsMeasure = false,
            IsDimension = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.AreaSalesManager + " User ErpId",
            Name = "ASMErpId",
            IsMeasure = false,
            IsDimension = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = linkNames.AreaSalesManager,
            Name = "ASM",
            IsMeasure = false,
            IsDimension = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "Field User Name",
            Name = "FieldUserName",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "Field User HQ",
            Name = "FieldUserHQ",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "Field User Erp ID",
            Name = "FieldUserErpID",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "User Designation",
            Name = "L1Position_UserDesignation",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Field User",
            DisplayName = "Reporting Manager",
            Name = "ReportingManager",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Channel Partner",
            DisplayName = linkNames.Distributor + " Erp ID",
            Name = "DistributorErpId",
            IsMeasure = false,
            IsDimension = true
        },
        new()
        {
            Attribute = "Channel Partner",
            DisplayName = linkNames.Distributor,
            Name = "Distributor",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Channel Partner",
            DisplayName = linkNames.Distributor + " Address",
            Name = "DistributorAddress",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Channel Partner",
            DisplayName = linkNames.Distributor + " GSTIN",
            Name = "DistributorGSTIN",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Channel Partner",
            DisplayName = linkNames.Distributor + " Phone",
            Name = "DistributorPhone",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Channel Partner",
            DisplayName = "Stockist Type",
            Name = "StockistType",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Channel Partner",
            DisplayName = linkNames.SuperStockist + " Erp ID",
            Name = "SuperStockistErpId",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Channel Partner",
            DisplayName = linkNames.SuperStockist,
            Name = "SuperStockist",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Channel Partner",
            DisplayName = "Is Using DMS",
            Name = "IsUsingDMS",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Beat,
            Name = "Beat",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Territory,
            Name = "Territory",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Region,
            Name = "Region",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Zone,
            Name = "Zone",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Level5,
            Name = "Level5",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Level6,
            Name = "Level6",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Sales Territory",
            DisplayName = linkNames.Level7,
            Name = "Level7",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Retailer",
            DisplayName = linkNames.Outlets + "ErpID",
            Name = "ShopErpId",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Retailer",
            DisplayName = linkNames.Outlets,
            Name = "Shop",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = true
        },
        new()
        {
            Attribute = "Retailer",
            DisplayName = linkNames.Outlets + "Segment",
            Name = "ShopSegmentation",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Retailer",
            DisplayName = linkNames.Outlets + "Type",
            Name = "ShopType",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Retailer",
            DisplayName = linkNames.Outlets + "Channel",
            Name = "ShopChannel",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Retailer",
            DisplayName = linkNames.Outlets + "Owner Name",
            Name = "ShopOwnersName",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Retailer",
            DisplayName = linkNames.Outlets + "Owner Phone",
            Name = "ShopOwnersNumber",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Retailer",
            DisplayName = linkNames.Outlets + "City",
            Name = "ShopCity",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Retailer",
            DisplayName = linkNames.Outlets + "SubCity",
            Name = "ShopSubCity",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Retailer",
            DisplayName = linkNames.Outlets + "State",
            Name = "ShopState",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Retailer",
            DisplayName = "IsFocussedOutlet",
            Name = "FocussedShop",
            IsMeasure = false,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown
        },
        new()
        {
            Attribute = "Time",
            DisplayName = "Order Date",
            Name = "Date",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsPivot = true
        },
        new()
        {
            Attribute = "Time",
            DisplayName = "Order Month",
            Name = "Month",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsPivot = true
        },
        new()
        {
            Attribute = "Time",
            DisplayName = "Order Week",
            Name = "Week",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.Unknown,
            IsPivot = true
        },
        new()
        {
            Attribute = "Time",
            DisplayName = "Invoice Date",
            Name = "InvoiceDate",
            IsMeasure = false,
            IsDimension = true,
            IsPivot = true
        },
        new()
        {
            Attribute = "Order",
            DisplayName = "TC",
            Name = "TC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Count
        },
        new()
        {
            Attribute = "Order",
            DisplayName = "PC",
            Name = "PC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Count
        },
        new()
        {
            Attribute = "Order",
            DisplayName = "UTC",
            Name = "UTC",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Order",
            DisplayName = linkNames.UPC,
            Name = "UPC",
            IsMeasure = true,
            IsDimension = true,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct,
            IsOtherMeasure = false
        },
        new()
        {
            Attribute = "Order",
            DisplayName = "Total Order Count",
            Name = "OrderCount",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Count
        },
        new()
        {
            Attribute = "Order",
            DisplayName = "SFA Order Count",
            Name = "SFAOrderCount",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Count
        },
        new()
        {
            Attribute = "Order",
            DisplayName = "Direct Order Count",
            Name = "DirectOrderCount",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Count
        },
        new()
        {
            Attribute = "Order",
            DisplayName = "Partial Order Count",
            Name = "PartialOrderCount",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Count
        },
        new()
        {
            Attribute = "Order",
            DisplayName = "Total Lines Cut (Order)",
            Name = "LinesCut_Order",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Order",
            DisplayName = "Avg Lines Cut (Order)",
            Name = "AvgLinesCut_Order",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Order",
            DisplayName = "Order Qty",
            Name = "OrderQtyInUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Order",
            DisplayName = "Order Qty" + linkNames.StdUnit,
            Name = "OrderQtyInStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Order",
            DisplayName = "Order Qty" + linkNames.SuperUnit,
            Name = "OrderQtyInSupUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Order",
            DisplayName = "Order Gross Value",
            Name = "OrderInRevenue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Order",
            DisplayName = "Order Scheme Qty (Unit)",
            Name = "SchemeQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Order",
            DisplayName = "Order Scheme Discount",
            Name = "SchemeDiscount",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Order",
            DisplayName = "Total Order Discount",
            Name = "OrderDiscount",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Order",
            DisplayName = "Order Net Value",
            Name = "NetValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Order",
            DisplayName = "Cancelled Order Qty" + linkNames.StdUnit,
            Name = "CancelledInStdUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Order",
            DisplayName = "Cancelled Order Qty" + linkNames.Unit,
            Name = "CancelledInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Order",
            DisplayName = "Cancelled Order Qty" + linkNames.SuperUnit,
            Name = "CancelledInSuperUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Order",
            DisplayName = "Cancelled Order Value",
            Name = "CancelledValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "InvoiceCount",
            Name = "InvoiceCount",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Invoice Qty" + linkNames.Unit,
            Name = "InvoiceQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Invoice Qty" + linkNames.StdUnit,
            Name = "InvoiceQtyInStdUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Invoice Qty" + linkNames.SuperUnit,
            Name = "InvoiceQtyInSuperUnit",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Invoice Gross Value",
            Name = "InvoiceValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Invoice Scheme Qty",
            Name = "InvoiceSchemeQty",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Invoice Scheme Discount",
            Name = "InvoiceSchemeDiscount",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Invoice Cash Discount",
            Name = "InvoiceCashDiscount",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Total Invoice Discount",
            Name = "InvoiceDiscount",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Invoice Net Value",
            Name = "InvoiceNetValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "UBO",
            Name = "UBO",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.CountDistinct
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Total Lines Cut (Sales)",
            Name = "LinesCut_Sales",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Sales Measure",
            DisplayName = "Avg Lines Cut (Sales)",
            Name = "AvgLinesCut_Sales",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Return",
            DisplayName = "Total Return Value",
            Name = "ReturnValue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Return",
            DisplayName = "Total Return Qty" + linkNames.StdUnit,
            Name = "ReturnQuantityInStdUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Return",
            DisplayName = "Total Return Qty" + linkNames.Unit,
            Name = "ReturnQuantityInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Return",
            DisplayName = "Total Return Qty" + linkNames.SuperUnit,
            Name = "ReturnQuantityInSuperUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Return",
            DisplayName = "Expired Return Value",
            Name = "ExpReturnInRevenue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Return",
            DisplayName = "Expired Return Qty " + linkNames.StdUnit,
            Name = "ExpReturnInStdUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Return",
            DisplayName = "Expired Return Qty " + linkNames.Unit,
            Name = "ExpReturnInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Return",
            DisplayName = "Expired Return Qty " + linkNames.SuperUnit,
            Name = "ExpReturnInSuperUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Return",
            DisplayName = "Damaged Return Value",
            Name = "DamReturnInRevenue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Return",
            DisplayName = "Damaged Return Qty" + linkNames.StdUnit,
            Name = "DamReturnInStdUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Return",
            DisplayName = "Damaged Return Qty" + linkNames.Unit,
            Name = "DamReturnInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Return",
            DisplayName = "Damaged Return Qty" + linkNames.SuperUnit,
            Name = "DamReturnInSuperUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Return",
            DisplayName = "Fresh Return Value",
            Name = "FreshReturnInRevenue",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Return",
            DisplayName = "Fresh Return Qty" + linkNames.StdUnit,
            Name = "FreshReturnInStdUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Return",
            DisplayName = "Fresh Return Qty" + linkNames.Unit,
            Name = "FreshReturnInUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        },
        new()
        {
            Attribute = "Return",
            DisplayName = "Fresh Return Qty" + linkNames.SuperUnit,
            Name = "FreshReturnInSuperUnits",
            IsMeasure = true,
            IsDimension = false,
            PerspectiveMeasure = PerspectiveMeasure.Sum
        }
    };
}
