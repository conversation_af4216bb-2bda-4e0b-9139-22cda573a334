﻿using System;
using Libraries.CommonEnums;

namespace Library.DateTimeHelpers;

public class DateRangeModel
{
    public DateTime? EndDate { get; set; }
    public DateTime? StartDate { get; set; }

    public bool IsValid()
    {
        return EndDate.HasValue && StartDate.HasValue && EndDate.Value >= StartDate.Value;
    }
}

public class DateTimeHelper
{
    public static DateRangeModel GetCurrentDateRangeForKPI(ComparisonTimePeriod comparisonTimePeriod,
        DateTime? endDate)
    {
        var returnvalue = new DateRangeModel();
        var today = endDate ?? DateTime.Now.AddDays(-1);
        switch (comparisonTimePeriod)
        {
            case ComparisonTimePeriod.Day:
                returnvalue.EndDate = endDate ?? DateTime.Now;
                returnvalue.StartDate = endDate ?? DateTime.Today;
                return returnvalue;

            case ComparisonTimePeriod.Week:
                returnvalue.EndDate = today;
                returnvalue.StartDate = today.AddDays(-7);
                return returnvalue;

            case ComparisonTimePeriod.Month:
                returnvalue.EndDate = today;
                returnvalue.StartDate = today.AddDays(1).AddMonths(-1);
                return returnvalue;

            case ComparisonTimePeriod.Unkown:
            default:
                break;
        }

        return returnvalue;
    }

    public static DateRangeModel GetDateRangeFromPreset(DateRangePreset dateRangePreset, DateTime? startDate = null,
        DateTime? endDate = null)
    {
        var returnvalue = new DateRangeModel();
        var today = endDate ?? DateTime.Now;
        switch (dateRangePreset)
        {
            case DateRangePreset.Last30Days:
            {
                returnvalue.EndDate = today;
                returnvalue.StartDate = today.AddDays(-30);
                return returnvalue;
            }
            case DateRangePreset.Last3Months:
            {
                returnvalue.EndDate = today;
                returnvalue.StartDate = today.AddMonths(-3);
                return returnvalue;
            }
            case DateRangePreset.Last7Days:
            {
                returnvalue.EndDate = today;
                returnvalue.StartDate = today.AddDays(-7);
                return returnvalue;
            }
            case DateRangePreset.Yesterday:
            {
                returnvalue.EndDate = today.AddDays(-1);
                returnvalue.StartDate = today.AddDays(-1);
                return returnvalue;
            }
            case DateRangePreset.Today:
            {
                returnvalue.EndDate = today;
                returnvalue.StartDate = today.Date;
                return returnvalue;
            }
            case DateRangePreset.MTD:
            case DateRangePreset.CustomDate:
            case DateRangePreset.Unknown:
            default:
            {
                returnvalue.StartDate = startDate;
                returnvalue.EndDate = endDate;
                return returnvalue;
            }
        }
    }

    public static DateRangeModel GetPreviousDateRangeForKPI(ComparisonType comparisonType, DateTime? endDate)
    {
        var returnvalue = new DateRangeModel();
        var today = endDate ?? DateTime.Now.AddDays(-1);
        switch (comparisonType)
        {
            case ComparisonType.Yesterday:
                returnvalue.EndDate = today;
                returnvalue.StartDate = today;
                return returnvalue;

            case ComparisonType.LastWeek:
                returnvalue.EndDate = today.AddDays(-7);
                returnvalue.StartDate = today.AddDays(-14);
                return returnvalue;

            case ComparisonType.LastMonth:
                returnvalue.EndDate = today.AddMonths(-1);
                returnvalue.StartDate = today.AddDays(1).AddMonths(-2);
                return returnvalue;

            case ComparisonType.LastMonthCurrentWeek:
                returnvalue.EndDate = today.AddMonths(-1);
                returnvalue.StartDate = today.AddDays(-7).AddMonths(-1);
                return returnvalue;

            case ComparisonType.LastMonthCurrentDay:
                returnvalue.EndDate = today.AddMonths(-1);
                returnvalue.StartDate = DateTime.Today.AddMonths(-1);
                return returnvalue;
            //case ComparisonType.ThisMonthAverage:
            //    returnvalue.EndDate = today;
            //    returnvalue.StartDate = today.AddMonths(-1);
            //    return returnvalue;
            case ComparisonType.Unkown:
            default:
                break;
        }

        return returnvalue;
    }
}

public class MTD_LMTD
{
    public DateRangeModel LMTD { get; set; }
    public DateRangeModel MTD { get; set; }
}
