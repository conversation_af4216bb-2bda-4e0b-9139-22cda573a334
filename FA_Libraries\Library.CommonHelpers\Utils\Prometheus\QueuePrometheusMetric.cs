﻿using Prometheus;

namespace Library.CommonHelpers.Utils.Prometheus;

public static class QueuePrometheusMetric
{
    private static readonly double[] BucketList = { 0.02, 0.05, 0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10, 20, 30 };

    private static readonly Histogram QueueProcessingTimeHistogram = Metrics
        .CreateHistogram("queue_processing_duration_seconds", "Queue message processing duration in seconds",
            new HistogramConfiguration { Buckets = BucketList });

    public static void ObserveQueueProcessingTime(double duration)
    {
        QueueProcessingTimeHistogram.Observe(duration);
    }
}
