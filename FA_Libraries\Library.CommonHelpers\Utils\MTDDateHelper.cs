﻿using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Library.DateTimeHelpers;

namespace Library.CommonHelpers.Utils;

public interface IMTDDateHelper
{
    Task<FA_MTD_LMTD> GetMTDLMTD(long companyId, DateTime date, int? yearStartMonth = null,
        bool includeToday = false);
}

public class MTDDateHelper : IMTDDateHelper
{
    private readonly AppConfigSettings appConfigSettings;

    public MTDDateHelper(AppConfigSettings appConfigSettings)
    {
        this.appConfigSettings = appConfigSettings;
    }

    public async Task<FA_MTD_LMTD> GetMTDLMTD(long companyId, DateTime date, int? yearStartMonth = null,
        bool includeToday = false)
    {
        var yearStartMonthParameter = yearStartMonth == null ? "" : $"&yearStartMonth={yearStartMonth.Value}";
        using (var client = new HttpClient())
        {
            client.DefaultRequestHeaders.Authorization =
                new AuthenticationHeaderValue("Bearer", appConfigSettings.reportApiToken);

            var dataUrl =
                $"{appConfigSettings.reportApiBaseUrl}api/MTDLMTDDate/GetMTDLMTD?companyId={companyId}&today={date.ToString("MM/dd/yyyy")}&includeToday={includeToday}{yearStartMonthParameter}";

            var data = await client.GetFromJsonAsync<FA_MTD_LMTD>(dataUrl);
            return data;
        }
    }
}

public class AppConfigSettings
{
    public string StorageConnectionString { get; set; }

    public string reportApiBaseUrl { get; set; }

    public string reportApiToken { get; set; }
}
