﻿using FAEngage.Core.Models.TransactionDbModels;
using FAEngage.Core.TransactionRepositories;
using FAEngage.DbStorage.DbContexts;
using Microsoft.EntityFrameworkCore;

namespace FAEngage.DbStorage.TransactionRepositories;

public class FAEngageTransactionRepository(
    WritableTransactionDbContext writableTransactionDbContext,
    TransactionDbContext transactionDbContext)
    : IFaEngageTransactionRepository
{
    public async Task AddEngageLogToDb(FAEngageLog fAEngageLog)
    {
        await writableTransactionDbContext.FAEngageLogs.AddAsync(fAEngageLog);
        await writableTransactionDbContext.SaveChangesAsync();
    }

    public async Task AddWhatsAppEngageLogToDb(FAEngageLogWhatsapp fAEngageLog)
    {
        await writableTransactionDbContext.FAEngageLogWhatsapps.AddAsync(fAEngageLog);
        await writableTransactionDbContext.SaveChangesAsync();
    }

    public async Task AddAnalyticsEngageLogToDb(FAManagerEngageLog fAEngageLog)
    {
        await writableTransactionDbContext.FAManagerEngageLogs.AddAsync(fAEngageLog);
        await writableTransactionDbContext.SaveChangesAsync();
    }

    public async Task<Guid> GetLastTransaction(long notificationId, long companyId, DateTimeOffset startTime, DateTimeOffset endTime)
    {
        return await transactionDbContext.FAEngageLogs
            .Where(s => s.NotificationId == notificationId
                        && s.CompanyId == companyId && s.SendTime >= startTime
                        && s.SendTime <= endTime)
            .OrderByDescending(s => s.Id)
            .Select(s => s.NotificationTransactionId)
            .FirstOrDefaultAsync();
    }

    public async Task<List<FAEngageLog>> GetLogsForTransactionId(long companyId, Guid lastTransactionId)
    {
        return await transactionDbContext.FAEngageLogs
            .Where(s => s.CompanyId == companyId
                        && s.NotificationTransactionId == lastTransactionId)
            .ToListAsync();
    }

    public async Task UpdateSendMessageForEngageLogs(Guid sendId, Guid transactionNotificationId, string message, int statusCode)
    {
        var data = await writableTransactionDbContext
            .FAEngageLogs
            .Where(s => s.NotificationTransactionId == transactionNotificationId && s.SentId == sendId)
            .ToListAsync();

        if (data.Count > 0)
        {
            foreach (var item in data)
            {
                item.SentMessage = message;
                item.IsFcmSent = statusCode <= 299;
            }
        }

        var engageManagerLogs = await writableTransactionDbContext
            .FAManagerEngageLogs
            .Where(s => s.NotificationTransactionId == transactionNotificationId && s.SentId == sendId)
            .ToListAsync();

        if (engageManagerLogs?.Count > 0)
        {
            foreach (var item in engageManagerLogs)
            {
                item.SentMessage = message;
                item.IsFcmSent = statusCode <= 299;
            }
        }

        await writableTransactionDbContext.SaveChangesAsync();
    }
}