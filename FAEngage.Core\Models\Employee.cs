﻿using Libraries.CommonEnums;

namespace FAEngage.Core.Models;

public class Employee
{
    public long Id { get; set; }
    public Guid GuId { get; set; }
    public string Name { get; set; }
    public string EmailId { get; set; }

    public string LocalName { get; set; }

    public string ErpId { get; set; }
    public string ReportingManager { get; set; }
    public string ReportingManagerErpId { get; set; }
    public string UserRank { get; set; }
    public DateTime? DateOfJoining { get; set; }
    public UserActiveStatus UserStatus { get; set; }
    public EmployeeRank Rank { get; set; }
    public EmployeeType EmployeeType { set; get; }
    public string ContactNo { get; set; }
    public PortalUserRole UserRole { get; set; }
    public DateTime? DateOfLeaving { get; set; }
    public DateTime DateOfCreation { get; set; }
    public string Region { get; set; }
    public string Zone { get; set; }

    public long? L5GeographyId { get; set; }
    public long OldTableId { get; set; }
    public long? DesignationId { get; set; }
    public bool IsActive { get; set; }
    public long CompanyId { get; set; }
    public long? AreaSalesManagerId { get; set; }
    public long? RegionId { get; set; }
    public bool IsDeactive { get; set; }
    public string EmployeeAttributeText1 { get; set; }
    public string EmployeeAttributeText2 { get; set; }
    public bool? EmployeeAttributeBoolean1 { get; set; }
    public bool? EmployeeAttributeBoolean2 { get; set; }
    public double? EmployeeAttributeNumber1 { get; set; }
    public double? EmployeeAttributeNumber2 { get; set; }
    public DateTime? EmployeeAttributeDate { get; set; }
    public bool IsFieldAppuser { get; set; }
    public string SecondaryEmailId { get; set; }
    public string ClientSideId { get; set; }
    public string Type { get; set; }
}