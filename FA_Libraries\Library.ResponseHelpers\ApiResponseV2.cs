﻿using Serilog;

namespace Library.ResponseHelpers;

public class ApiResponseV2<T>
{
    public static ApiResponseV2<T> GetSuccess(string message = "Success")
    {
        return new ApiResponseV2<T> { Message = message, ResponseStatus = ResponseStatus.Success };
    }

    public static ApiResponseV2<T> GetFailure(Exception ex, string message)
    {
        Log.Error(ex, message);
        return new ApiResponseV2<T> { Message = message, ResponseStatus = ResponseStatus.Failure };
    }

    public ApiResponseV2(string taskName = null)
    {
        ResponseStatus = ResponseStatus.Ignored;
        Message = "";
        ResponseList = new List<ApiResponseMessageV2<T>>();
        ResponseStatusCount = new ResponseStatusCount(taskName);
    }

    /// <summary>
    /// Message Indicating the Overall Response Summary of the Requested API
    /// </summary>
    public string Message { get; set; }

    /// <summary>
    /// List of Errors
    /// </summary>
    public List<ApiResponseMessageV2<T>> ResponseList { get; set; }

    /// <summary>
    /// Enum Indicating the Response status
    /// </summary>
    public ResponseStatus ResponseStatus { get; set; }

    /// <summary>
    /// Summary containing Number of SuccessFull And Failed Tasks
    /// </summary>
    public ResponseStatusCount ResponseStatusCount { get; set; }

    public bool IsSuccess()
    {
        return ResponseStatus == ResponseStatus.Success || ResponseStatus == ResponseStatus.PartialSuccess;
    }

    public void CalculateResponse()
    {
        Message = ResponseStatusCount.StatusMessage;
        ResponseStatus = ResponseStatusCount.ResponseStatus;
        ResponseList = ResponseList.OrderBy(r => r.Success).ToList();
    }

    public static ApiResponseV2<T> operator +(ApiResponseV2<T> a, ApiResponseV2<T> b)
    {
        var total = new ApiResponseV2<T> { ResponseStatusCount = a.ResponseStatusCount + b.ResponseStatusCount, Message = (!string.IsNullOrWhiteSpace(a.Message) ? a.Message + ";" : "") + b.Message };
        total.ResponseList.AddRange(a.ResponseList);
        total.ResponseList.AddRange(b.ResponseList);
        if (a.ResponseStatus == ResponseStatus.Ignored && b.ResponseStatus == ResponseStatus.Ignored)
        {
            total.ResponseStatus = ResponseStatus.Ignored;
        }
        else if (a.IsSuccess() && b.IsSuccess())
        {
            total.ResponseStatus = ResponseStatus.Success;
        }
        else if ((a.ResponseStatus == ResponseStatus.Failure && b.ResponseStatus != ResponseStatus.Success) || (b.ResponseStatus == ResponseStatus.Failure && a.ResponseStatus != ResponseStatus.Success))
        {
            total.ResponseStatus = ResponseStatus.Failure;
        }
        else
        {
            total.ResponseStatus = ResponseStatus.PartialSuccess;
        }

        return total;
    }
}

/// <summary>
/// Base for Error and Success Messages
/// </summary>
public class ApiResponseMessageV2<T>
{
    public ApiResponseMessageV2(T value, string message, bool success)
    {
        Message = message;
        Success = success;
        Entities = value;
    }

    /// <summary>
    /// Error Message for the indicated ERPID
    /// </summary>
    public string Message { get; set; }

    /// <summary>
    /// Status of the action requested from Api
    /// </summary>
    public bool Success { get; set; }

    public T Entities { get; set; }
}
