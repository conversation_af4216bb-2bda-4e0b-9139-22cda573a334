﻿using Libraries.CommonEnums;
using Libraries.CommonEnums.Helpers;
using Library.NumberSystem;

namespace FAEngage.Core.Helpers;

public class CompanySettings(Dictionary<string, object> settings)
{
    private string GetSettingByKey(string key)
    {
        if (settings.TryGetValue(key, out var setting))
        {
            return setting.ToString();
        }

        return null;
    }

    private T GetSetting<T>(string key, T defaultValue)
    {
        try
        {
            var data = GetSettingByKey(key);
            if (!string.IsNullOrEmpty(data))
            {
                return (T)Convert.ChangeType(Convert.ChangeType(data, data.GetType()), typeof(T));
            }

            return defaultValue;
        }
        catch (Exception)
        {
            return defaultValue;
        }
    }

    private T GetSettingEnum<T>(string key, T defaultValue) where T : struct
    {
        try
        {
            var result = defaultValue;
            var value = GetSettingByKey(key).Replace(" ", "");
            if (!string.IsNullOrEmpty(value))
            {
                Enum.TryParse(value, out result);
            }

            return result;
        }
        catch (Exception)
        {
            return defaultValue;
        }
    }

    private PositionCodeLevel GetSettingEnumForPositionCodeLevel(string key, PositionCodeLevel defaultValue)
    {
        try
        {
            var result = defaultValue;
            var value = PositionRoleExtension.PositionCodeLevelFromPositionLevel(GetSettingByKey(key));
            if (!string.IsNullOrEmpty(value))
            {
                Enum.TryParse(value, out result);
            }

            return result;
        }
        catch (Exception)
        {
            return defaultValue;
        }
    }

    public JourneyPlanType GetJourneyPlanType => GetSettingEnum("JourneyPlanType", JourneyPlanType.Default);
    public bool IsUsesNewJourneyPlan => GetSetting("JourneyPlanVersion", "Default") == "New Journey Plan";
    public TimeSpan TimeZoneOffset => TimeSpan.FromMinutes(GetSetting("TimeZoneOffsetMinutes", 330));
    public bool TertiaryOffTakeManual => GetSetting("tertiaryOfftakeManual", false);
    public bool CompanyUsesMonthWiseOpening => GetSetting("UsesMonthWiseOpeningStockMT", false);
    public string GetCountryName => GetSetting("Country", string.Empty);
    public PortalUserRole GetCompanyHighestHierarchy => GetSettingEnum("CompanyUsesHighestHierarchy", PortalUserRole.GlobalSalesManager);
    public PositionCodeLevel GetCompanyHighestPositionLevel => GetSettingEnumForPositionCodeLevel("HighestPositionLevel", PositionCodeLevel.L4Position);
    public string TargetValueType => GetSetting("TargetValueType", "Revenue");
    public TargetValueType OutletTargetOn => GetSettingEnum("Outlettargeton", Libraries.CommonEnums.TargetValueType.Revenue);
    public string BeatTargetValueType => GetSetting("Beatwisetargetvaluetype", "Revenue");
    public CompanySeesDataInEnum CompanySeesDataIn => GetSettingEnum("SalesDataVisibilityMode", CompanySeesDataInEnum.SuperUnit);
    public int MonthStartDate => GetSetting("monthStartDate", 1);
    public int YearStartMonth => GetSetting("yearStartMonth", 4);
    public bool UsesSalesInAmount => GetSetting("UsesSalesinQuantity", false);
    public bool UsesSecondarySales => GetSetting("usesSecondarySales", false);
    public bool CompanyUsesDMS => GetSetting("CompanyUsesDMS", false);
    public bool UsingIntelligentScheme => GetSetting("usesIntelligentSchemes", false);
    public bool UsesJourneyCalendar => GetSetting("UsesJourneyCalendar", false);
    public bool UsesProductDivision => GetSetting("usesDistProdDivBeatMappings", false);
    public bool usesReverseGeocodes => GetSetting("usesReverseGeocodes", false);
    public string CurrencySymbol => GetSetting("CurrencySymbol", "₹");
    public string CalculateAchFrom => GetSetting("CalculateAchFrom", "NotApplicable");
    public bool FetchOutletPerformanceDataFrom3rdPartyDMS => GetSetting("FetchOutletPerformancedatafrom3rdPartyDMS", false);
    public string OrderBookingScreenType => GetSetting("AppOrderBookingScreenType", "Main");
    public int AssortedProductFlagDays => GetSetting("assortedProductFlagDays", 30);
    public bool UsesDistProdDivBeatMappings => GetSetting("usesDistProdDivBeatMappings", false);
    public bool UsesScheduledCallProductivity => GetSetting("ScheduledCallProductivity", false);
    public int TargetType => GetSetting("TargetsType", 1);
    public bool UsesAddOutletRequest => GetSetting("CompanyUsesAddOutletRequest", false);
    public TypeofDistributorMapping TypeofDistributorMapping => GetSettingEnum("TypeofDistributorMapping", TypeofDistributorMapping.BeatDistributor);
    public string PrimaryTargetsOn => GetSetting("PrimaryTargetsOn", "No Targets");
    public List<string> GetRejectionReasons => GetSetting("ReasonForOutletRequestRejection", new List<string>());
    public int DigitsInPhNo => GetSetting("DigitsInPhNo", 10);
    public int NumberOfDaysFastMovingCalculation => GetSetting("numberofDaysFastMovingCalculation", 30);
    public NumberSystems NumberSystem => GetSettingEnum("NumberSystem", NumberSystems.Indian);
    public EmployeeTargetsCalculationType CalculateAchievementAgainstEmployeeTargets => GetSettingEnum("CalculateAchievementAgainstEmployeeTargets", EmployeeTargetsCalculationType.Order);
    public bool ShouldCalculateBeatLength => GetSetting("allowcompaniestocalculateBeatLength", false);
    public double CompanyPricePerUser => GetSetting("PricePerUser", 0.0);
    public int MinimumBillingUser => GetSetting("MinimumBillingUser", 0);
    public bool UserUsesNSApp => GetSetting("UserUsesNSApp", false);
    public bool UsesSingleApprovalForOutletAdditionRequest => GetSetting("UsesSingleApprovalForOutletAdditionRequest", false);
    public string FABattleGround => GetSetting("FABattleGround", "Not Applicable");
    public string BillingType => GetSetting("BillingType", "Monthly");
    public string TargetOn => GetSetting("TargetOn", "Overall");
    public string BeatTargetOn => GetSetting("Beatwisetargeton", "Overall");
    public bool UsesPaymentFeature => GetSetting("usesPaymentFeature", false);
    public bool UsesPositionCodes => GetSetting("UsesPositionCodes", false);
    public bool UsesNewDashboard => GetSetting("CompanyUsesNewDashboard", false);
    public bool SeeReportOnBasisOfPositionCode => GetSetting("SeeReportOnBasisOfPositionCode", false);
    public bool UseGeographicalMappingOfOutlets => GetSetting("AllowGeographicalMappingOfOutlets", false);
    public GeographyLevel CompanyHighestGeography => GetSettingEnum("HighestGeoHierarchy", GeographyLevel.Level4);
    public bool usesLast10Invoices => GetSetting("usesLastThirtyDayInvoice", false);
    public long? WFHSurveyId => GetSetting("WFHSurveyId", (long?)0);
    public bool UsesThirdPartyAPIForInvoiceAchievement => GetSetting("UsethirdPartyAPIforInvoiceAchievement", false);
    public bool UsesZonalJourneyCycle => GetSetting("UsesZonalJourneyCycle", false);
    public JourneyPlanVersion JourneyPlanVersion => GetSettingEnum("JourneyPlanVersion", JourneyPlanVersion.OldJourneyPlan);
    public bool IsUsingNewJourneyPlanStructure => JourneyPlanVersion == JourneyPlanVersion.NewJourneyPlan;
    public bool IsBeatForNewJourneyPlanStructure => JourneyPlanningEntity == JourneyPlanningEntity.Beat;
    public bool IsRouteForNewJourneyPlanStructure => JourneyPlanningEntity == JourneyPlanningEntity.Route;
    public JourneyPlanningEntity JourneyPlanningEntity => GetSettingEnum("JourneyPlanningEntity", JourneyPlanningEntity.Beat);
    public bool IsCompanyUsesAttendanceBasedTADA => GetSetting("CompanyUsesAttendanceBasedTADA", false);
    public bool IsCompanyUsesNormBasedAttendance => GetSetting("UsesNewAttendanceModule", false);
    public ShowEmployeeSalesIn ShowEmployeeSalesIn => GetSettingEnum("ShowEmployeeSalesIn", ShowEmployeeSalesIn.NetValue);
    public bool IsCallReviewAllowed => GetSetting("callReviewAllowed", false);
    public bool DownloadReportInExcel => GetSetting("DownloadReportInExcel", false);
    public bool IsUnifyEnabled => GetSetting("UsesFAUnify", false);
    public bool UsesEngageWhatsappIntegration => GetSetting("CompanyUsesEngageWhatsappIntegration", false);
    public bool E11Company => GetSetting("E11Company", false);
    public bool IsCompanyUsesHCCBUserFlows => GetSetting("CompanyUsesHCCBUserFlows", false);
}