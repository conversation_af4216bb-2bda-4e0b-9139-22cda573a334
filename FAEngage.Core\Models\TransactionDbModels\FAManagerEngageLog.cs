﻿namespace FAEngage.Core.Models.TransactionDbModels;

public class FAManagerEngageLog
{
    public long Id { get; set; }

    public long CompanyId { get; set; }

    public long NotificationId { get; set; }

    public long UserId { get; set; }

    public DateTimeOffset SendTime { get; set; }

    public DateTime CreatedAt { get; set; }

    public bool IsReadByUser { get; set; }

    public DateTimeOffset? ReadByUserTime { get; set; }

    public bool IsSentFromBackend { get; set; }

    public string SentMessage { get; set; }

    public Guid SentId { get; set; }

    public Guid NotificationTransactionId { get; set; }

    public long? AlertId { get; set; }

    public string PositionCodeIds { get; set; }

    public bool IsNotAggregated { get; set; }

    public bool IsFcmSent { get; set; }
}