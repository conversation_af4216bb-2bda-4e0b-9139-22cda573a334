﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using CsvHelper;
using FileGenerator.Attributes;
using FileGenerator.HelperModels;
using FileGenerator.Interfaces;

namespace FileGenerator;

public class CsvGeneratorNew
{
    private readonly bool _showAllColumns;
    private readonly bool _useNomenclature;
    private readonly Dictionary<string, object> companySettings;
    private readonly bool isSettingEnabled;
    private readonly CompanyNomenclatureSpecifier nomenclatureSpecifier;
    private readonly Dictionary<string, bool> selectedCols;
    private readonly Dictionary<string, TableFieldAttribute> colAttributeDic;

    public CsvGeneratorNew(Dictionary<string, TableFieldAttribute> colAttributeDic = null, CompanyNomenclatureSpecifier nomenclatureSpecifier = null, bool showAllColumns = false,
        bool isSettingEnabled = false, Dictionary<string, object> companySettings = null,
        Dictionary<string, bool> selectedCols = null)
    {
        this.nomenclatureSpecifier = nomenclatureSpecifier;
        _showAllColumns = showAllColumns;
        _useNomenclature = nomenclatureSpecifier != null;
        this.isSettingEnabled = isSettingEnabled;
        this.companySettings = companySettings;
        this.selectedCols = selectedCols;
        this.colAttributeDic = colAttributeDic;
    }

    public bool MakeCSV(CreateExcelProperties props, Stream stream, bool onlyExcelAttributes = true)
    {
        //Use UTF8 Encoding for correctly showing special Characters
        TextWriter writeStream = new StreamWriter(stream, new UTF8Encoding(true));
        var csvWriter = new CsvWriter(writeStream, CultureInfo.InvariantCulture);

        var showDataForColumns = new List<string>();
        var colNo = 0;
        foreach (var colProp in props.columnsOrder)
        {
            //derivedMeasures Handling
            if (!colAttributeDic.ContainsKey(colProp))
            {
                if (props.derivedkpis.Contains(colProp))
                {
                    csvWriter.WriteField(colProp);
                    showDataForColumns.Add(colProp);
                }

                continue;
            }

            var colAttribute = colAttributeDic[colProp];
            if (colAttribute == null)
            {
                continue;
            }

            var columnDisplayName = colAttribute.ColumnName;
            if (_useNomenclature && colAttribute.NomenclatureRequirement)
            {
                var listforheader = columnDisplayName.Split(' ').ToList();
                columnDisplayName = string.Join(" ", listforheader.Select(s => nomenclatureSpecifier.GetHeaderName(s)));
            }

            if (_showAllColumns && !(colAttribute.ColumnRequirement == Requirement.SettingBased && !CheckIfColRequired(colProp, props.dataList)))
            {
                csvWriter.WriteField(columnDisplayName);
                showDataForColumns.Add(colProp);
            }
            else if (!CheckIfColNullNew(colProp, props.dataList))
            {
                csvWriter.WriteField(columnDisplayName);
                showDataForColumns.Add(colProp);
            }

            colNo++;
        }

        long flushSize = 100000;
        var flushPosition = flushSize;
        var itemNo = 1;

        foreach (var item in props.dataList)
        {
            csvWriter.NextRecord();
            colNo = 0;
            foreach (var colProp in showDataForColumns)
            {
                var dataValue = item[colProp];
                if (dataValue != null)
                {
                    if (dataValue.GetType() == typeof(DateTime) && colAttributeDic[colProp] != null)
                    {
                        var dataStr = "";
                        var attr = colAttributeDic[colProp];
                        if (!string.IsNullOrWhiteSpace(attr.CellFormat))
                        {
                            dataStr = ((DateTime)dataValue).ToString(attr.CellFormat);
                        }
                        else
                        {
                            dataStr = dataValue.ToString();
                        }

                        csvWriter.WriteField(dataStr);
                    }
                    else
                    {
                        csvWriter.WriteField(dataValue);
                    }
                }
                else
                {
                    csvWriter.WriteField("");
                }

                colNo++;
            }

            if (itemNo > flushPosition)
            {
                writeStream.Flush();
                stream.Flush();
                flushPosition += flushSize;
            }

            itemNo++;
        }

        csvWriter.NextRecord();
        writeStream.Flush();
        stream.Flush();
        if (stream.CanSeek)
        {
            stream.Position = 0;
        }

        return true;
    }

    /// <summary>
    /// copy one csv stream to other by removing delete columns
    /// </summary>
    /// <param name="inputStream"></param>
    /// <param name="outputStream"></param>
    /// <param name="columnsToRemove"></param>
    /// <param name="batchSize"></param>
    public static void CopyCsvWithoutDeleteColsInOtherStream(Stream inputStream, Stream outputStream, List<string> columnsToRemove, int batchSize = 50000)
    {
        inputStream.Flush();
        inputStream.Position = 0;
        using (var reader = new StreamReader(inputStream, new UTF8Encoding(true)))
        using (var csvReader = new CsvReader(reader, CultureInfo.InvariantCulture))
        using (var writer = new StreamWriter(outputStream, new UTF8Encoding(true), leaveOpen: true))
        using (var csvWriter = new CsvWriter(writer, CultureInfo.InvariantCulture))
        {
            if (csvReader.Read() && csvReader.ReadHeader())
            {
                var headers = csvReader.HeaderRecord.ToList();
                var filteredHeaders = headers.Except(columnsToRemove).ToList();

                //new headers
                foreach (var header in filteredHeaders)
                {
                    csvWriter.WriteField(header);
                }

                csvWriter.NextRecord();

                // Process the CSV in batches
                var records = new List<dynamic>();
                var recordCount = 0;

                while (csvReader.Read())
                {
                    var record = csvReader.GetRecord<dynamic>();
                    recordCount++;

                    // Remove columnsToRemove
                    if (record is IDictionary<string, object> recordDict)
                    {
                        foreach (var column in columnsToRemove)
                        {
                            recordDict.Remove(column);
                        }
                    }

                    records.Add(record);

                    //batch writing
                    if (recordCount % batchSize == 0)
                    {
                        WriteCsv(csvWriter, records, filteredHeaders);
                        records.Clear();
                    }
                }

                // Write any remaining records
                if (records.Any())
                {
                    WriteCsv(csvWriter, records, filteredHeaders);
                }
            }
        }
    }

    /// <summary>
    /// write records in csv
    /// </summary>
    /// <param name="csvWriter"></param>
    /// <param name="records"></param>
    /// <param name="headers"></param>
    public static void WriteCsv(CsvWriter csvWriter, List<dynamic> records, List<string> headers)
    {
        foreach (var record in records)
        {
            var recordDict = record as IDictionary<string, object>;

            foreach (var header in headers)
            {
                if (recordDict.ContainsKey(header))
                {
                    csvWriter.WriteField(recordDict[header]);
                }
                else
                {
                    csvWriter.WriteField(string.Empty);
                }
            }

            csvWriter.NextRecord();
        }
    }

    private bool CheckIfColRequired(string key, List<Dictionary<string, object>> listOfData)
    {
        if (isSettingEnabled)
        {
            foreach (var item in listOfData)
            {
                if (item[key] != null && !string.IsNullOrWhiteSpace(item[key].ToString()))
                {
                    return true;
                }

                if (_showAllColumns)
                {
                    return true;
                }
            }

            return false;
        }

        return false;
    }

    private static bool CheckIfColNullNew(string key, List<Dictionary<string, object>> listOfData)
    {
        foreach (var item in listOfData)
        {
            if (item.ContainsKey(key) && item[key] != null && !string.IsNullOrWhiteSpace(item[key].ToString()))
            {
                return false;
            }
        }

        return true;
    }
}
