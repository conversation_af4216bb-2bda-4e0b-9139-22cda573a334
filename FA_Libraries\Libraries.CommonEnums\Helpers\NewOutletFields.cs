﻿using System.Collections.Generic;

namespace Libraries.CommonEnums.Helpers;

public class NewOutletField
{
    public static List<NewOutletFields> OutletFields =>
        new()
        {
            //Outlet Info
            new NewOutletFields
            {
                DisplayName = "Outlet Name",
                Field = NewOutletFieldsForApp.OutletName,
                IsMandatory = true,
                IsVisible = true,
                DisplayOrder = 10
            },
            new NewOutletFields
            {
                DisplayName = "Address",
                Field = NewOutletFieldsForApp.Address,
                IsMandatory = true,
                IsVisible = true,
                DisplayOrder = 20
            },
            new NewOutletFields
            {
                DisplayName = "Owner's Name",
                Field = NewOutletFieldsForApp.OwnersName,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 30
            },
            new NewOutletFields
            {
                DisplayName = "Contact Number",
                Field = NewOutletFieldsForApp.ContactNo,
                IsMandatory = true,
                IsVisible = true,
                DisplayOrder = 40
            },
            //Outlet Location
            new NewOutletFields
            {
                DisplayName = "Beat Name",
                Field = NewOutletFieldsForApp.BeatId,
                IsMandatory = true,
                IsVisible = true,
                DisplayOrder = 50
            },
            new NewOutletFields
            {
                DisplayName = "Market Name",
                Field = NewOutletFieldsForApp.MarketName,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 60
            },
            new NewOutletFields
            {
                DisplayName = "District",
                Field = NewOutletFieldsForApp.District,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 70
            },
            new NewOutletFields
            {
                DisplayName = "City",
                Field = NewOutletFieldsForApp.City,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 80
            },
            new NewOutletFields
            {
                DisplayName = "State",
                Field = NewOutletFieldsForApp.State,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 90
            },
            new NewOutletFields
            {
                DisplayName = "PinCode",
                Field = NewOutletFieldsForApp.PinCode,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 100
            },
            //Outlet/Owner Email
            new NewOutletFields
            {
                DisplayName = "Email",
                Field = NewOutletFieldsForApp.Email,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 110
            },
            new NewOutletFields
            {
                DisplayName = "Secondary Email",
                Field = NewOutletFieldsForApp.SecondaryEmail,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 120
            },
            //Outlet Segmentation Attribute
            new NewOutletFields
            {
                DisplayName = "Channel",
                Field = NewOutletFieldsForApp.ChannelId,
                IsMandatory = true,
                IsVisible = true,
                DisplayOrder = 130
            },
            new NewOutletFields
            {
                DisplayName = "ShopType",
                Field = NewOutletFieldsForApp.ShopTypeId,
                IsMandatory = false,
                IsVisible = true,
                DisplayOrder = 140
            },
            new NewOutletFields
            {
                DisplayName = "Segmentation",
                Field = NewOutletFieldsForApp.Segmentation,
                IsMandatory = true,
                IsVisible = true,
                DisplayOrder = 150
            },
            new NewOutletFields
            {
                DisplayName = "ERPId",
                Field = NewOutletFieldsForApp.ERPId,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 160
            },
            //Outlet Image
            new NewOutletFields
            {
                DisplayName = "Image",
                Field = NewOutletFieldsForApp.ImageId,
                IsMandatory = false,
                IsVisible = true,
                DisplayOrder = 170
            },
            //Outlet Tax and Accounts
            new NewOutletFields
            {
                DisplayName = "PAN",
                Field = NewOutletFieldsForApp.PAN,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 180
            },
            new NewOutletFields
            {
                DisplayName = "Aadhar",
                Field = NewOutletFieldsForApp.Aadhar,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 190
            },
            new NewOutletFields
            {
                DisplayName = "Bank Account Number",
                Field = NewOutletFieldsForApp.BankAccountNumber,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 200
            },
            new NewOutletFields
            {
                DisplayName = "Account Holders Name",
                Field = NewOutletFieldsForApp.AccountHoldersName,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 210
            },
            new NewOutletFields
            {
                DisplayName = "IFSC Code",
                Field = NewOutletFieldsForApp.IFSCCode,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 220
            },
            new NewOutletFields
            {
                DisplayName = "GST Registered",
                Field = NewOutletFieldsForApp.GSTRegistered,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 230
            },
            new NewOutletFields
            {
                DisplayName = "GSTIN",
                Field = NewOutletFieldsForApp.GSTIN,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 240
            },
            //In case of MT
            new NewOutletFields
            {
                DisplayName = "Place Of Delivery",
                Field = NewOutletFieldsForApp.PlaceOfDelivery,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 250
            },
            //Extras
            new NewOutletFields
            {
                DisplayName = "AttributeText1",
                Field = NewOutletFieldsForApp.AttributeText1,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 260
            },
            new NewOutletFields
            {
                DisplayName = "AttributeText2",
                Field = NewOutletFieldsForApp.AttributeText2,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 270
            },
            new NewOutletFields
            {
                DisplayName = "AttributeText3",
                Field = NewOutletFieldsForApp.AttributeText3,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 280
            },
            new NewOutletFields
            {
                DisplayName = "AttributeText4",
                Field = NewOutletFieldsForApp.AttributeText4,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 290
            },
            new NewOutletFields
            {
                DisplayName = "AttributeNumber1",
                Field = NewOutletFieldsForApp.AttributeNumber1,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 300
            },
            new NewOutletFields
            {
                DisplayName = "AttributeNumber2",
                Field = NewOutletFieldsForApp.AttributeNumber2,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 310
            },
            new NewOutletFields
            {
                DisplayName = "AttributeNumber3",
                Field = NewOutletFieldsForApp.AttributeNumber3,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 320
            },
            new NewOutletFields
            {
                DisplayName = "AttributeNumber4",
                Field = NewOutletFieldsForApp.AttributeNumber4,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 330
            },
            new NewOutletFields
            {
                DisplayName = "AttributeBoolean1",
                Field = NewOutletFieldsForApp.AttributeBoolean1,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 340
            },
            new NewOutletFields
            {
                DisplayName = "AttributeBoolean2",
                Field = NewOutletFieldsForApp.AttributeBoolean2,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 350
            },
            new NewOutletFields
            {
                DisplayName = "AttributeDate1",
                Field = NewOutletFieldsForApp.AttributeDate1,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 360
            },
            new NewOutletFields
            {
                DisplayName = "AttributeDate2",
                Field = NewOutletFieldsForApp.AttributeDate2,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 370
            },
            new NewOutletFields
            {
                DisplayName = "AttributeImage1",
                Field = NewOutletFieldsForApp.AttributeImage1Id,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 380
            },
            new NewOutletFields
            {
                DisplayName = "AttributeImage2",
                Field = NewOutletFieldsForApp.AttributeImage2Id,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 390
            },
            new NewOutletFields
            {
                DisplayName = "AttributeImage3",
                Field = NewOutletFieldsForApp.AttributeImage3Id,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 400
            },
            // Some more discrimination
            new NewOutletFields
            {
                DisplayName = "Consumer Type",
                Field = NewOutletFieldsForApp.ConsumerType,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 410
            },
            new NewOutletFields
            {
                DisplayName = "Segmentation Scope",
                Field = NewOutletFieldsForApp.SegmentationScope,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 420
            },
            //Optional Unknown fields
            new NewOutletFields
            {
                DisplayName = "Is Asset Present",
                Field = NewOutletFieldsForApp.IsAssetPresent,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 430
            },
            new NewOutletFields
            {
                DisplayName = "Asset Code",
                Field = NewOutletFieldsForApp.AssetCode,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 440
            },
            new NewOutletFields
            {
                DisplayName = "Asset Type",
                Field = NewOutletFieldsForApp.AssetType,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 450
            },
            new NewOutletFields
            {
                DisplayName = "Asset Size",
                Field = NewOutletFieldsForApp.AssetSize,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 460
            },
            new NewOutletFields
            {
                DisplayName = "Asset Description",
                Field = NewOutletFieldsForApp.AssetDescription,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 470
            },
            new NewOutletFields
            {
                DisplayName = "Outlet Potential",
                Field = NewOutletFieldsForApp.OutletPotential,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 480
            },
            new NewOutletFields
            {
                DisplayName = "Franchise",
                Field = NewOutletFieldsForApp.Franchise,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 490
            },
            new NewOutletFields
            {
                DisplayName = "Margin Slab",
                Field = NewOutletFieldsForApp.EntityMarginSlabId,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 500
            },
            new NewOutletFields
            {
                DisplayName = "IsFocused",
                Field = NewOutletFieldsForApp.IsFocussed,
                IsMandatory = false,
                IsVisible = true,
                DisplayOrder = 510
            },
            new NewOutletFields
            {
                DisplayName = "Latitude",
                Field = NewOutletFieldsForApp.Latitude,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 520
            },
            new NewOutletFields
            {
                DisplayName = "Longitude",
                Field = NewOutletFieldsForApp.Longitude,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 530
            },
            new NewOutletFields
            {
                DisplayName = "FSSAINumber",
                Field = NewOutletFieldsForApp.FSSAINumber,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 540
            },
            new NewOutletFields
            {
                DisplayName = "FSSAIExpiryDate",
                Field = NewOutletFieldsForApp.FSSAIExpiryDate,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 550
            },
            new NewOutletFields
            {
                DisplayName = "ISR Availability",
                Field = NewOutletFieldsForApp.ISRAvailability,
                IsMandatory = false,
                IsVisible = false,
                DisplayOrder = 560
            }
        };
}

public class NewOutletFields
{
    public string DisplayName { get; set; }
    public int DisplayOrder { get; set; }
    public NewOutletFieldsForApp Field { set; get; }
    public bool IsMandatory { set; get; }
    public bool IsVisible { set; get; }
}
