﻿using System.Collections.Generic;
using Libraries.CommonEnums;

namespace Libraries.PerspectiveColumns.Interface;

public interface IPerspective
{
    List<PerspectiveColumnModel> Columns { get; set; }
}

public class PerspectiveBuilder
{
    public static List<ViewPerspective> PositionCompatiblePerspectives = new()
    {
        ViewPerspective.ProductWiseSales,
        ViewPerspective.EmpGeoPerformanceData,
        ViewPerspective.DayStart,
        ViewPerspective.NoSalesReason,
        ViewPerspective.TrendReportNLT,
        ViewPerspective.TrendReportNLTPosition,
        ViewPerspective.TrendReportLT,
        ViewPerspective.TrendReportLTPosition,
        ViewPerspective.SecondaryDemandVsSales,
        ViewPerspective.ProductDemandVsSales,
        ViewPerspective.DistributorProductPerformance
    };

    public static List<ViewPerspective> HistoricalPerspectives = new()
    {
        ViewPerspective.EmployeePerformance,
        ViewPerspective.HistoricalSalesData,
        ViewPerspective.SecondaryDemandVsSales,
        ViewPerspective.ProductDemandVsSales,
        ViewPerspective.DistributorPerformance,
        ViewPerspective.DistributorProductPerformance
    };

    public static List<ChartType> ChartsWithColorCoding = new()
    {
        ChartType.FlatTable,
        ChartType.FullPageTableLT,
        ChartType.FullPageTableNLT,
        ChartType.GroupedNoLimit,
        ChartType.OneNumber,
        ChartType.TwoNumber,
        ChartType.Bar
    };

    public static List<ViewPerspective> PerspectivesWithColorColorCoding = new()
    {
        ViewPerspective.EmpGeoPerformanceData,
        ViewPerspective.ProductWiseSales,
        ViewPerspective.NoSalesReason,
        ViewPerspective.TrendReportNLT,
        ViewPerspective.TrendReportLT,
        ViewPerspective.MTStockAndSales
    };

    public static List<ViewPerspective> PerspectivesWithDrillDown = new() { ViewPerspective.EmpGeoPerformanceData, ViewPerspective.ProductWiseSales, ViewPerspective.NoSalesReason, ViewPerspective.MTStockAndSales };
    public static List<ViewPerspective> PerspectivesWithProdDrillDown = new() { ViewPerspective.ProductWiseSales };

    public static List<ViewPerspective> ViewPerspectivesImplementedDerivedMeasure = new()
    {
        ViewPerspective.EmpGeoPerformanceData,
        ViewPerspective.NoSalesReason,
        ViewPerspective.ProductWiseSales,
        ViewPerspective.SecondaryDemandVsSales,
        ViewPerspective.TrendReportLT,
        ViewPerspective.TrendReportLTPosition,
        ViewPerspective.TrendReportNLT,
        ViewPerspective.TrendReportNLTPosition,
        ViewPerspective.DistributorPerformance,
        ViewPerspective.ProductDemandVsSales,
        ViewPerspective.DistributorProductPerformance,
        ViewPerspective.MTOutofStock,
        ViewPerspective.MTShelfShare,
        ViewPerspective.DayStart
    };

    public static IPerspective GetPerspectiveFor(ViewPerspective viewPerspective, long companyId, Dictionary<string, string> nomenclatureDict, string perspectiveColumns = null)
    {
        switch (viewPerspective)
        {
            case ViewPerspective.ProductWiseSales:
                return new ProductSalePerspective(nomenclatureDict);

            case ViewPerspective.DayStart:
                return new DayStartPerspective(nomenclatureDict);

            case ViewPerspective.NoSalesReason:
                return new NoSalesReasonPerspective(nomenclatureDict);

            case ViewPerspective.LiveSalesData:
                return new LiveDataSalesPerspective(nomenclatureDict);

            case ViewPerspective.MasterData:
                return new MasterDataPerspective(nomenclatureDict);

            case ViewPerspective.MTStockAndSales:
                return new MTProductSalePerspective(nomenclatureDict);

            case ViewPerspective.MTShelfShare:
                return new MTShelfSharePerspective(nomenclatureDict);

            case ViewPerspective.MTOutofStock:
                return new MTOutofStockPerspective(nomenclatureDict);

            case ViewPerspective.HistoricalSalesData:
                return new HistoricalSalesDataPerspective(nomenclatureDict);

            case ViewPerspective.TrendReportNLT:
                return new TrendReportNLTPerspective(nomenclatureDict);

            case ViewPerspective.TrendReportLT:
                return new TrendReportLTPerspective(nomenclatureDict);

            case ViewPerspective.EmpGeoPerformanceData:
                return new EmpGeoPerformanceModel(nomenclatureDict);

            case ViewPerspective.SecondaryDemandVsSales:
                return new SecondaryDemandVsSalesPerspective(nomenclatureDict);

            case ViewPerspective.ProductDemandVsSales:
                return new ProductDemandVsSalesPerspective(nomenclatureDict, perspectiveColumns);

            case ViewPerspective.DistributorPerformance:
                return new DistributorPerformancePerspective(nomenclatureDict);

            case ViewPerspective.DistributorProductPerformance:
                return new DistributorProductPerformancePerspective(nomenclatureDict);

            case ViewPerspective.LiveSalesDataPosition:
                return new LiveDataSalesByPositionPerspective(nomenclatureDict);

            case ViewPerspective.FlexibleTargetVsAchievement:
                return new FlexibleTargetVsAchievementPerspective(nomenclatureDict);

            case ViewPerspective.DistributorStockSale:
                return new DistributorStockSale(nomenclatureDict, perspectiveColumns);
            default:
                return new ProductSalePerspective(nomenclatureDict);
        }
    }
}
