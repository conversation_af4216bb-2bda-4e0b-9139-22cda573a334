﻿using System.Data.SqlClient;
using System.Text.RegularExpressions;
using FAEngage.Core.MasterRepositories;
using FAEngage.DbStorage.DbContexts;

namespace FAEngage.DbStorage.MasterRepositories;

public class MasterKpiAchievementRepository(MasterDbSqlDataReader masterDbSqlDataReader)
    : IMasterKpiAchievementRepository
{
    public async Task<string> GetKPIAchievedTarget(string query, long companyId, long esmId, DateTime startDate,
        DateTime endDate, long monthnumber)
    {
        if (string.IsNullOrWhiteSpace(query)) return null;

        var parameters = new List<SqlParameter>();
        var paramsInQuery = Regex.Matches(query, @"@\b\S+?\b");

        foreach (Match p in paramsInQuery)
        {
            string value = null;
            switch (p.Value.ToLower())
            {
                case "@companyid":
                    value = companyId.ToString();
                    break;
                case "@esmid":
                    value = esmId.ToString();
                    break;
                case "@startdatekey":
                    value = startDate.ToString("yyyyMMdd");
                    break;
                case "@enddatekey":
                    value = endDate.ToString("yyyyMMdd");
                    break;
                case "@monthnumber":
                    value = monthnumber.ToString();
                    break;
            }

            if (value != null) parameters.Add(new SqlParameter(p.Value.ToLower(), value));
        }

        return await masterDbSqlDataReader.GetSingleResultOfQuery(query, parameters);
    }
}