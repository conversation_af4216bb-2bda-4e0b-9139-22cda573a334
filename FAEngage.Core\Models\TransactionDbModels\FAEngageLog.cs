﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using Libraries.CommonModels;

namespace FAEngage.Core.Models.TransactionDbModels;

public class FAEngageLog
{
    public long Id { get; set; }

    public long CompanyId { get; set; }

    public long NotificationId { get; set; }

    public long UserId { get; set; }

    public DateTimeOffset SendTime { get; set; }

    public Guid NotificationTransactionId { get; set; }

    public DateTime CreatedAt { get; set; }

    public bool IsReadByUser { get; set; }

    public DateTimeOffset? ReadByUserTime { get; set; }
    public bool IsSentFromBackend { get; set; }
    public string AggregationHierarchyJson { get; set; }

    public string SentMessage { get; set; }
    public Guid SentId { get; set; }

    public bool IsFcmSent { get; set; }

    [NotMapped]
    public List<EntityUserMinForEngage> AggregationHierarchy => JsonSerializer.Deserialize<List<EntityUserMinForEngage>>(AggregationHierarchyJson);
}