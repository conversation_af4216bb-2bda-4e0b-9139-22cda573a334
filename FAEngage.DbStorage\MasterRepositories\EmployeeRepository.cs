﻿using FAEngage.Core.MasterRepositories;
using FAEngage.Core.Models;
using FAEngage.Core.Models.MasterDbModels;
using FAEngage.DbStorage.DbContexts;
using Libraries.CommonEnums;
using Library.Infrastructure.Interface;
using Microsoft.EntityFrameworkCore;

namespace FAEngage.DbStorage.MasterRepositories;

public class EmployeeRepository(MasterDbContext db) : IEmployeeRepository
{
    private IQueryable<ClientEmployee> GetFieldUsersQueryable(long companyId, bool isIncludeDeactiveUsers = false)
    {
        return GetEmployeeQueryable(companyId, isIncludeDeactiveUsers)
            .Where(e => e.IsFieldAppuser)
            .OrderByDescending(e => e.Id);
    }

    private IQueryable<ClientEmployee> GetEmployeeQueryable(long companyId, bool isIncludeDeactiveUsers = false)
    {
        return isIncludeDeactiveUsers
            ? db.Employee.Where(e => e.Company == companyId && !e.IsTrainingUser)
            : db.Employee.Where(e => e.Company == companyId && !e.IsDeactive && !e.IsTrainingUser);
    }

    private IQueryable<ClientEmployee> GetEmployees(long companyId, bool isIncludeDeactiveUsers = false)
    {
        return isIncludeDeactiveUsers
            ? db.Employee.Where(e => e.Company == companyId && !e.IsTrainingUser)
            : db.Employee.Where(e => e.Company == companyId && !e.IsDeactive && !e.IsTrainingUser);
    }

    private IQueryable<ClientEmployee> GetFieldUserUnderRoleQueryable(long companyId, List<long> userIds,
        PortalUserRole portalUserRole, bool includeInactive = false)
    {
        return GetFieldUsersQueryable(companyId, includeInactive).Where(u =>
            (userIds.Contains(u.Parent.Parent.Parent.Parent.Parent.Id) &&
             u.Parent.Parent.Parent.Parent.Parent.UserRole == portalUserRole)
            || (userIds.Contains(u.Parent.Parent.Parent.Parent.Id) &&
                u.Parent.Parent.Parent.Parent.UserRole == portalUserRole)
            || (userIds.Contains(u.Parent.Parent.Parent.Id) && u.Parent.Parent.Parent.UserRole == portalUserRole)
            || (userIds.Contains(u.Parent.Parent.Id) && u.Parent.Parent.UserRole == portalUserRole)
            || (userIds.Contains(u.Parent.Id) && u.Parent.UserRole == portalUserRole)
            || (userIds.Contains(u.Id) && u.UserRole == portalUserRole)
            || (userIds.Contains(u.Id) && PortalUserRole.ClientEmployee == portalUserRole)
            || portalUserRole == PortalUserRole.CompanyAdmin || portalUserRole == PortalUserRole.CompanyExecutive ||
            portalUserRole == PortalUserRole.AccountManager || portalUserRole == PortalUserRole.GlobalAdmin);
    }

    private async Task<IEnumerable<ClientEmployee>> GetFieldUserUnderRoleQueryable(long companyId,
        long userId, PortalUserRole portalUserRole, bool isInclueDeactiveUsers = false)
    {
        var employees = await GetEmployees(companyId, isIncludeDeactiveUsers: true).ToListAsync();
        var employeesDict = employees.ToDictionary(e => e.Id, e => e);
        var fieldUsers = employees
            .Where(e => e.IsFieldAppuser && (isInclueDeactiveUsers || !e.IsDeactive))
            .ToList();
        fieldUsers = JoinParents(fieldUsers, employeesDict);

        return fieldUsers.Where(u =>
            (u.Parent.Parent.Parent.Parent.Parent.Id == userId &&
             u.Parent.Parent.Parent.Parent.Parent.UserRole == portalUserRole)
            || (u.Parent.Parent.Parent.Parent.Id == userId &&
                u.Parent.Parent.Parent.Parent.UserRole == portalUserRole)
            || (u.Parent.Parent.Parent.Id == userId && u.Parent.Parent.Parent.UserRole == portalUserRole)
            || (u.Parent.Parent.Id == userId && u.Parent.Parent.UserRole == portalUserRole)
            || (u.Parent.Id == userId && u.Parent.UserRole == portalUserRole)
            || (u.Id == userId && u.UserRole == portalUserRole)
            || portalUserRole == PortalUserRole.CompanyAdmin || portalUserRole == PortalUserRole.CompanyExecutive ||
            portalUserRole == PortalUserRole.AccountManager || portalUserRole == PortalUserRole.GlobalAdmin);
    }

    private List<ClientEmployee> JoinParents(List<ClientEmployee> fieldUsers, Dictionary<long, ClientEmployee> ceDict)
    {
        foreach (var fu in fieldUsers)
        {
            fu.Parent = ceDict.TryGetValue(fu.ParentId ?? 0, out var p)
                ? p.ShallowCopy()
                : new ClientEmployee(); // fu.Parent;
        }

        foreach (var fu in fieldUsers)
        {
            fu.Parent.Parent = ceDict.TryGetValue(fu.Parent.ParentId ?? 0, out var p)
                ? p.ShallowCopy()
                : new ClientEmployee(); // fu.Parent.Parent;
        }

        foreach (var fu in fieldUsers)
        {
            fu.Parent.Parent.Parent = ceDict.TryGetValue(fu.Parent.Parent.ParentId ?? 0, out var p)
                ? p.ShallowCopy()
                : new ClientEmployee(); // fu.Parent.Parent.Parent;
        }

        foreach (var fu in fieldUsers)
        {
            fu.Parent.Parent.Parent.Parent = ceDict.TryGetValue(fu.Parent.Parent.Parent.ParentId ?? 0, out var p)
                ? p.ShallowCopy()
                : new ClientEmployee(); // fu.Parent.Parent.Parent.Parent;
        }

        foreach (var fu in fieldUsers)
        {
            fu.Parent.Parent.Parent.Parent.Parent =
                ceDict.TryGetValue(fu.Parent.Parent.Parent.Parent.ParentId ?? 0, out var p)
                    ? p.ShallowCopy()
                    : new ClientEmployee(); // fu.Parent.Parent.Parent.Parent.Parent;
        }

        return fieldUsers;
    }

    private IQueryable<ClientEmployee> GetFieldUserUnderRoleQueryable_Old(long companyId, long oldTableId,
        PortalUserRole portalUserRole, bool isIncludeDeactiveUsers = false)
    {
        return GetFieldUsersQueryable(companyId, isIncludeDeactiveUsers).Where(u =>
            (u.Parent.Parent.Parent.Parent.Parent.OldTableId == oldTableId &&
             u.Parent.Parent.Parent.Parent.Parent.UserRole == portalUserRole)
            || (u.Parent.Parent.Parent.Parent.OldTableId == oldTableId &&
                u.Parent.Parent.Parent.Parent.UserRole == portalUserRole)
            || (u.Parent.Parent.Parent.OldTableId == oldTableId &&
                u.Parent.Parent.Parent.UserRole == portalUserRole)
            || (u.Parent.Parent.OldTableId == oldTableId && u.Parent.Parent.UserRole == portalUserRole)
            || (u.Parent.OldTableId == oldTableId && u.Parent.UserRole == portalUserRole)
            || (u.OldTableId == oldTableId && u.UserRole == portalUserRole)
            || (u.Id == oldTableId && portalUserRole == PortalUserRole.ClientEmployee)
            || portalUserRole == PortalUserRole.CompanyAdmin || portalUserRole == PortalUserRole.CompanyExecutive ||
            portalUserRole == PortalUserRole.AccountManager || portalUserRole == PortalUserRole.GlobalAdmin);
    }

    public async Task<List<Employee>> GetAllEmployeesSRAndDSR(long companyId)
    {
        return await db.Employee
            .Where(e => e.Company == companyId
                        && !e.IsDeactive && e.IsFieldAppuser
                        && (e.EmployeeType == EmployeeType.SR || e.EmployeeType == EmployeeType.DSR || e.EmployeeType == EmployeeType.JSR))
            .Select(e => new Employee
            {
                Id = e.Id,
                OldTableId = e.OldTableId ?? 0,
                GuId = e.GUID,
                Name = e.Name,
                LocalName = e.LocalName,
                ErpId = e.ClientSideId,
                Rank = e.Rank,
                Region = e.Region.Name,
                Zone = e.Region.Zone.Name,
                //Date: 2023-01-02; Asana: https://app.asana.com/0/****************/****************/f;
                //Cahnge: Map Reporting Manager To Parent Name Its ERPId to a new Field to avoid further Confusion, The Function Usage was also updated Accordingly
                ReportingManager = e.ParentId != null ? e.Parent.Name : string.Empty,
                ReportingManagerErpId = e.ParentId != null ? e.Parent.ClientSideId : string.Empty,
                DesignationId = e.DesignationId,
                EmployeeType = e.EmployeeType
            }).ToListAsync();
    }

    public async Task<List<Employee>> GetAllEmployees(long companyId, List<long> userIds,
        bool isIncludeDeactiveUsers = false)
    {
        if (isIncludeDeactiveUsers)
        {
            return await db.Employee
                .Where(e => e.Company == companyId
                            && userIds.Contains(e.Id))
                .Select(e => new Employee
                {
                    Id = e.Id,
                    Name = e.Name,
                    UserRole = e.UserRole,
                    OldTableId = e.OldTableId.HasValue ? e.OldTableId.Value : 0,
                    EmailId = e.EmailId,
                    ErpId = e.ClientSideId,
                    EmployeeAttributeText1 = e.EmployeeAttributeText1,
                    EmployeeAttributeText2 = e.EmployeeAttributeText2,
                    EmployeeAttributeNumber1 = e.EmployeeAttributeNumber1,
                    EmployeeAttributeBoolean1 = e.EmployeeAttributeBoolean1,
                    EmployeeAttributeDate = e.EmployeeAttributeDate
                }).ToListAsync();
        }
        else
        {
            return await db.Employee
                .Where(e => e.Company == companyId
                            && !e.IsDeactive && userIds.Contains(e.Id))
                .Select(e => new Employee
                {
                    Id = e.Id,
                    Name = e.Name,
                    UserRole = e.UserRole,
                    OldTableId = e.OldTableId.HasValue ? e.OldTableId.Value : 0,
                    EmailId = e.EmailId,
                    ErpId = e.ClientSideId,
                    EmployeeAttributeText1 = e.EmployeeAttributeText1,
                    EmployeeAttributeText2 = e.EmployeeAttributeText2,
                    EmployeeAttributeNumber1 = e.EmployeeAttributeNumber1,
                    EmployeeAttributeBoolean1 = e.EmployeeAttributeBoolean1,
                    EmployeeAttributeDate = e.EmployeeAttributeDate
                }).ToListAsync();
        }
    }

    public async Task<string?> GetManagerToken(long userId, long companyId)
    {
        return await db.FAManagersTokens
            .Where(m => m.UserId == userId
                        && m.CompanyId == companyId
                        && m.LoginIdentifier == ManagerAppLoginIdentifier.GT)
            .Select(s => s.FCMToken)
            .FirstOrDefaultAsync();
    }

    public async Task<string?> GetFieldUserToken(long userId, long companyId)
    {
        return await db.Devices
            .Where(s => s.EmployeeId == userId
                                           && s.CompanyID == companyId)
            .OrderByDescending(s => s.LastSeenAt)
            .Select(s => s.GcmId)
            .FirstOrDefaultAsync();
    }

    public async Task<DeviceEntityMin?> GetFieldUserAlertTypeAsync(long userId, long companyId)
    {
        return await db.Devices.Where(s => s.CompanyID == companyId && s.EmployeeId == userId)
            .OrderByDescending(s => s.LastSeenAt)
            .Select(s => new DeviceEntityMin
            {
                GcmId = s.GcmId,
                AppVersionNumber = s.AppVersionNumber,
                AlertSource = s.AlertSource ?? AlertSource.GTPlayStore

            }).FirstOrDefaultAsync();
    }

    public async Task<Employee?> GetActiveEmployeeById(long id)
    {
        return await db.Employee
            .Where(e => e.Id == id && !e.IsDeactive)
            .Select(e => new Employee
            {
                Id = e.Id,
                Name = e.Name,
                ContactNo = e.ContactNo,
                CompanyId = e.Company,
                EmailId = e.EmailId,
                ErpId = e.ClientSideId,
                ReportingManager = e.Parent != null ? e.Parent.Name : string.Empty,
                DesignationId = e.DesignationId,
                AreaSalesManagerId = e.AreaSalesManagerId,
                RegionId = e.RegionId,
                OldTableId = e.OldTableId ?? e.Id,
                UserRole = e.UserRole
            }).FirstOrDefaultAsync();
    }

    public async Task<UserWithManagers?> GetEmployeeWithHierarchyWithNewId(long companyId, long empId)
    {
        return await GetFieldUsersQueryable(companyId)
            .Where(s => s.Id == empId)
            .Select(e => new UserWithManagers
            {
                ParentName = e.Parent.Name,
                ParentId = e.Parent.Id,
                Parent2Name = e.Parent.Parent.Name,
                Parent2Id = e.Parent.Parent.Id,
                Parent3Name = e.Parent.Parent.Parent.Name,
                Parent3Id = e.Parent.Parent.Parent.Id,
                Parent4Id = e.Parent.Parent.Parent.Parent.Id,
                Parent4Name = e.Parent.Parent.Parent.Parent.Name,
                Parent5Name = e.Parent.Parent.Parent.Parent.Parent.Name,
                Parent5Id = e.Parent.Parent.Parent.Parent.Parent.Id,
                Id = e.Id,
                ContactNo = e.ContactNo,
                Rank = e.Rank,
                UserRole = e.UserRole,
                Name = e.Name,
                OldTableId = e.OldTableId,
                IsDeactive = e.IsDeactive,
                CompanyId = e.Company,
                ErpId = e.ClientSideId,
                IsOrderBookingDisabled = e.IsOrderBookingDisabled,
                LocalName = e.LocalName
            }).FirstOrDefaultAsync();
    }

    public async Task<List<Employee>> GetAllEmployees(long companyId)
    {
        return await db.Employee
            .Where(e => e.Company == companyId
                        && !e.IsDeactive)
            .Select(e => new Employee
            {
                Id = e.Id,
                OldTableId = e.OldTableId ?? 0,
                GuId = e.GUID,
                Name = e.Name,
                LocalName = e.LocalName,
                ErpId = e.ClientSideId,
                Rank = e.Rank,
                Region = e.Region.Name,
                Zone = e.Region.Zone.Name,
                //Date: 2023-01-02; Asana: https://app.asana.com/0/****************/****************/f;
                //Cahnge: Map Reporting Manager To Parent Name Its ERPId to a new Field to avoid further Confusion, The Function Usage was also updated Accordingly
                ReportingManager = e.ParentId != null ? e.Parent.Name : string.Empty,
                ReportingManagerErpId = e.ParentId != null ? e.Parent.ClientSideId : string.Empty,
                DesignationId = e.DesignationId
            }).ToListAsync();
    }

    public async Task<List<Employee>> GetFieldUserIdsUnderManagerModel(long companyId, PortalUserRole userRole, List<long> userIds,
        EmployeeType? userType = null)
    {
        return await GetFieldUserUnderRoleQueryable(companyId, userIds, userRole)
            .Where(u => (userType.HasValue && u.EmployeeType == userType)
                        || !userType.HasValue).Select(e =>
                new Employee
                {
                    Id = e.Id,
                    Rank = e.Rank
                }).ToListAsync();
    }
}