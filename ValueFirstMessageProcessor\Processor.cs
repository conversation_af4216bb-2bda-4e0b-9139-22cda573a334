﻿using FAEngage.Core.Models;
using FAEngage.Core.Models.TransactionDbModels;
using FAEngage.DbStorage.DbContexts;
using Flurl.Http;
using Microsoft.Azure.WebJobs;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace ValueFirstMessageProcessor
{
    public class Processor
    {
        private readonly TransactionDbContext _transactionDbContext; 
        private readonly WritableTransactionDbContext _writableTransactionDbContext; 
        private readonly ILogger<Processor> _logger;
        private const long CompanyId = 172305;
        private const string ApiUrl = "https://crm.livguard.com/LivCRM/index.php?entryPoint=sso";
        private const string AuthHeader = "RkE6UGFzc3dvcmRAMTIz";

        public Processor(
            TransactionDbContext transactionDbContext,
            WritableTransactionDbContext writableTransactionDbContext,
            ILogger<Processor> logger)
        {
            _transactionDbContext = transactionDbContext;
            _writableTransactionDbContext = writableTransactionDbContext;
            _logger = logger;
        }

        public async Task ProcessAsync()
        {
            _logger.LogInformation("Starting WhatsApp log processing...");

            try
            {
                var startTime = DateTime.UtcNow.Date;
                //var startTime = new DateTime(2025, 03, 01); // hardcode date to fetch unprocessed logs
                var unprocessedLogs = await _transactionDbContext.FAEngageLogWhatsapps
                    .Where(log => log.CompanyId == CompanyId && !log.IsValueFirstProcessed)
                    .ToListAsync();

                if (unprocessedLogs.Count == 0)
                {
                    _logger.LogInformation("No unprocessed logs found.");
                    return;
                }

                bool isUpdated = false;

                foreach (var log in unprocessedLogs)
                {
                    var guid = GetCallbackGuid(log);
                    if (string.IsNullOrEmpty(guid)) continue;

                    var result = await SendFlurlRequestAsync(guid);
                    if (result != null)
                    {
                        var logToUpdate = await _writableTransactionDbContext.FAEngageLogWhatsapps
                            .FirstOrDefaultAsync(x => x.Id == log.Id);

                        if (logToUpdate != null)
                        {
                            var sentMessage = new SentMessage { Description = result.Description, ReasonCode = result.ReasonCode };
                            logToUpdate.IsReadByUser = result.ReasonCode == "0" ? true : false;
                            logToUpdate.SentMessage = JsonSerializer.Serialize(sentMessage);

                            if (DateTimeOffset.TryParse(result.MessageDate, out var messageDate))
                            {
                                logToUpdate.ReadByUserTime = result.ReasonCode == "0" ? messageDate : null;
                                logToUpdate.IsValueFirstProcessed = true;
                                isUpdated = true;
                            }
                        }
                    }
                }

                if (isUpdated)
                {
                    await _writableTransactionDbContext.SaveChangesAsync();
                    _logger.LogInformation("Database updated successfully.");
                }

                var endTime = DateTime.UtcNow;
                _logger.LogInformation("Processing completed in {Time} ms.", (endTime - startTime).TotalMilliseconds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "WhatsApp message processing failed.");
                throw;
            }
        }

        private async Task<SuccessResponseData?> SendFlurlRequestAsync(string callbackGuid)
        {
            try
            {
                var response = await ApiUrl
                    .WithHeader("Authorization", AuthHeader)
                    .WithHeader("Content-Type", "application/json")
                    .PostJsonAsync(new
                    {
                        jobType = "whatapp_dlr",
                        source = "fa",
                        client_guid = callbackGuid
                    });

                var responseContent = await response.GetStringAsync();
                var dict = JsonSerializer.Deserialize<Dictionary<string, object>>(responseContent);

                if (dict == null) return null;

                if (dict.ContainsKey("code") && dict["code"].ToString() == "200")
                {
                    if (dict.TryGetValue("data", out object? value))
                    {
                        var returnResult = value != null ? 
                            JsonSerializer.Deserialize<List<SuccessResponseData>>(value.ToString())?.FirstOrDefault() 
                            : null;
                        return returnResult;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send Flurl request for GUID: {Guid}", callbackGuid);
            }

            return null;
        }

        private static string? GetCallbackGuid(FAEngageLogWhatsapp log)
        {
            try
            {
                var objectJson = JsonSerializer.Deserialize<WhatsAppResponseSARGroup>(log.SentMessage);
                return objectJson?.MESSAGEACK?.GUID?.GUID;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deserializing SentMessage: {ex.Message}");
                return null;
            }
        }
    }

    public class ApiResponse
    {
        [JsonPropertyName("status")]
        public required string Status { get; set; }
        [JsonPropertyName("code")]
        public required int Code { get; set; }
        [JsonPropertyName("message")]
        public required string Message { get; set; }
        [JsonPropertyName("data")]
        public required string Data { get; set; }
    }

    public class SuccessResponseData
    {
        [JsonPropertyName("to")]
        public required string To { get; set; }
        [JsonPropertyName("from")]
        public required string From { get; set; }
        [JsonPropertyName("message_status")]
        public required string MessageStatus { get; set; }
        [JsonPropertyName("message_date")]
        public required string MessageDate { get; set; }
        [JsonPropertyName("reason_code")]
        public required string ReasonCode { get; set; }
        [JsonPropertyName("description")]
        public required string Description { get; set; }
    }

    public class SentMessage
    {
        public required string ReasonCode { get; set; }
        public required string Description { get; set; }
    }
}
