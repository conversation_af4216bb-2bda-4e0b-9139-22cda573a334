﻿using System;

namespace Libraries.CommonEnums;

//Use enum DataTypeEnum
[Obsolete]
public enum OutletMetricDataType
{
    Decimal = 1,
    WholeNumber = 2,
    Date = 3,
    String = 4,
    Boolean = 5,
    Percentage = 6,
    Array = 7,
    Currency = 8,
    Time = 9
}

public enum OutletMetricQueryRelation
{
    OnlyReportQuery = 1,
    ReportQueryDividedbyMasterQuery = 2,
    OnlyMasterQuery = 3,
    OnlyTransactionQuery = 4,
    TransactionQueryDividedByMasterQuery = 5,
    OnlyClickhouseQuery = 6,
    HCCBDatabaseOnly = 7,
    HCCBDBDividedbyMasterDB = 8,
    HCCBDBDividedbyReportsDB = 9,
    ClickhouseDBOnly = 10,
    ClickhouseDBDividedbyMasterDB = 11
}

//Use enum Frequency
[Obsolete]
public enum OutletMetricFrequency
{
    Daily = 1,
    Week = 2,
    LastWeek = 3,
    CurrentMonth = 4,
    LastMonth = 5,
    LastMonthTillDate = 6,
    LastNDays = 7,
    YearTillDate = 8
}

public enum OutletMetricCurrentDayOfWeek
{
    Sunday = 0,
    Monday = 1,
    Tuesday = 2,
    Wednesday = 3,
    Thursday = 4,
    Friday = 5,
    Saturday = 6
}
